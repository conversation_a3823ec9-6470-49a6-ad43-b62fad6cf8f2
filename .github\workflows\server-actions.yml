name: GitHub Server Actions 
run-name: ${{ github.actor }} is deploying server  
on:
  push:
    branches:
      - main
env:
  SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
  SUPABASE_DB_PASSWORD: ${{ secrets.SUPABASE_DB_PASSWORD }}
  PROJECT_ID: ${{ secrets.SUPABASE_PROJECT }} 
jobs:
  deploy-supabase-functions:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: supabase/setup-cli@v1
        with:
          version: latest
      - run: supabase init --force
        working-directory: ./server
      - run: supabase link --project-ref $PROJECT_ID
        working-directory: ./server
      - run: supabase functions deploy stripe-payment --no-verify-jwt
        working-directory: ./server
      - run: supabase functions deploy nylas-webhook --no-verify-jwt
        working-directory: ./server
      - run: supabase functions deploy job-created
        working-directory: ./server
      - run: supabase functions deploy customer-created
        working-directory: ./server
      - run: supabase functions deploy customer-job-created
        working-directory: ./server
  deploy-supabase-migration:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: supabase/setup-cli@v1
        with:
          version: latest
      - run: supabase init --force
        working-directory: ./server
      - run: supabase link --project-ref $PROJECT_ID
        working-directory: ./server
      - run: supabase db push
        working-directory: ./server
