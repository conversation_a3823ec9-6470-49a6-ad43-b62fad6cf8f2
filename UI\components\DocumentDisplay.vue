<template>
  <table class="table-fixed">
    <tbody>
      <tr>
        <td class="w-40 ">
          <USelect
            v-model="selectedId"
            :options="options"
            value-attribute="id"
            option-attribute="name"
            class="w-40 truncate"
            @change="emit('change', selectedId)"
             />
        </td>
        <td class="w-10">
          <div>
            <DocumentDownload :document="selectedDoc" />
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</template>

<script lang="ts" setup>
import type { Document } from '~/common/common';

const props = defineProps<{selectedDocId: string|undefined}>();
const options = defineModel<Document[]>('options', {default: []})
const emit = defineEmits(['change'])
const selectedDoc = ref<Document|undefined>()
const selectedId = ref(props.selectedDocId);

watch(
  selectedId, 
  (newVal:string|undefined) => { 
    console.log('selectedDocId changed', newVal)
    selectedDoc.value = getDoc(newVal) }
)

watch(
  options.value,
  (newVal) => {
    console.log("updating options", newVal);
  }
)

function getDoc(docId: string|undefined): Document|undefined {
  return options.value.find((doc) => doc.id === docId)
}

onMounted(() => {
  console.log('selectedDocId', selectedId.value);
  selectedDoc.value = getDoc(selectedId.value);
})

console.log("Doc DIsplay for", selectedDoc.value);

</script>