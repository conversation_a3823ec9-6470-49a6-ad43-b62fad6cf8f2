<template>
  <div class="data-entry-container px-8 min-h-screen max-w-7xl mx-auto">
    <h1 class="text-2xl sm:text-4xl font-bold mb-3 text-center text-orange-700">
      Our Plans
    </h1>
    <p class="text-lg sm:text-xl text-center mb-12 text-orange-600">
      Choose the perfect plan to get to your next job:
    </p>

    <div class="flex flex-wrap justify-center items-center gap-8">
      <ULink
        v-for="(item, index) in paymentCardsData"
        :key="index"
        :to="loggedIn ? item.link : '#'"
        :disabled="!loggedIn"
        target="_blank"
        class="product-button button-bg-gradient text-white px-8 py-6 rounded-lg shadow-lg hover:bg-orange-600 transition transform hover:scale-105 text-center"
      >
        <span class="text-xl sm:text-2xl font-bold">{{ item.title }}</span>
        <span class="text-lg pt-5">{{ item.price }}</span>
    </ULink>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { formatUsdPenniesToDollars } from "~/common/common";
import type { Database } from "~/lib/applysquad-common/database.types";
const email = ref("");
const supabase = useSupabaseClient<Database>();
const user = useSupabaseUser();
const loggedIn = ref(user.value ? true : false);
email.value = user?.value?.email ?? "";


const { data, error } = await supabase
  .from("products")
  .select("*")
  .eq("status", "ACTIVE");

if (error) {
  throw error;
}

const paymentCardsData = ref(
  data.map((product) => ({
    link: `${product.payment_link}?prefilled_email=${email.value}`,
    title: product.label,
    price: formatUsdPenniesToDollars(product.price_usd_pennies),
  }))
);
</script>

<style >
.data-entry-container {
  display: flex;
  flex-direction: column;
  /* justify-content: center; */
  align-items: center;
  padding-top: 30px;
}

.product-button {
  width: 300px;
  height: 200px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>
