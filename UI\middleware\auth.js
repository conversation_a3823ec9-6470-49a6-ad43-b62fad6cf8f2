import { isAdmin, isCustomer, isAgent } from "~/common/common"

export default defineNuxtRouteMiddleware(async(to, from) => {
    
    const user = useSupabaseUser()
    const navToCustomer = to.path.indexOf('customer') > 0
    const navToAdmin = to.path.indexOf('admin') > 0
    const navToAgent = to.path.indexOf('agent') > 0
    
    if (!user.value) {
        console.warn('Unauthorized access. Redirecting to login...')
        return navigateTo('/login')
    
    } else if (navToAdmin && !(await isAdmin(user.value, useSupabaseClient()))) {
        console.warn(`Unauthorized access to admin by user ${user.value.email}. Redirecting to home...`)
        return navigateTo('/')
    
    } else if (navToCustomer && !(isCustomer(user.value, useSupabaseClient()))) {
        console.warn(`Unauthorized access to customer by user ${user.value.email}. Redirecting to home...`)
        return navigateTo('/')
    
    }  else if (navToAgent && !(isAgent(user.value, useSupabaseClient()))) {
        console.warn(`Unauthorized access to agent by user ${user.value.email}. Redirecting to home...`)
        return navigateTo('/')
    }
})