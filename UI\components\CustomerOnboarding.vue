<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import type { Customer, Document } from "~/common/common";
import { useSupabaseClient } from "#imports";
import type { Database } from "~/lib/applysquad-common/database.types";
import { format } from "date-fns";
import {
  languages,
  jobType,
  DocumentType,
  enumFromKey,
  workSpace,
  genders,
  seniorityLevels,
  companyTypes,
  regions,
  functionalPreferences,
  countries,
  industries,
} from "../common/common";

// Define the structure of a location
interface Location {
  id?: number;
  location?: string;
  workAuthorized?: boolean;
  payRequirement?: string;
}

const props = defineProps<{
  customer: Customer;
  isCustomer: boolean;
  documentDeleted: boolean;
}>();

const emit = defineEmits<{
  (e: "documentDeleted"): void;
  (e: "getCustomerDocuments"): void;
}>();

const supabase = useSupabaseClient<Database>();
type JobType = Database["public"]["Enums"]["job_type"];
const toast = useToast();
const isEditing = ref(false);
const documents = ref<Document[]>([]);
const docTypes = [DocumentType.CV, DocumentType.COVER];
const existingOnboardingId = ref<number | null>(null);
const customerCVs = ref<Document[]>([]);
const customerCoverLetters = ref<Document[]>([]);

// Initialize empty arrays for selected values
const selectedLanguagesFluent = ref<any>([]);
const selectedLanguagesLearning = ref<any>([]);
const languagesRef = ref<string[]>(languages);
const selectedRegions = ref<any>([]);
const selectedFunctionalPreferences = ref<any>([]);
const selectedCompanySize = ref<any>([]);
const locations = ref<Location[]>([]);
const isLoading = ref<boolean>(true);
const formState = reactive({
  job_types: [] as JobType[],
  current_address: "",
  industries: [] as string[],
  job_titles: "",
  salary_requirements: "",
  gender: "",
  workplace_type: [] as string[],
  languages_fluent: [] as string[],
  languages_learning: [] as string[],
  visa_sponsorship: false,
  regions: [] as string[],
  countries: "",
  industry_excludes: [] as string[],
  functional_preferences: [] as string[],
  seniority: [] as string[],
  company_size: [] as string[],
  keywords: "",
  company_excludes: "",
  role_excludes: "",
  other_info: "",
  nationality: "" as string | { label: string; value: string },
  authorized_countries: [] as Array<string | { label: string; value: string }>,
  date_of_birth: "",
  university: "",
});

// Convert all options to objects with id and name
const regionOptions = ref(
  regions.map((region, index) => ({
    id: index + 1,
    name: region,
  }))
);

const functionalPreferenceOptions = ref(
  functionalPreferences.map((pref, index) => ({
    id: index + 1,
    name: pref,
  }))
);

const companySizeOptions = ref(
  companyTypes.map((size, index) => ({
    id: index + 1,
    name: size,
  }))
);

// Convert languages array to ref with proper type checking
const languageOptions = ref(
  Array.isArray(languagesRef.value)
    ? languagesRef.value.map((lang: string, index: number) => ({
        id: index + 1,
        name: lang,
      }))
    : []
);

const languagesFluent = computed({
  get: () => selectedLanguagesFluent.value,
  set: async (labels) => {
    const promises = labels.map(async (label: any) => {
      if (label.id) {
        // Use Set to ensure unique values
        formState.languages_fluent = Array.from(
          new Set([...formState.languages_fluent, label.name])
        );
        return label;
      }

      const response = {
        id: languageOptions.value.length + 1,
        name: label.name,
      };

      languageOptions.value.push(response);
      // Use Set to ensure unique values
      formState.languages_fluent = Array.from(
        new Set([...formState.languages_fluent, label.name])
      );
      return response;
    });

    selectedLanguagesFluent.value = await Promise.all(promises);
  },
});

const languagesLearning = computed({
  get: () => selectedLanguagesLearning.value,
  set: async (labels) => {
    const promises = labels.map(async (label: any) => {
      if (label.id) {
        // Use Set to ensure unique values
        formState.languages_learning = Array.from(
          new Set([...formState.languages_learning, label.name])
        );
        return label;
      }

      const response = {
        id: languageOptions.value.length + 1,
        name: label.name,
      };

      languageOptions.value.push(response);
      // Use Set to ensure unique values
      formState.languages_learning = Array.from(
        new Set([...formState.languages_learning, label.name])
      );
      return response;
    });

    selectedLanguagesLearning.value = await Promise.all(promises);
  },
});

// Computed property for regions
const regionsComputed = computed({
  get: () => selectedRegions.value,
  set: async (labels) => {
    const promises = labels.map(async (label: any) => {
      if (label.id) {
        formState.regions = Array.from(
          new Set([...formState.regions, label.name])
        );
        return label;
      }

      const response = {
        id: regionOptions.value.length + 1,
        name: label.name,
      };

      regionOptions.value.push(response);
      formState.regions = Array.from(
        new Set([...formState.regions, label.name])
      );
      return response;
    });

    selectedRegions.value = await Promise.all(promises);
  },
});

// Computed property for functional preferences
const functionalPreferencesComputed = computed({
  get: () => selectedFunctionalPreferences.value,
  set: async (labels) => {
    const promises = labels.map(async (label: any) => {
      if (label.id) {
        formState.functional_preferences = Array.from(
          new Set([...formState.functional_preferences, label.name])
        );
        return label;
      }

      const response = {
        id: functionalPreferenceOptions.value.length + 1,
        name: label.name,
      };

      functionalPreferenceOptions.value.push(response);
      formState.functional_preferences = Array.from(
        new Set([...formState.functional_preferences, label.name])
      );
      return response;
    });

    selectedFunctionalPreferences.value = await Promise.all(promises);
  },
});

// Local ref for countries list that we can modify
const localCountries: any = ref([...countries]);

// Computed property that just returns the local countries list
const sortedCountries = computed(() => localCountries.value);

// Computed property for company size
const companySizeComputed = computed({
  get: () => selectedCompanySize.value,
  set: async (labels) => {
    const promises = labels.map(async (label: any) => {
      if (label.id) {
        formState.company_size = Array.from(
          new Set([...formState.company_size, label.name])
        );
        return label;
      }

      const response = {
        id: companySizeOptions.value.length + 1,
        name: label.name,
      };

      companySizeOptions.value.push(response);
      formState.company_size = Array.from(
        new Set([...formState.company_size, label.name])
      );
      return response;
    });

    selectedCompanySize.value = await Promise.all(promises);
  },
});

// Helper function to display array values
function displayArrayValue(arr: any[] | null | undefined): string {
  return Array.isArray(arr) && arr.length > 0
    ? arr.join(", ")
    : "Not specified";
}

// Load existing onboarding data
async function loadOnboardingData() {
  const { data, error } = await supabase
    .from("customer_onboarding")
    .select("*")
    .eq("customer_id", props.customer.customerId)
    .single();

  if (error) {
    console.error("Error loading onboarding data:", error);
    return;
  }

  if (data) {
    existingOnboardingId.value = data.id;

    // Directly set form state values from data
    formState.job_types = data?.job_types || [];
    formState.current_address = data.current_address || "";
    formState.industries =
      (typeof data?.industries === 'string' &&
        data?.industries.split(",").map((item) => item.trim())) ||
      ([] as string[]);
    formState.job_titles = data.job_titles || "";
    formState.salary_requirements = data.salary_requirements || "";
    formState.gender = data.gender || "";
    formState.workplace_type = data.workpalce_type || [];
    formState.languages_fluent = data.languages_fluent || [];
    formState.languages_learning = data.languages_learning || [];
    formState.visa_sponsorship = data.visa_sponsorship || false;
    formState.regions = data.regions || [];
    formState.countries = data.countries || "";
    formState.industry_excludes =
      (typeof data?.industry_excludes === 'string' &&
        data?.industry_excludes.split(",").map((item) => item.trim())) ||
      ([] as string[]);
    formState.functional_preferences = data.functional_preferences || [];
    formState.seniority = data.seniority || [];
    formState.company_size = data.company_size || [];
    formState.keywords = data.keywords || "";
    formState.company_excludes = data.company_excludes || "";
    formState.role_excludes = data.role_excludes || "";
    formState.other_info = data.other_info || "";
    // Convert nationality to option format
    if (data.nationality) {
      const nationalityOption = countries.find(
        (opt) => opt.value === data.nationality
      );
      formState.nationality = nationalityOption || "";
    } else {
      formState.nationality = "";
    }

    // Convert authorized_countries to option format and sort the countries list
    if (data.authorized_countries && Array.isArray(data.authorized_countries)) {
      // First, convert the authorized countries to full objects
      const selectedCountries = data.authorized_countries
        .map((code: any) => countries.find((opt) => opt.value === code))
        .filter((opt: any) => opt); // Remove any undefined values

      // Get the remaining countries that are not in authorized_countries
      const remainingCountries = countries.filter(
        (country) =>
          data.authorized_countries &&
          !data.authorized_countries.includes(country.value)
      );

      // Update the local countries array to have selected ones first
      localCountries.value = [...selectedCountries, ...remainingCountries];

      // Set the form state
      formState.authorized_countries = selectedCountries.filter(
        (country: any): country is { label: string; value: string } =>
          country !== undefined
      );
    } else {
      localCountries.value = [...countries];
      formState.authorized_countries = [];
    }
    // Get only the date part when loading from database
    formState.date_of_birth = data.date_of_birth
      ? data.date_of_birth.split("T")[0].split("+")[0]
      : "";
    formState.university = data.university || "";

    // Safely handle languages_fluent
    if (Array.isArray(data.languages_fluent)) {
      selectedLanguagesFluent.value = data.languages_fluent.map((lang: any) => {
        const option = languageOptions.value.find((opt) => opt.name === lang);
        return option || { id: languageOptions.value.length + 1, name: lang };
      });
      formState.languages_fluent = data.languages_fluent;
    } else {
      selectedLanguagesFluent.value = [];
      formState.languages_fluent = [];
    }

    // Safely handle languages_learning
    if (Array.isArray(data.languages_learning)) {
      selectedLanguagesLearning.value = data.languages_learning.map(
        (lang: any) => {
          const option = languageOptions.value.find((opt) => opt.name === lang);
          return option || { id: languageOptions.value.length + 1, name: lang };
        }
      );
      formState.languages_learning = data.languages_learning;
    } else {
      selectedLanguagesLearning.value = [];
      formState.languages_learning = [];
    }

    selectedRegions.value = Array.isArray(data.regions)
      ? data.regions?.map((region: any) => ({
          id: regionOptions.value.find((option) => option.name === region)?.id,
          name: region,
        }))
      : [];

    selectedFunctionalPreferences.value = Array.isArray(
      data.functional_preferences
    )
      ? data.functional_preferences.map((pref: any) => ({
          id: functionalPreferenceOptions.value.find(
            (option) => option.name === pref
          )?.id,
          name: pref,
        }))
      : [];

    selectedCompanySize.value = Array.isArray(data.company_size)
      ? data.company_size.map((size: any) => ({
          id: companySizeOptions.value.find((option) => option.name === size)
            ?.id,
          name: size,
        }))
      : [];
  }
}

const fetchLocations = async () => {
  if (existingOnboardingId.value) {
    try {
      const { data, error } = await supabase
        .from("onboarding_locations")
        .select("*")
        .eq("onboarding_id", Number(existingOnboardingId.value));

      if (error) throw error;

      // Populate the locations array with fetched data
      locations.value = data.map((loc) => ({
        id: loc.id,
        location: loc.location,
        workAuthorized: loc.work_authorized,
        payRequirement: loc.pay_requirement ?? "",
      }));
    } catch (error) {
      console.error("Error fetching locations:", error);
    }
  }
};

// Load customer's CV documents
async function loadDocuments() {
  try {
    // Fetch data from the Supabase table
    const { data, error } = await supabase
      .from("documents")
      .select("*")
      .eq("customer_id", props.customer.customerId)
      .order("created_at", { ascending: false })
      .eq("active", true);

    if (error) {
      console.error("Error loading documents:", error);
      return;
    }

    if (data) {
      documents.value = data.map((doc) => ({
        id: doc.id,
        bucket: doc.bucket,
        path: doc.path,
        name: doc.file_name,
        customerId: doc.customer_id,
        type: enumFromKey(DocumentType, doc.document_type),
        createdAt: doc.created_at,
      }));

      // Filter and map the data to ref values
      customerCVs.value = documents.value.filter(
        (doc: any) => doc.type === DocumentType.CV
      );
      customerCoverLetters.value = documents.value.filter(
        (doc: any) => doc.type === DocumentType.COVER
      );
    }
  } catch (err) {
    console.error("Unexpected error loading documents:", err);
  }
}

// Handle form submission
async function submitForm() {
  try {
    const onboardingData = {
      customer_id: props.customer.customerId,
      job_types: formState.job_types.length > 0 ? formState.job_types : null,
      languages_fluent:
        formState.languages_fluent.length > 0
          ? formState.languages_fluent
          : null,
      languages_learning:
        formState.languages_learning.length > 0
          ? formState.languages_learning
          : null,
      current_address: formState.current_address || null,
      industries:
        Array.isArray(formState?.industries) && formState.industries.length > 0
          ? formState.industries.join(", ")
          : null,
      job_titles: formState.job_titles || null,
      salary_requirements: formState.salary_requirements || null,
      gender: formState.gender || null,
      workpalce_type:
        formState.workplace_type.length > 0 ? formState.workplace_type : null,
      visa_sponsorship: formState.visa_sponsorship || null,
      regions: formState.regions.length > 0 ? formState.regions : null,
      countries: formState.countries || null,
      industry_excludes:
        Array.isArray(formState?.industry_excludes) &&
        formState.industry_excludes.length > 0
          ? formState.industry_excludes.join(", ")
          : null,
      functional_preferences:
        formState.functional_preferences.length > 0
          ? formState.functional_preferences
          : null,
      seniority: formState.seniority.length > 0 ? formState.seniority : null,
      company_size:
        formState.company_size.length > 0 ? formState.company_size : null,
      keywords: formState.keywords || null,
      company_excludes: formState.company_excludes || null,
      role_excludes: formState.role_excludes || null,
      other_info: formState.other_info || null,
      nationality:
        (typeof formState.nationality === "object" && formState.nationality
          ? formState.nationality.value
          : formState.nationality) || null,
      authorized_countries:
        formState.authorized_countries.length > 0
          ? formState.authorized_countries.map((item: any) =>
              typeof item === "object" ? item.value : item
            )
          : null,
      // Send only the date part to database
      date_of_birth: formState.date_of_birth || null,
      university: formState.university || null,
    };

    let error;
    if (existingOnboardingId.value) {
      const { error: updateError } = await supabase
        .from("customer_onboarding")
        .update(onboardingData as object)
        .eq("id", existingOnboardingId.value)
        .eq("customer_id", props.customer.customerId);
      error = updateError;
    } else {
      const { error: insertError } = await supabase
        .from("customer_onboarding")
        .insert(onboardingData as object);
      error = insertError;
    }

    if (error) {
      console.error("Database operation error:", error);
      throw error;
    }

    await loadOnboardingData();

    const { error: customerError } = await supabase
      .from("customers")
      .update({ onboarding_complete: true })
      .eq("id", props.customer.customerId);

    if (customerError) throw customerError;

    toast.add({
      title: "Success",
      description: "Profile updated successfully",
      color: "green",
    });

    isEditing.value = false;
  } catch (error) {
    console.error("Error saving form:", error);
    toast.add({
      title: "Error",
      description: "Failed to update profile",
      color: "red",
    });
  }
}

// Handle successful document upload
function handleUploadSuccess() {
  loadDocuments();
  emit("getCustomerDocuments");
}

async function handleDocumentDelete(docId: string) {
  try {
    const { error } = await supabase
      .from("documents")
      .update({ active: false })
      .eq("id", docId);

    if (error) throw error;

    // Emit event to parent
    emit("documentDeleted");

    // Update local state
    documents.value = documents.value.filter((d) => d.id !== docId);
    toast.add({
      title: "Success",
      description: "Document deleted successfully",
      color: "green",
    });

    loadDocuments();
  } catch (error) {
    console.error("Error deleting document:", error);
    toast.add({
      title: "Error",
      description: "Failed to delete document",
      color: "red",
    });
  }
}

watch(
  () => props.documentDeleted,
  async (newVal) => {
    if (newVal) {
      // Perform actions when isDeleted changes
      await loadDocuments();
    }
  }
);

// Initialize component
onMounted(async () => {
  isLoading.value = true;
  await loadOnboardingData();
  await fetchLocations();
  await loadDocuments();
  isLoading.value = false;
});
</script>

<template>
  <div class="space-y-6">
    <!-- Header with Edit Button -->
    <UForm :state="formState" class="space-y-4" @submit="submitForm">
      <div class="flex justify-between items-center">
        <h3 class="text-lg font-semibold text-orange-600">Onboarding Survey</h3>
        <div class="gap-4 flex flex-wrap">
          <!-- Submit Button -->
          <div>
            <UButton
              v-if="isEditing"
              variant="outline"
              icon="la:save"
              type="submit"
              class="w-38 h-10 text-nowrap"
              :label="existingOnboardingId ? 'Update Changes' : 'Save Changes'"
            />
          </div>
          <div class="pl-4">
            <UButton
              v-if="isCustomer"
              variant="outline"
              class="w-36 h-10 text-nowrap"
              :icon="isEditing ? 'la:window-close' : 'la:edit-solid'"
              :label="isEditing ? 'Cancel' : 'Edit'"
              @click="isEditing = !isEditing"
            />
          </div>
        </div>
      </div>

      <template v-if="isLoading">
        <div class="flex justify-center items-center pt-20">
          <BaseSpinner :show-loader="isLoading" size="lg" />
        </div>
      </template>
      <template v-else>
        <div class="flex flex-col md:flex-row space-y-4">
          <div class="grid sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
            <!-- Nationality -->
            <UFormGroup label="What is your nationality?" class="my-2">
              <template v-if="isEditing">
                <USelectMenu
                  v-model="formState.nationality"
                  :options="countries"
                  option-attribute="label"
                  searchable
                  placeholder="Select your nationality"
                />
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{
                    formState.nationality
                      ? typeof formState.nationality === "object"
                        ? formState.nationality.label
                        : formState.nationality
                      : "Not specified"
                  }}
                </div>
              </template>
            </UFormGroup>

            <!-- Date of Birth -->
            <UFormGroup
              label="What is your date of birth? (DD/MM/YYYY)"
              class="my-2"
            >
              <template v-if="isEditing">
                <UPopover :popper="{ placement: 'bottom-start' }">
                  <UButton
                    icon="i-heroicons-calendar-days-20-solid"
                    :label="
                      formState.date_of_birth &&
                      format(new Date(formState.date_of_birth), 'dd/MM/yyyy')
                    "
                    variant="outline"
                    :ui="{
                      base: 'w-full',
                      truncate: 'flex justify-center w-full',
                    }"
                    truncate
                  />
                  <template #panel="{ close }">
                    <DatePicker
                      v-model="formState.date_of_birth"
                      is-required
                      @close="close"
                    />
                  </template>
                </UPopover>
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{
                    formState.date_of_birth
                      ? new Date(formState.date_of_birth).toLocaleDateString()
                      : "Not specified"
                  }}
                </div>
              </template>
            </UFormGroup>

            <!-- University -->
            <UFormGroup label="What university did you attend?" class="my-2">
              <template v-if="isEditing">
                <UInput
                  v-model="formState.university"
                  placeholder="Enter your university"
                />
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{ formState.university || "Not specified" }}
                </div>
              </template>
            </UFormGroup>

            <!-- Gender -->
            <UFormGroup
              label="What is your gender?"
              class="my-2"
              :ui="{
                label: {
                  base: 'block font-medium text-gray-700 dark:text-gray-200',
                },
              }"
            >
              <template v-if="isEditing">
                <USelectMenu v-model="formState.gender" :options="genders" />
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{ formState.gender || "Not specified" }}
                </div>
              </template>
            </UFormGroup>

            <!-- current address -->
            <UFormGroup
              label="what is your current address?"
              class="my-2"
              :ui="{
                label: {
                  base: 'block font-medium text-gray-700 dark:text-gray-200',
                },
              }"
            >
              <template v-if="isEditing">
                <UInput
                  v-model="formState.current_address"
                  placeholder="Enter locations..."
                />
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{ formState.current_address || "Not specified" }}
                </div>
              </template>
            </UFormGroup>

            <!-- languagesFluent -->
            <UFormGroup
              label="What languages do you speak fluently?"
              class="my-2"
              :ui="{
                label: {
                  base: 'block font-medium text-gray-700 dark:text-gray-200',
                },
              }"
            >
              <template v-if="isEditing">
                <USelectMenu
                  v-model="languagesFluent"
                  :options="languageOptions"
                  by="id"
                  name="languages_fluent"
                  option-attribute="name"
                  multiple
                  searchable
                  creatable
                  :default-value="selectedLanguagesFluent"
                  placeholder="Select languages..."
                >
                  <template #option="{ option }">
                    <span class="truncate">{{ option.name }}</span>
                  </template>
                  <template #option-create="{ option }">
                    <span class="flex-shrink-0">New language:</span>
                    <span class="block truncate">{{ option.name }}</span>
                  </template>
                </USelectMenu>
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{ displayArrayValue(formState.languages_fluent) }}
                </div>
              </template>
            </UFormGroup>

            <!-- learning Language -->
            <UFormGroup
              label="What languages are you learning?"
              class="my-2"
              :ui="{
                label: {
                  base: 'block font-medium text-gray-700 dark:text-gray-200',
                },
              }"
            >
              <template v-if="isEditing">
                <USelectMenu
                  v-model="languagesLearning"
                  :options="languageOptions"
                  by="id"
                  name="languages_learning"
                  option-attribute="name"
                  multiple
                  searchable
                  creatable
                  :default-value="selectedLanguagesLearning"
                  placeholder="Select languages..."
                >
                  <template #option="{ option }">
                    <span class="truncate">{{ option.name }}</span>
                  </template>
                  <template #option-create="{ option }">
                    <span class="flex-shrink-0">New language:</span>
                    <span class="block truncate">{{ option.name }}</span>
                  </template>
                </USelectMenu>
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{ displayArrayValue(formState.languages_learning) }}
                </div>
              </template>
            </UFormGroup>

            <!-- visa sponsorship -->
            <UFormGroup
              label="Will you need visa sponsorship?"
              class="my-2"
              :ui="{
                label: {
                  base: 'block font-medium text-gray-700 dark:text-gray-200',
                },
              }"
            >
              <template v-if="isEditing">
                <UToggle v-model="formState.visa_sponsorship" color="orange" />
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{
                    existingOnboardingId
                      ? formState.visa_sponsorship
                        ? "Yes"
                        : "No"
                      : "Not specified"
                  }}
                </div>
              </template>
            </UFormGroup>

            <!-- Job titles -->
            <UFormGroup
              label="What job roles/titles would you like?"
              class="my-2"
              :ui="{
                label: {
                  base: 'block font-medium text-gray-700 dark:text-gray-200',
                },
              }"
            >
              <template v-if="isEditing">
                <UInput
                  v-model="formState.job_titles"
                  placeholder="Enter job roles..."
                />
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{ formState.job_titles || "Not specified" }}
                </div>
              </template>
            </UFormGroup>

            <!-- Industries -->
            <UFormGroup
              label="What industries would you like to work in?"
              class="my-2"
              :ui="{
                label: {
                  base: 'block font-medium text-gray-700 dark:text-gray-200',
                },
              }"
            >
              <template v-if="isEditing">
                <USelectMenu
                  v-model="formState.industries"
                  :options="industries"
                  multiple
                  :default-value="formState.industries"
                  placeholder="Select Industry..."
                />
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{
                    Array.isArray(formState.industries) &&
                    displayArrayValue(formState.industries)
                  }}
                </div>
              </template>
            </UFormGroup>

            <!-- Job Types -->
            <UFormGroup
              label="What kind of job do you want?"
              class="my-2"
              :ui="{
                label: {
                  base: 'block font-medium text-gray-700 dark:text-gray-200',
                },
              }"
            >
              <template v-if="isEditing">
                <USelectMenu
                  v-model="formState.job_types"
                  :options="jobType"
                  multiple
                  :default-value="formState.job_types"
                  placeholder="Select job types..."
                />
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{ displayArrayValue(formState.job_types) }}
                </div>
              </template>
            </UFormGroup>

            <!-- workplace -->
            <UFormGroup
              label="What type of workplace do you want?"
              class="my-2"
              :ui="{
                label: {
                  base: 'block font-medium text-gray-700 dark:text-gray-200',
                },
              }"
            >
              <template v-if="isEditing">
                <USelectMenu
                  v-model="formState.workplace_type"
                  :options="workSpace"
                  multiple
                  :default-value="formState.workplace_type"
                />
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{ displayArrayValue(formState.workplace_type) }}
                </div>
              </template>
            </UFormGroup>

            <!-- functional preferences -->
            <UFormGroup
              label="What are your functional preferences?"
              class="my-2"
              :ui="{
                label: {
                  base: 'block font-medium text-gray-700 dark:text-gray-200',
                },
              }"
            >
              <template v-if="isEditing">
                <USelectMenu
                  v-model="functionalPreferencesComputed"
                  :options="functionalPreferenceOptions"
                  by="id"
                  name="functional_preferences"
                  option-attribute="name"
                  multiple
                  searchable
                  creatable
                  :default-value="selectedFunctionalPreferences"
                  placeholder="Select or enter preferences..."
                >
                  <template #option="{ option }">
                    <span class="truncate">{{ option.name }}</span>
                  </template>
                  <template #option-create="{ option }">
                    <span class="flex-shrink-0">New preference:</span>
                    <span class="block truncate">{{ option.name }}</span>
                  </template>
                </USelectMenu>
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{ displayArrayValue(formState.functional_preferences) }}
                </div>
              </template>
            </UFormGroup>

            <!-- capture relevant roles keywords -->
            <UFormGroup
              label="Any specific keywords we should include to capture relevant roles?"
              class="my-2"
              :ui="{
                label: {
                  base: 'block font-medium text-gray-700 dark:text-gray-200',
                },
              }"
            >
              <template v-if="isEditing">
                <UInput
                  v-model="formState.keywords"
                  placeholder="Enter keywords..."
                />
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{ formState.keywords || "Not specified" }}
                </div>
              </template>
            </UFormGroup>

            <!-- level of seniority -->
            <UFormGroup
              label="What level of seniority are you targeting?"
              class="my-2"
              :ui="{
                label: {
                  base: 'block font-medium text-gray-700 dark:text-gray-200',
                },
              }"
            >
              <template v-if="isEditing">
                <USelectMenu
                  v-model="formState.seniority"
                  :options="seniorityLevels"
                  multiple
                />
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{ displayArrayValue(formState.seniority) }}
                </div>
              </template>
            </UFormGroup>

            <!-- companySize -->
            <UFormGroup
              label="What size or type of company should we prioritize?"
              class="my-2"
              :ui="{
                label: {
                  base: 'block font-medium text-gray-700 dark:text-gray-200',
                },
              }"
            >
              <template v-if="isEditing">
                <USelectMenu
                  v-model="companySizeComputed"
                  :options="companySizeOptions"
                  by="id"
                  name="company_size"
                  option-attribute="name"
                  multiple
                  searchable
                  creatable
                  :default-value="selectedCompanySize"
                  placeholder="Select company preferences..."
                >
                  <template #option="{ option }">
                    <span class="truncate">{{ option.name }}</span>
                  </template>
                  <template #option-create="{ option }">
                    <span class="flex-shrink-0">New size:</span>
                    <span class="block truncate">{{ option.name }}</span>
                  </template>
                </USelectMenu>
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{ displayArrayValue(formState.company_size) }}
                </div>
              </template>
            </UFormGroup>

            <!-- companies excluded -->
            <UFormGroup
              label="What companies should be excluded entirely?"
              class="my-2"
              :ui="{
                label: {
                  base: 'block font-medium text-gray-700 dark:text-gray-200',
                },
              }"
            >
              <template v-if="isEditing">
                <UInput
                  v-model="formState.company_excludes"
                  placeholder="Enter excluded companies..."
                />
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{ formState.company_excludes || "Not specified" }}
                </div>
              </template>
            </UFormGroup>

            <!-- roles to exclude -->
            <UFormGroup
              label="Are there types of roles to exclude? (e.g., operational or entry-level roles)"
              class="my-2"
              :ui="{
                label: {
                  base: 'block font-medium text-gray-700 dark:text-gray-200',
                },
              }"
            >
              <template v-if="isEditing">
                <UInput
                  v-model="formState.role_excludes"
                  placeholder="Enter excluded roles..."
                />
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{ formState.role_excludes || "Not specified" }}
                </div>
              </template>
            </UFormGroup>

            <!-- other industries to exclude -->
            <UFormGroup
              label="Are there any other industries to exclude?"
              class="my-2"
              :ui="{
                label: {
                  base: 'block font-medium text-gray-700 dark:text-gray-200',
                },
              }"
            >
              <template v-if="isEditing">
                <USelectMenu
                  v-model="formState.industry_excludes"
                  :options="industries"
                  multiple
                  :default-value="formState.industry_excludes"
                  placeholder="Select Industry..."
                />
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{
                    Array.isArray(formState.industry_excludes) &&
                    displayArrayValue(formState.industry_excludes)
                  }}
                </div>
              </template>
            </UFormGroup>

            <!-- Other information -->
            <UFormGroup
              label="Other information we should know:"
              :ui="{
                label: {
                  base: 'block font-medium text-gray-700 dark:text-gray-200',
                },
              }"
            >
              <template v-if="isEditing">
                <UTextarea
                  v-model="formState.other_info"
                  placeholder="Enter other information..."
                />
              </template>
              <template v-else>
                <div class="text-sm text-gray-700">
                  {{ formState.other_info || "Not specified" }}
                </div>
              </template>
            </UFormGroup>

            <!-- Upload Documents -->
            <UFormGroup
              v-if="isCustomer && isEditing"
              label="Upload Documents:"
              :ui="{
                label: {
                  base: 'block font-medium text-gray-700 dark:text-gray-200',
                },
              }"
            >
              <!-- File Uploader -->
              <div class="flex items-center gap-4">
                <FileUploader
                  :customer-id="customer.customerId"
                  :document-types="docTypes"
                  :document-added-callback="handleUploadSuccess"
                />
              </div>
            </UFormGroup>
          </div>
        </div>

        <UFormGroup
          label="Locations"
          :ui="{
            label: {
              base: 'block font-medium text-gray-700 dark:text-gray-200',
            },
          }"
        >
          <div
            v-if="!isEditing && locations.length"
            class="grid sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6"
          >
            <div
              v-for="(location, index) in locations"
              :key="index"
              class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700"
            >
              <p class="text-sm text-gray-800 dark:text-gray-100">
                <span>Location:</span>
                {{ location.location || "Not specified" }}
              </p>
              <p class="text-sm text-gray-800 dark:text-gray-100">
                <span>Authorized to work:</span>
                {{ location.workAuthorized ? "Yes" : "No" }}
              </p>
              <p class="text-sm text-gray-800 dark:text-gray-100">
                <span>Salary:</span>
                {{ location.payRequirement || "Not specified" }}
              </p>
            </div>
          </div>
          <div v-else class="text-sm text-gray-700">N/a</div>
        </UFormGroup>
        <!-- CV Documents -->
        <div class="flex gap-4">
          <UFormGroup
            label="Your CV Documents"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <div v-if="customerCVs.length > 0" class="space-y-2 flex flex-col">
              <div
                v-for="doc in customerCVs"
                :key="doc.id"
                class="flex items-center justify-between gap-4 p-2 bg-gray-50 rounded-lg"
              >
                <div class="flex-grow">
                  <p class="text-sm font-medium text-gray-700">
                    {{ doc.name }}
                  </p>
                </div>
                <div class="flex">
                  <DocumentDownload v-if="!isEditing" :document="doc" />
                  <UIcon
                    v-if="isCustomer && isEditing"
                    name="la:trash"
                    class="text-red-500 w-7 h-7 hover:cursor-pointer"
                    @click="handleDocumentDelete(doc.id)"
                  />
                </div>
              </div>
            </div>

            <!-- Empty State -->
            <div v-else class="text-sm text-gray-500">
              No CV documents uploaded yet.
            </div>
          </UFormGroup>

          <!-- Cover Documents -->
          <UFormGroup
            label="Your Cover Documents"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <div
              v-if="customerCoverLetters.length > 0"
              class="space-y-2 flex flex-col"
            >
              <div
                v-for="doc in customerCoverLetters"
                :key="doc.id"
                class="flex items-center justify-between gap-4 p-2 bg-gray-50 rounded-lg"
              >
                <div class="flex-grow">
                  <p class="text-sm font-medium text-gray-700">
                    {{ doc.name }}
                  </p>
                </div>
                <div class="flex">
                  <DocumentDownload v-if="!isEditing" :document="doc" />
                  <UIcon
                    v-if="isCustomer && isEditing"
                    name="la:trash"
                    class="text-red-500 w-7 h-7 hover:cursor-pointer"
                    @click="handleDocumentDelete(doc.id)"
                  />
                </div>
              </div>
            </div>

            <!-- Empty State -->
            <div v-else class="text-sm text-gray-500">
              No Cover documents uploaded yet.
            </div>
          </UFormGroup>
        </div>
      </template>
    </UForm>
  </div>
</template>
