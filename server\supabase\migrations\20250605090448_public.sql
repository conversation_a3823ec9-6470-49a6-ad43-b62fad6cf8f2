create table "public"."jobs_audit" (
    "id" uuid not null default gen_random_uuid(),
    "job_id" uuid,
    "modified_by" uuid,
    "modified_time" timestamp with time zone not null default now(),
    "current_job_status" text,
    "new_job_status" text
);


alter table "public"."jobs" add column "modified_by" uuid;

alter table "public"."jobs" add column "modified_time" timestamp with time zone;

CREATE UNIQUE INDEX jobs_audit_pkey ON public.jobs_audit USING btree (id);

alter table "public"."jobs_audit" add constraint "jobs_audit_pkey" PRIMARY KEY using index "jobs_audit_pkey";

alter table "public"."jobs" add constraint "jobs_modified_by_fkey" FOREIGN KEY (modified_by) REFERENCES auth.users(id) not valid;

alter table "public"."jobs" validate constraint "jobs_modified_by_fkey";

alter table "public"."jobs_audit" add constraint "jobs_audit_job_id_fkey" FOREIGN KEY (job_id) REFERENCES jobs(id) not valid;

alter table "public"."jobs_audit" validate constraint "jobs_audit_job_id_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.update_and_audit_job()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
declare
  mod_user uuid;
  mod_time timestamptz;
begin
  begin
    mod_user := auth.uid()::uuid;
  exception when others then
    mod_user := '00000000-0000-0000-0000-000000000000';
  end;

  mod_time := current_timestamp;

  new.modified_by := mod_user;
  new.modified_time := mod_time;

  if new.job_status is distinct from old.job_status then
    insert into public.jobs_audit (
      id,
      job_id,
      current_job_status,
      new_job_status,
      modified_by,
      modified_time
    ) values (
      gen_random_uuid(),
      new.id,
      old.job_status,
      new.job_status,
      mod_user,
      mod_time
    );
  end if;

  return new;
end;
$function$
;

grant delete on table "public"."jobs_audit" to "anon";

grant insert on table "public"."jobs_audit" to "anon";

grant references on table "public"."jobs_audit" to "anon";

grant select on table "public"."jobs_audit" to "anon";

grant trigger on table "public"."jobs_audit" to "anon";

grant truncate on table "public"."jobs_audit" to "anon";

grant update on table "public"."jobs_audit" to "anon";

grant delete on table "public"."jobs_audit" to "authenticated";

grant insert on table "public"."jobs_audit" to "authenticated";

grant references on table "public"."jobs_audit" to "authenticated";

grant select on table "public"."jobs_audit" to "authenticated";

grant trigger on table "public"."jobs_audit" to "authenticated";

grant truncate on table "public"."jobs_audit" to "authenticated";

grant update on table "public"."jobs_audit" to "authenticated";

grant delete on table "public"."jobs_audit" to "service_role";

grant insert on table "public"."jobs_audit" to "service_role";

grant references on table "public"."jobs_audit" to "service_role";

grant select on table "public"."jobs_audit" to "service_role";

grant trigger on table "public"."jobs_audit" to "service_role";

grant truncate on table "public"."jobs_audit" to "service_role";

grant update on table "public"."jobs_audit" to "service_role";

CREATE TRIGGER trg_update_and_audit_job BEFORE UPDATE ON public.jobs FOR EACH ROW EXECUTE FUNCTION update_and_audit_job();


