
import { setPostmark, startDeno } from "../customer-created/index.ts";
import { load } from 'jsr:@std/dotenv';
import { supabaseClient } from '../shared/common.ts';
import { createCustomer, post, TestPostmark } from './test-common.ts';
import { assertEquals } from 'jsr:@std/assert';

const testData = {
  customerId: "1234-5678-9000",
  email: "<EMAIL>",
  referred_by_id: "ABC12345",
  affiliate_id: "AAAAAAAA"
}


Deno.test("integration test",
  async () => {
    await load({ export:true });
    startDeno()
    const supabase = supabaseClient();
    const postmark = new TestPostmark();
    setPostmark(postmark);

    const eventPayload = Deno.readTextFileSync("./test-customer-created-event.json");
    
    await createCustomer(
      testData.email,
      supabase,
      testData.referred_by_id);

    await post(eventPayload);
    
    assertNotificationSent(postmark); 

})

function assertNotificationSent(postmark: TestPostmark) {
  assertEquals(postmark.sent.length, 1, "notification not sent");
  assertEquals(postmark.sent[0].body.includes(testData.affiliate_id), true, "affiliate id was not in email body");
  assertEquals(postmark.sent[0].to, testData.email, "email was wrong");
}

