
import { setEvents, startDeno } from "../create-search-criteria/index.ts";
import { load } from 'jsr:@std/dotenv';
import { createCustomer, post, TestEvents, upsertOnboarding, upsertPlan } from './test-common.ts';
import { assertEquals, assertNotEquals } from 'jsr:@std/assert';
import { Customer, getCustomerSearchCriteria, PRODUCT_CODE, supabaseClient } from '../shared/common.ts';



Deno.test("integration test",
  async () => {
    await load({ export:true });
    startDeno()
    const supabase = supabaseClient();
    // search crtieria requires a customer with open plan
    const cust:Customer = await createCustomer("<EMAIL>", supabase);
    await upsertPlan(cust.id, PRODUCT_CODE.APPS_20, supabase);
    const titles = [ "manager", "janitor" ];
    const locations = [ "Paris", "Berlin" ];
    await upsertOnboarding(cust.id, titles, locations, supabase);
    const searchDateStr = "2025-05-19"
    const events = new TestEvents();
    setEvents(events);

    await post(JSON.stringify({ searchDate: searchDateStr }));
      
    // ensure the correct message was sent
    events.published.forEach((event) => console.log("created event:", event));
    assertNotEquals(events.published.length, 0, "search criteria not created");

    const searchCriteriaId = events.published.pop()?.customerSearchCriteriaId!;
    const searchCriteria = await getCustomerSearchCriteria(searchCriteriaId, supabase);

    // ensure search criteria created and saved
    assertEquals(searchCriteria.job_titles, titles);
    assertEquals(searchCriteria.locations, locations);
    assertEquals(searchCriteria.search_date, searchDateStr)

    console.log("created search criteria with job titles: ", searchCriteria.job_titles);
    console.log("created search criteria with location: ", searchCriteria.locations);

});
