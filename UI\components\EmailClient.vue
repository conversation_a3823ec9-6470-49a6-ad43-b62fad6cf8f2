<template>
  <div class="space-y-4">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div class="text-lg font-semibold">
        {{ customer?.name || "" }} ({{ customer?.email || "" }})
      </div>
    </div>

    <div class="flex gap-2">
      <UInput
        v-model="searchQuery"
        placeholder="Search emails..."
        class="w-full max-w-sm"
        @keyup.enter="handleSearch"
      />

      <UButton
        icon="i-heroicons-magnifying-glass"
        :loading="loading"
        @click="handleSearch"
      />
    </div>

    <ClientOnly>
      <UTable :rows="rows" :columns="columns" :loading="loading">
        <template #date-data="{ row }">
          {{ formatDate(row.date) }}
        </template>

        <template #view-data="{ row }">
          <UPopover :popper="{ placement: 'left' }">
            <UButton
              color="gray"
              variant="ghost"
              icon="i-heroicons-eye"
              size="sm"
            />
            <template #panel>
              <div class="max-w-lg max-h-96 overflow-auto p-4">
                <div v-if="row.body" v-html="row.body" />
                <div v-else class="text-gray-500">No content available</div>
              </div>
            </template>
          </UPopover>
        </template>

        <template #loading-state>
          <div class="flex items-center justify-center p-4">
            <UIcon name="i-heroicons-arrow-path" class="animate-spin mr-2" />
            Loading messages...
          </div>
        </template>
      </UTable>
    </ClientOnly>
  </div>
</template>

<script lang="ts" setup>
import { NYLAS_CUSTOMER_STATUS, type Customer } from "~/common/common";
import type { Database } from "~/lib/applysquad-common/database.types";

const config = useRuntimeConfig();
const supabase = useSupabaseClient<Database>();
const nylasGrant = ref<string | null>(null);
const loading = ref(false);
const route = useRoute();
const customerId = route.query.customerId as string;

const rows = ref<Message[]>([]);
const searchQuery = ref("");

interface Message {
  id: string;
  from: string;
  subject: string;
  date: Date;
  body: string;
}

interface NylasMessage {
  id: string;
  from: Array<{ email: string; name?: string }>;
  subject: string;
  date: number;
  body: string;
}

const props = defineProps<{
  customer: Customer;
}>();

const columns = [
  {
    key: "from",
    label: "From",
  },
  {
    key: "date",
    label: "Date",
  },
  {
    key: "subject",
    label: "Subject",
  },
  {
    key: "view",
    label: "View",
    sortable: false,
  },
];

function formatDate(date: Date): string {
  return new Intl.DateTimeFormat("default", {
    dateStyle: "medium",
    timeStyle: "short",
  }).format(date);
}

async function fetchMessages(query: string): Promise<Message[]> {
  const baseUrl = config.public.NYLAS_API_URL;
  const apiKey = config.public.NYLAS_API_KEY;

  // Helper function to perform individual searches
  async function searchMessages(
    searchField: string,
    query: string
  ): Promise<NylasMessage[]> {
    const searchParams = new URLSearchParams({
      limit: "50",
      search_query_native: `${searchField} "${query}"`,
    });

    const endpoint = `${baseUrl}/v3/grants/${
      nylasGrant.value
    }/messages?${searchParams.toString()}`;

    try {
      const response = await fetch(endpoint, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        console.error(`Error in ${searchField} search: ${response.statusText}`);
        return [];
      }

      const jsonResponse = await response.json();
      return jsonResponse.data || [];
    } catch (error) {
      console.error(`Error fetching ${searchField} messages:`, error);
      return [];
    }
  }

  try {
    // Perform searches for SUBJECT and TEXT separately
    const [subjectResults, textResults] = await Promise.all([
      searchMessages("SUBJECT", query),
      searchMessages("TEXT", query),
    ]);

    // Combine and deduplicate the results
    const allResults = [...subjectResults, ...textResults];
    const uniqueMessages = Array.from(
      new Map(allResults.map((item) => [item.id, item])).values()
    );

    // Map the results to the desired Message format
    return uniqueMessages.map((item) => ({
      id: item.id || "",
      from: item.from?.[0]?.email || "Unknown",
      subject: item.subject || "No Subject",
      date: new Date(item.date ? item.date * 1000 : Date.now()),
      body: item.body || "",
    }));
  } catch (error) {
    console.error(`Error fetching messages:`, error);
    return [];
  }
}

async function handleSearch() {
  if (!searchQuery.value.trim()) {
    rows.value = [];
    return;
  }

  loading.value = true;
  try {
    rows.value = await fetchMessages(searchQuery.value.trim());
  } catch (error) {
    console.error("Error searching messages:", error);
    rows.value = [];
  } finally {
    loading.value = false;
  }
}

// Load initial messages
onMounted(async () => {
  loading.value = true;
  try {
    const { data, error } = await supabase
      .from("nylas_customers")
      .select("grant")
      .eq("customer_id", customerId)
      .eq("status", NYLAS_CUSTOMER_STATUS.READY)
      .single();

    if (error) {
      console.error("Error fetching Nylas grant:", error);
      return;
    }

    nylasGrant.value = data?.grant || null;
  } catch (error) {
    console.error("Error loading initial messages:", error);
  } finally {
    loading.value = false;
  }
});
</script>
