<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
      <h3 class="text-2xl font-semibold text-orange-600">Onboarding Survey</h3>
    </div>

    <!-- Select Menu for Small Screens -->
    <div v-if="isSmallScreen" class="mb-4">
      <USelectMenu
        v-model="agentTab"
        :options="baseAgentTabs"
        option-attribute="label"
      />
    </div>

    <!-- Tabs for Large Screens -->
    <UTabs
      v-if="!isSmallScreen"
      v-model="agentTab"
      :items="baseAgentTabs"
      orientation="vertical"
      class="w-full"
      :ui="{
        wrapper: 'flex items-start gap-8',
        list: { width: 'w-48' },
      }"
    >
      <template #role>
        <CustomerOnboardingStep1
          v-model:is-editing="isEditing"
          v-model:agent-tab="agentTab"
          :data="existingOnboardingData"
          :is-customer="isCustomer"
          :customer="customer"
          :load-data="loadOnboardingData"
        />
      </template>

      <template #company>
        <CustomerOnboardingStep2
          v-model:is-editing="isEditing"
          v-model:agent-tab="agentTab"
          :data="existingOnboardingData"
          :is-customer="isCustomer"
          :customer="customer"
          :load-data="loadOnboardingData"
        />
      </template>

      <template #location>
        <CustomerOnboardingStep3
          v-model:is-editing="isEditing"
          v-model:agent-tab="agentTab"
          :data="existingOnboardingData"
          :is-customer="isCustomer"
          :customer="customer"
          :load-data="loadOnboardingData"
        />
      </template>

      <template #personal>
        <CustomerOnboardingStep4
          v-model:is-editing="isEditing"
          v-model:agent-tab="agentTab"
          :data="existingOnboardingData"
          :is-customer="isCustomer"
          :customer="customer"
          :load-data="loadOnboardingData"
          v-model:hasOnboarding="isOnboarding"
        />
      </template>
    </UTabs>

    <!-- Dynamic Content for Small Screens -->
    <div v-else>
      <component
        :is="componentMap[agentTab.id]"
        v-model:is-editing="isEditing"
        v-model:agent-tab="agentTab"
        :data="existingOnboardingData"
        :is-customer="isCustomer"
        :customer="customer"
        :load-data="loadOnboardingData"
        v-model:hasOnboarding="isOnboarding"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  CustomerOnboardingStep1,
  CustomerOnboardingStep2,
  CustomerOnboardingStep3,
  CustomerOnboardingStep4,
} from "#components";
import { useWindowSize } from "@vueuse/core";
import type { Customer } from "~/common/common";
import type { Database } from "~/lib/applysquad-common/database.types";

const { width } = useWindowSize();
const isSmallScreen = computed(() => width.value < 768); // Adjust breakpoint as needed
const baseAgentTabs = [
  { slot: "role", label: "Role", id: 0 },
  { slot: "company", label: "Company", id: 1 },
  { slot: "location", label: "Location", id: 2 },
  { slot: "personal", label: "Personal", id: 3 },
];

const agentTab = ref<any>(0); // Ensure it's always an object
const supabase = useSupabaseClient<Database>();
const existingOnboardingData = ref<any>(null);
const isOnboarding = ref(false);
const hasOnboarding = defineModel<boolean>("hasOnboarding");

watchEffect(() => {
  if (isSmallScreen.value) {
    // Convert number agentTab to the correct object format
    const tab = baseAgentTabs.find((tab) => tab.id === agentTab.value);
    if (tab) {
      agentTab.value = tab; // Set as an object for small screens
    }
  } else {
    // Ensure agentTab is always a number on large screens
    if (typeof agentTab.value === "object") {
      agentTab.value = agentTab.value.id;
    }
  }
});

const props = defineProps<{
  customer: Customer;
  isCustomer: boolean;
}>();

watch(
  () => isOnboarding.value,
  async (newVal) => {
    if (newVal) {
      // Perform actions when isDeleted changes
      hasOnboarding.value = newVal
    }
  }
);

const componentMap: any = {
  0: CustomerOnboardingStep1,
  1: CustomerOnboardingStep2,
  2: CustomerOnboardingStep3,
  3: CustomerOnboardingStep4,
};

const isEditing = computed(() => props.isCustomer);

// Load existing onboarding data
async function loadOnboardingData() {
  const { data, error } = await supabase
    .from("customer_onboarding")
    .select("*")
    .eq("customer_id", props.customer.customerId)
    .single();

  if (error) {
    // error is normal when there is no onboarding
    return;
  }

  if (data) {
    existingOnboardingData.value = data;
  }
}

onMounted(loadOnboardingData);
</script>
