import { supabaseClient, TEMPLATES } from '../shared/common.ts';
import { SupabaseClient } from 'jsr:@supabase/supabase-js@2';
import { Database } from '../lib/database.types.ts';

import { handleError, Postmark, template } from '../shared/common.ts';
import { LivePostmark } from '../shared/common.ts';


console.log("Customer Created Event Handler booting");

export function startDeno() {
  // just used by tests to start Deno.serve()
}

type Record = {
  id: string;
  email: string;
  name: string;
  referred_by_id: string;
  affiliate_id: string;
}

type InsertPayload = {
  record: Record;
}



Deno.serve(async (req: Request) => {

  console.log("handling customer created request");

  if (req.method !== "POST") {
    return new Response("Invalid method", { status: 405 });
  }
  
  const supabase = supabaseClient();
  const payload: InsertPayload = JSON.parse(decodeURI(await req.text()) );
  console.log("payload", payload);
  
  await notifyAffiliate(payload, supabase);

  return new Response("customer created event handled")
})

/**
 * When a customer signs up with an affiliate link, notify the affiliate.
 */
async function notifyAffiliate(payload: InsertPayload, supabase: SupabaseClient<Database>) {

  if (payload.record.referred_by_id) {
    const {data: customer, error} = await supabase
        .from("customers")
        .select("*")
        .eq("affiliate_id", payload.record.referred_by_id)
        .single();

    if (error) {
      console.error("could not fetch customer", error);
      handleError(error);
    }

    if (!customer) {
      console.warn(`invalid affiliate id: ${payload.record.referred_by_id}`)
    } else {

      console.log("singup was through affiliate", payload.record.referred_by_id);
      const data = {
        name: customer.name,
        trackingId: payload.record.affiliate_id
      }
      const body = await template(TEMPLATES.affiliateNotification, data)
      console.log("notification:", body);

      await (getPostmark().sendEmail( 
        { to: customer.email,
          subject: "Your referral signed up for Applysquad",
          body: body }));
    }
  } else {
    console.log("customer signup was not a referral");
  }
}


// this is to allow the test to set a mock
let postmarkClient: Postmark;
export function setPostmark(client: Postmark) {
  postmarkClient = client;
}
function getPostmark() {
  if (!postmarkClient) {
    postmarkClient = new LivePostmark();
  }
  return postmarkClient;
}


