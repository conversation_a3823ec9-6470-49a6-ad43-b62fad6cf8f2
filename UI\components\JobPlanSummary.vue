<template>
  <div v-if="plan">
    <slot name="header" :plan="plan" :customer="customer" />
    <slot name="summary" :summary="summary" :plan="plan" />
    <slot name="statistics" :statistics="statistics" />
  </div>
  <div v-else>
    No plan purchased
    <slot name="no-plan" :is-customer="isCustomer" />
  </div>
</template>

<script lang="ts" setup>
import {
  type Plan,
  type CustomerJob,
  type Customer,
  PlanStatus,
  PRODUCT_CODE,
  CustomerJobStatus,
} from "~/common/common";
import { fetchJobPlan } from "~/common/customer-service";
import type { Database } from "~/lib/applysquad-common/database.types";

const props = defineProps({
  isCustomer: Boolean,
});
const customer = defineModel<Customer>("customer");
const customerJobs = defineModel<CustomerJob[]>("customerJobs");

watch(
  customerJobs,
  () => {
    if (!customerJobs.value) return;
    loadSummary(customerJobs.value!);
  },
  { deep: true }
);
const supabase = useSupabaseClient<Database>();
const plan = ref<Plan | undefined>(
  await fetchJobPlan(customer.value!.customerId, supabase)
);
const summary = ref({
  applied: 0,
  approved: 0,
  declined: 0,
  awaitingApproval: 0,
  customerApply: 0,
});

const statistics = ref([
  { label: "Plan Applications", number: 0 },
  { label: "Approved", number: 0 },
  { label: "Approved", number: 0 },
  { label: "Applied", number: 0 },
  { label: "Awaiting Approval", number: 0 },
  { label: "Declined", number: 0 },
  { label: "Outstanding", number: 0 },
]);

async function loadSummary(jobs: CustomerJob[]) {
  const applied = jobs.filter(
    (job) =>
      !job.customerApply &&
      [CustomerJobStatus.APPLIED, CustomerJobStatus.SATISFIED].includes(
        job.status
      )
  ).length;

  const approved = jobs.filter(
    (job) =>
      !job.customerApply && [CustomerJobStatus.APPROVED].includes(job.status)
  ).length;

  const declined = jobs.filter((job) =>
    [CustomerJobStatus.DECLINED, CustomerJobStatus.DISSATISFIED].includes(
      job.status
    )
  ).length;

  const newCount = jobs.filter((job) =>
    [CustomerJobStatus.NEW].includes(job.status)
  ).length;

  const customerApply = jobs.filter(
    (job) =>
      [ CustomerJobStatus.CUSTOMER_APPLIED ].includes(job.status)
  ).length;

  summary.value = {
    applied: applied,
    approved: approved,
    declined: declined,
    awaitingApproval: newCount,
    customerApply: customerApply,
  };

  statistics.value = [
    {
      label: "Plan Applications",
      number: plan.value?.workPromised || 0,
    },
    { label: "Awaiting Approval", number: summary.value.awaitingApproval },
    { label: "Applied", number: summary.value.applied },
    { label: "Linkedin EA", number: summary.value.customerApply},
    { label: "Approved", number: summary.value.approved },
    { label: "Declined", number: summary.value.declined },
    {
      label: "Outstanding",
      number:
        (plan.value?.workPromised || 0) -
        (summary.value.applied + summary.value.approved + summary.value.awaitingApproval),
    },
  ];
}

loadSummary(customerJobs.value!);

watch(customerJobs, async () => {
  loadSummary(customerJobs.value!);
});
</script>

<style scoped>
/* Add any styles needed for the shared component */
</style>
