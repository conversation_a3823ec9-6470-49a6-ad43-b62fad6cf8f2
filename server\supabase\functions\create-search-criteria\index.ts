// import { createStopwatch } from "jsr:@117/stopwatch";
import { getCustomerWithActiveJobPlans, getSearchableOnboardingSurvey, getRelevantJobBoards, LiveQstash, Events, supabaseClient, toDate, dateStr, getExistingSearchCriteria } from '../shared/common.ts';
import { TOPICS } from '../shared/common.ts';
import { JobBoard, CustomerSearchCriteria } from '../shared/common.ts';
import { CustomerSearchCriteriaCreated } from '../shared/common.ts';
import { v4 as uuid } from 'npm:uuid';


console.log("Create Search Criteria Event Handler booting");

export function startDeno() {
  // just used by tests to start Deno.serve()
}

Deno.serve(async (req: Request) => {

  const body = await req.text()
  console.log("handling create search criteria request, reqeust", body);
  const searchDate = getSearchDate(body);
  console.error("Auto Job-Search for search date ", searchDate);

  // for every customer with active plan, get their onboarding
  const supabase = supabaseClient();
  const customers = await getCustomerWithActiveJobPlans(supabase);
  const searchCriteria:CustomerSearchCriteria[] = (await Promise.all(customers.flatMap(async (customer) => {
    
    const onboarding = await getSearchableOnboardingSurvey(customer.id, supabase);

    if (!onboarding) {
      // TODO:let customer know we can't search for them yet
      console.log(`no onboarding, no search for customer ${customer.email}`);
      return [];
    } else {

      // for each relevant job board, create search criteria
      const jobBoards:JobBoard[] = await getRelevantJobBoards(onboarding, supabase);

      const searchCriteria:CustomerSearchCriteria[] = (await Promise.all(jobBoards.map(async (board) => {
        
        const existing = await getExistingSearchCriteria(customer.id, searchDate, board.id, supabase);
        if (existing) {
          // there is existing search criteria so use it
          return existing;
        } else {
          return {
            id: uuid(),
            search_date: dateStr(searchDate),
            job_board_key: board.key,
            customer_id: customer.id,
            job_titles: onboarding.jobTitles,
            locations: onboarding.locations,
            search_status: "NEW"
          }
        }
    })));
      
    return searchCriteria;
    }

  }))).flat(2);

  // save search criteria
  const { error } = await supabase
    .from("customer_search_criteria")
    .upsert( searchCriteria );

  if (error) {
    console.error("could not save search criteria:", error);
    throw error;
  }

  // publish events
  const events:CustomerSearchCriteriaCreated[] = searchCriteria
    .map((sc) => ({ customerSearchCriteriaId: sc.id }));
  
  const topic = Deno.env.get(TOPICS.SEARCH_JOB_BOARD)!;
  await getEvents().publishCustSearchCriteriaCreated(events, topic);

  console.log("sent batch search criterias to qstash");

  return new Response("created search criterias");
});

/**
 * 
 * @param req expect JSON body: { searchDate: "2025-5-21" }.
 *            If null, assume today.
 */
function getSearchDate(body:string): Date {
  const json = JSON.parse(body);
  if (json.searchDate) {
    return toDate(json.searchDate);
  } else {
    return new Date();
  }   
}

// this is to allow the test to set a mock
let qstash: Events;
export function setEvents(client: Events) {
  qstash = client;
}
function getEvents() {
  if (!qstash) {
    qstash = new LiveQstash();
  }
  return qstash;
}
