# Search Job Board Function

## Overview

The `search-job-board` function is a Supabase Edge Function that processes job search requests from customers by searching external job boards (like TheirStack) and managing the results in the database. It's part of the ApplySquad automated job application system.

## Purpose

This function serves as the core job discovery engine that:

1. **Receives search requests** via Qstash webhooks containing customer search criteria
2. **Searches external job boards** using the customer's preferences (job titles, locations)
3. **Saves new jobs** to the database with deduplication based on URL
4. **Creates customer-job relationships** for relevant matches
5. **Publishes events** for downstream processing (notifications, applications, etc.)

## Architecture

```
Qstash Webhook → search-job-board → TheirStack API → Database → Event Publishing
```

### Key Components

- **Main Handler**: `handleSearchJobBoardRequest()` - Orchestrates the entire flow
- **Job Board Interface**: `JobBoardSearch` - Abstraction for different job board APIs
- **TheirStack Integration**: `TheirStackJobBoard` - Specific implementation for TheirStack.com
- **Database Operations**: `saveJobIfNotExists()`, `checkCustomerJobExists()`
- **Event Publishing**: Publishes `SearchJobBoardsFound` events via Qstash

## Data Flow

### 1. Input Processing
- Receives Qstash webhook with `customerSearchCriteriaId`
- Validates request signature for security
- Fetches customer search criteria from database

### 2. Job Search
- Translates customer criteria to job board specific format
- Calls external job board API (TheirStack)
- Transforms response to internal Job format

### 3. Job Storage
- Saves jobs to `jobs` table with URL-based deduplication
- Handles errors gracefully, continuing with other jobs if one fails
- Tracks creation source as 'AGENT'

### 4. Relationship Management
- Checks existing `customer_jobs` relationships
- Identifies new job matches for the customer
- Prepares events for new matches only

### 5. Event Publishing
- Publishes `SearchJobBoardsFound` events to Qstash
- Events trigger downstream processing (match-customer-job function)

## Database Schema

### Tables Used

#### `customer_search_criteria`
```sql
- id: uuid (primary key)
- customer_id: uuid (foreign key to customers)
- job_board_key: text (e.g., 'THEIR_STACK')
- job_titles: varchar[] (array of job titles to search)
- locations: varchar[] (array of locations to search)
- search_date: date
- search_status: enum ('NEW', 'COMPLETE')
```

#### `jobs`
```sql
- id: text (primary key, format: 'theirstack-{external_id}')
- url: text (unique constraint for deduplication)
- title: text
- employer: text
- location: text
- description: text
- pay_amount: number
- pay_frequency: text
- pay_currency: text
- languages: text
- visa_required: text ('YES'/'NO')
- job_type: enum ('FULLTIME', 'PARTTIME', 'CONTRACT', etc.)
- job_status: enum ('OPEN', 'CLOSED')
- account_required: boolean
- customer_apply: boolean
- create_src: enum ('AGENT')
```

#### `customer_jobs`
```sql
- id: number (primary key)
- customer_id: uuid (foreign key to customers)
- job_id: text (foreign key to jobs)
- status: enum ('NEW', 'APPROVED', 'DECLINED', etc.)
```

## External Integrations

### TheirStack API
- **Endpoint**: `https://api.theirstack.com/v1/jobs/search`
- **Authentication**: Bearer token via `THEIRSTACK_API_KEY`
- **Rate Limiting**: Controlled via `posted_at_max_age_days` parameter
- **Response Format**: JSON with job listings and metadata

### Qstash Integration
- **Input**: Receives webhooks with search criteria IDs
- **Output**: Publishes `SearchJobBoardsFound` events
- **Security**: Validates signatures using signing keys
- **Topics**: Uses `MATCH_CUSTOMER_JOB` topic for event routing

## Configuration

### Environment Variables
```bash
THEIRSTACK_API_KEY=your_api_key_here
QSTASH_CURRENT_SIGNING_KEY=current_key
QSTASH_NEXT_SIGNING_KEY=next_key
QSTASH_KEY=qstash_token
QSTASH_URL=qstash_base_url
MATCH_CUSTOMER_JOB=topic_name
SUPABASE_URL=your_supabase_url
OUR_FUNCTION_KEY=supabase_service_key
```

## Error Handling

### Graceful Degradation
- Individual job save failures don't stop processing
- Continues with remaining jobs if one fails
- Logs errors for debugging while maintaining flow

### Validation
- Qstash signature validation prevents unauthorized requests
- Database constraints prevent duplicate jobs (URL uniqueness)
- Type safety through TypeScript interfaces

## Performance Considerations

### Deduplication Strategy
- URL-based uniqueness prevents duplicate job storage
- Efficient database queries using indexes
- Batch processing for multiple jobs

### Rate Limiting
- TheirStack API calls limited by `posted_at_max_age_days`
- Typically set to 1 day for regular searches
- Higher values for initial customer searches

## Testing

### Test Structure
- **Unit Tests**: Individual function testing with real database
- **Integration Tests**: Full flow testing with mock external APIs
- **Mock Strategy**: Mock TheirStack API, use real Supabase database
- **Test Data**: JSON files for consistent test scenarios

### Key Test Scenarios
- New job discovery and storage
- Duplicate job handling
- Customer-job relationship creation
- Event publishing verification
- Error recovery and graceful degradation

## Monitoring & Observability

### Logging
- Request processing stages
- Job search results and counts
- Database operation outcomes
- Event publishing confirmation

### Metrics to Monitor
- Job discovery rate per search
- Duplicate job percentage
- API response times
- Error rates by component

## Security

### Authentication
- Qstash webhook signature validation
- Supabase service key authentication
- TheirStack API key protection

### Data Protection
- No sensitive customer data in logs
- Secure environment variable handling
- Database access through service roles

## Future Enhancements

### Planned Features
- Additional job board integrations
- Advanced job matching algorithms
- Real-time job notifications
- Enhanced deduplication logic

### Scalability Considerations
- Horizontal scaling via Supabase Edge Functions
- Database connection pooling
- Async processing for large job batches
