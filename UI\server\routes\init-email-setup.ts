import { serverSupabaseClient } from '#supabase/server'
import type { Database } from '~/lib/applysquad-common/database.types';
import Nylas from "nylas";

export default defineEventHandler(async (event) => {
    
    const customerId = getQuery(event).customerId;
    if (!customerId) {
      throw new Error("no customer id")
    }

    const config = useRuntimeConfig(event);
    const nylasKey = config.public.NYLAS_API_KEY;
    const nylasUrl = config.public.NYLAS_API_URL
    const nylasClientId = config.public.NYLAS_CLIENT_ID;
    const siteUrl = config.public.SITE_URL;
    const redirect = encodeURI(siteUrl + config.public.EMAIL_SETUP_CALLBACK_URL);

    const nylas = new Nylas({
      apiKey: nylasKey,
      apiUri: nylasUrl
    });

    const client = await serverSupabaseClient<Database>(event)
    const { error } = await client.from("nylas_customers")
      .upsert(
        { "customer_id": customerId!.toString()  },
        { onConflict : "customer_id", ignoreDuplicates: true}
      )
    if (error) {
      console.error('could not start email setup ', error)
      throw error
    }

    try {

      const url = nylas.auth.urlForOAuth2({
        clientId: nylasClientId,
        redirectUri: redirect,
        state: `${customerId}`
      });
    
      sendRedirect(event, url);

    } catch (error) {
      console.log('could not fetch nylas auth url', error)
      throw error;
    }

  });