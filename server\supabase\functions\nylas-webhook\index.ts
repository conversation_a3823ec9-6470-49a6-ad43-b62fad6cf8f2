import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { supabaseClient, handleError } from '../shared/common.ts'
import { type Database } from '../lib/database.types.ts'
import { SupabaseClient } from 'jsr:@supabase/supabase-js@2';

console.log("Nylas webhook function booting")

interface GrantExpiredEventDto {
    id: string,
    grant: string
}


export function startDeno() {
  // just used by tests to start Deno.serve()
}


Deno.serve(async (req: Request) => {
    console.log("executing nylas event function")
    if (req.method === "GET") {
        const challenge = new URL(req.url).searchParams.get("challenge");
        console.log("challenge", challenge);
        return new Response(challenge);
    } else if (req.method!== "POST") {
      return new Response("Invalid method", { status: 405 })
    }

    try {
        checkNylasKey(req);
    } catch (e) {
        console.error('invalid nylas key', e);
        return new Response("Invalid Nylas key", { status: 401 });
    }

    const body = await req.text()
    const grantExpired = parseRawEvent(body)

    if (grantExpired) {
        console.log(`processing nylas grant expired event: ${grantExpired.id}`);
    
        const supabase = supabaseClient();
        await updateGrantExpired(grantExpired, supabase);
        console.log(`Nylas-Grant-Expired processed successfully for ${grantExpired.grant}`)
    
        return new Response(
          `thank you for your message`,
          { headers: { "Content-Type": "text/plain" } },
        )
    } else {
        return new Response("Invalid event", { status: 400 })
    }
})

function checkNylasKey(req: Request) {
    let nylasKey = req.headers.get("x-nylas-signature");
    if (!nylasKey) {
        nylasKey = req.headers.get("X-Nylas-Signature");
    }
    if (nylasKey !== Deno.env.get("NYLAS_WEBHOOK_KEY")) {
        throw new Error("Invalid Nylas key");
    }
}

/**
 * Parse the raw Nylas event payload into a Grant Expired object.
 * @param rawEvent 
 * @returns 
 */
export function parseRawEvent(rawEvent: string): GrantExpiredEventDto|undefined {
    const parsedEvent = JSON.parse(rawEvent);
  
    // need to check this at some point!
    if (parsedEvent.type !== 'grant.expired') {
      console.error(`Invalid event type: ${parsedEvent.type}`);
      return undefined;
    }
  
    const eventId = parsedEvent.id;
    const grantId = parsedEvent.data.object.grant_id;
    return { id: eventId, grant: grantId };
}

async function updateGrantExpired(grantExpired: GrantExpiredEventDto, supabase: SupabaseClient<Database>) {
 
    const {error} = await supabase
      .from("nylas_customers")
      .update({status: "EXPIRED"})
      .eq("grant", grantExpired.grant)
  
    if (error) {
      handleError(error)
    }
  
    console.log(`updated grant ${grantExpired.grant} to expired`)
}