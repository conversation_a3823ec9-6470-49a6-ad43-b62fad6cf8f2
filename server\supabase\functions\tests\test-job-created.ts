
import { startDeno } from "../job-created/index.ts";
import { load } from 'jsr:@std/dotenv';
import { supabaseClient } from '../shared/common.ts';
import { type Database } from '../lib/database.types.ts';
import { SupabaseClient } from 'jsr:@supabase/supabase-js@2';
import { assertNotEquals } from 'jsr:@std/assert';
import { v4 as _uuid } from 'npm:uuid';
import { post } from './test-common.ts';


/**
 * This test costs money to run, so it should only be run as needed.
 */
Deno.test("integration test",
  async () => {
    await load({ export:true });
    startDeno()
    const supabase = supabaseClient();

    const jobId = "ddf6696b-8c9f-4390-9335-e53333bb7847"; //_uuid();
    const eventPayload = Deno
      .readTextFileSync("./test-job-created-event.json")
      .replace("@@JOB_ID@@", jobId);
    // console.log("sending payload " + eventPayload);
    const description = JSON.parse(eventPayload).record.description;
    //console.log("description:", description);
    await saveJob(jobId, description, supabase);

    await post(eventPayload);
    
    await assertSummaryComplete(jobId, supabase);

})


async function saveJob(jobId: string, description: string, supabase: SupabaseClient<Database>) {
  const { error } = await supabase
    .from("jobs")
    .upsert(
      {id: jobId, description: description, url: "some.url", location: "paris", employer: "me", title: "boss" },
      { onConflict: "id", ignoreDuplicates: true }
    )

  if (error) {
    throw error;
  }
  }

async function assertSummaryComplete(jobId: string, supabase: SupabaseClient<Database>) {
  const { data, error } = await supabase
    .from("jobs")
    .select("summary")
    .eq("id", jobId)
    .single();

  if (error) {
    throw error;
  }
  console.log("saved summary", data.summary);

  assertNotEquals(data.summary, null);
}

