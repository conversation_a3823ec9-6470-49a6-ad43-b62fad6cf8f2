export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      admins: {
        Row: {
          created_at: string
          id: number
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: number
          user_id?: string
        }
        Update: {
          created_at?: string
          id?: number
          user_id?: string
        }
        Relationships: []
      }
      agent_customers: {
        Row: {
          active: boolean
          agent_id: string
          created_at: string
          customer_id: string
        }
        Insert: {
          active?: boolean
          agent_id?: string
          created_at?: string
          customer_id?: string
        }
        Update: {
          active?: boolean
          agent_id?: string
          created_at?: string
          customer_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "agent_customers_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agent_customers_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      agents: {
        Row: {
          active: boolean
          country: string | null
          created_at: string
          date_of_birth: string
          email: string
          gender: string
          id: string
          linkedin_url: string | null
          name: string
          upwork_url: string | null
          whatsapp_number: string
        }
        Insert: {
          active?: boolean
          country?: string | null
          created_at?: string
          date_of_birth: string
          email: string
          gender: string
          id?: string
          linkedin_url?: string | null
          name: string
          upwork_url?: string | null
          whatsapp_number: string
        }
        Update: {
          active?: boolean
          country?: string | null
          created_at?: string
          date_of_birth?: string
          email?: string
          gender?: string
          id?: string
          linkedin_url?: string | null
          name?: string
          upwork_url?: string | null
          whatsapp_number?: string
        }
        Relationships: []
      }
      contacts: {
        Row: {
          company: string | null
          created_at: string
          description: string | null
          email: string | null
          id: string
          linkedin_url: string | null
          name: string
          phone: string | null
          title: string | null
        }
        Insert: {
          company?: string | null
          created_at?: string
          description?: string | null
          email?: string | null
          id?: string
          linkedin_url?: string | null
          name: string
          phone?: string | null
          title?: string | null
        }
        Update: {
          company?: string | null
          created_at?: string
          description?: string | null
          email?: string | null
          id?: string
          linkedin_url?: string | null
          name?: string
          phone?: string | null
          title?: string | null
        }
        Relationships: []
      }
      cover_letters: {
        Row: {
          created_at: string
          customer_job_id: number
          data: string
          id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string
          customer_job_id: number
          data: string
          id?: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string
          customer_job_id?: number
          data?: string
          id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "cover_letters_customer_job_id_fkey"
            columns: ["customer_job_id"]
            isOneToOne: false
            referencedRelation: "customer_jobs"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_contacts: {
        Row: {
          agent_notes: string | null
          contact_id: string | null
          created_at: string
          customer_id: string
          customer_notes: string | null
          hours: number | null
          id: number
          message: string | null
          src_id: string
          status: Database["public"]["Enums"]["contact_status"]
        }
        Insert: {
          agent_notes?: string | null
          contact_id?: string | null
          created_at?: string
          customer_id?: string
          customer_notes?: string | null
          hours?: number | null
          id?: number
          message?: string | null
          src_id: string
          status?: Database["public"]["Enums"]["contact_status"]
        }
        Update: {
          agent_notes?: string | null
          contact_id?: string | null
          created_at?: string
          customer_id?: string
          customer_notes?: string | null
          hours?: number | null
          id?: number
          message?: string | null
          src_id?: string
          status?: Database["public"]["Enums"]["contact_status"]
        }
        Relationships: [
          {
            foreignKeyName: "customer_contacts_contact_id_fkey"
            columns: ["contact_id"]
            isOneToOne: false
            referencedRelation: "contacts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_contacts_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_jobs: {
        Row: {
          agent_id: string | null
          agent_notes: string | null
          cover_letter: string | null
          created_at: string
          customer_id: string
          customer_notes: string | null
          id: number
          job_id: string
          match_probability: number | null
          modified_by: string | null
          modified_time: string | null
          negative_match_reasons: string[] | null
          positive_match_reasons: string[] | null
          status: Database["public"]["Enums"]["customer_job_status"]
        }
        Insert: {
          agent_id?: string | null
          agent_notes?: string | null
          cover_letter?: string | null
          created_at?: string
          customer_id?: string
          customer_notes?: string | null
          id?: number
          job_id?: string
          match_probability?: number | null
          modified_by?: string | null
          modified_time?: string | null
          negative_match_reasons?: string[] | null
          positive_match_reasons?: string[] | null
          status?: Database["public"]["Enums"]["customer_job_status"]
        }
        Update: {
          agent_id?: string | null
          agent_notes?: string | null
          cover_letter?: string | null
          created_at?: string
          customer_id?: string
          customer_notes?: string | null
          id?: number
          job_id?: string
          match_probability?: number | null
          modified_by?: string | null
          modified_time?: string | null
          negative_match_reasons?: string[] | null
          positive_match_reasons?: string[] | null
          status?: Database["public"]["Enums"]["customer_job_status"]
        }
        Relationships: [
          {
            foreignKeyName: "customer_jobs_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_jobs_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_jobs_job_id_fkey"
            columns: ["job_id"]
            isOneToOne: false
            referencedRelation: "jobs"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_jobs_audit: {
        Row: {
          current_customer_job_status: string
          customer_job_id: number
          id: string
          modified_by: string | null
          modified_time: string
          new_customer_job_status: string
        }
        Insert: {
          current_customer_job_status: string
          customer_job_id: number
          id?: string
          modified_by?: string | null
          modified_time?: string
          new_customer_job_status: string
        }
        Update: {
          current_customer_job_status?: string
          customer_job_id?: number
          id?: string
          modified_by?: string | null
          modified_time?: string
          new_customer_job_status?: string
        }
        Relationships: [
          {
            foreignKeyName: "customer_jobs_audit_customer_job_id_fkey"
            columns: ["customer_job_id"]
            isOneToOne: false
            referencedRelation: "customer_jobs"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_onboarding: {
        Row: {
          authorized_countries: string[] | null
          company_excludes: string | null
          company_size: string[] | null
          countries: string | null
          created_at: string
          current_address: string | null
          customer_id: string
          date_of_birth: string | null
          functional_preferences: string[] | null
          gender: string | null
          id: number
          industries: string | null
          industry_excludes: string | null
          job_titles: string | null
          job_types: Database["public"]["Enums"]["job_type"][] | null
          keywords: string | null
          languages_fluent: string[] | null
          languages_learning: string[] | null
          nationality: string | null
          other_info: string | null
          regions: string[] | null
          role_excludes: string | null
          salary_requirements: string | null
          seniority: string[] | null
          university: string | null
          visa_sponsorship: boolean | null
          workpalce_type: Database["public"]["Enums"]["workplace_type"][] | null
        }
        Insert: {
          authorized_countries?: string[] | null
          company_excludes?: string | null
          company_size?: string[] | null
          countries?: string | null
          created_at?: string
          current_address?: string | null
          customer_id?: string
          date_of_birth?: string | null
          functional_preferences?: string[] | null
          gender?: string | null
          id?: number
          industries?: string | null
          industry_excludes?: string | null
          job_titles?: string | null
          job_types?: Database["public"]["Enums"]["job_type"][] | null
          keywords?: string | null
          languages_fluent?: string[] | null
          languages_learning?: string[] | null
          nationality?: string | null
          other_info?: string | null
          regions?: string[] | null
          role_excludes?: string | null
          salary_requirements?: string | null
          seniority?: string[] | null
          university?: string | null
          visa_sponsorship?: boolean | null
          workpalce_type?:
            | Database["public"]["Enums"]["workplace_type"][]
            | null
        }
        Update: {
          authorized_countries?: string[] | null
          company_excludes?: string | null
          company_size?: string[] | null
          countries?: string | null
          created_at?: string
          current_address?: string | null
          customer_id?: string
          date_of_birth?: string | null
          functional_preferences?: string[] | null
          gender?: string | null
          id?: number
          industries?: string | null
          industry_excludes?: string | null
          job_titles?: string | null
          job_types?: Database["public"]["Enums"]["job_type"][] | null
          keywords?: string | null
          languages_fluent?: string[] | null
          languages_learning?: string[] | null
          nationality?: string | null
          other_info?: string | null
          regions?: string[] | null
          role_excludes?: string | null
          salary_requirements?: string | null
          seniority?: string[] | null
          university?: string | null
          visa_sponsorship?: boolean | null
          workpalce_type?:
            | Database["public"]["Enums"]["workplace_type"][]
            | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_onboarding_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: true
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_search_criteria: {
        Row: {
          created_at: string
          customer_id: string
          id: string
          job_board_key: string
          job_titles: string[]
          locations: string[]
          search_date: string
          search_status: Database["public"]["Enums"]["search_status"]
        }
        Insert: {
          created_at?: string
          customer_id?: string
          id?: string
          job_board_key?: string
          job_titles: string[]
          locations: string[]
          search_date: string
          search_status?: Database["public"]["Enums"]["search_status"]
        }
        Update: {
          created_at?: string
          customer_id?: string
          id?: string
          job_board_key?: string
          job_titles?: string[]
          locations?: string[]
          search_date?: string
          search_status?: Database["public"]["Enums"]["search_status"]
        }
        Relationships: [
          {
            foreignKeyName: "customer_search_criteria_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_search_criteria_job_board_key_fkey"
            columns: ["job_board_key"]
            isOneToOne: false
            referencedRelation: "job_boards"
            referencedColumns: ["key"]
          },
        ]
      }
      customers: {
        Row: {
          affiliate_id: string | null
          agent_notes: string | null
          auto_search: boolean | null
          created_at: string
          email: string
          id: string
          linkedin_premium: boolean | null
          linkedin_url: string | null
          name: string
          onboarding_complete: boolean
          orientation_status: Database["public"]["Enums"]["customer_orientation_status"]
          other_pain_points: string | null
          pain_points: string | null
          referred_by: string | null
          referred_by_id: string | null
          status: Database["public"]["Enums"]["customer_status"]
          whatsapp_group_url: string | null
          whatsapp_number: string | null
        }
        Insert: {
          affiliate_id?: string | null
          agent_notes?: string | null
          auto_search?: boolean | null
          created_at?: string
          email: string
          id?: string
          linkedin_premium?: boolean | null
          linkedin_url?: string | null
          name: string
          onboarding_complete?: boolean
          orientation_status?: Database["public"]["Enums"]["customer_orientation_status"]
          other_pain_points?: string | null
          pain_points?: string | null
          referred_by?: string | null
          referred_by_id?: string | null
          status?: Database["public"]["Enums"]["customer_status"]
          whatsapp_group_url?: string | null
          whatsapp_number?: string | null
        }
        Update: {
          affiliate_id?: string | null
          agent_notes?: string | null
          auto_search?: boolean | null
          created_at?: string
          email?: string
          id?: string
          linkedin_premium?: boolean | null
          linkedin_url?: string | null
          name?: string
          onboarding_complete?: boolean
          orientation_status?: Database["public"]["Enums"]["customer_orientation_status"]
          other_pain_points?: string | null
          pain_points?: string | null
          referred_by?: string | null
          referred_by_id?: string | null
          status?: Database["public"]["Enums"]["customer_status"]
          whatsapp_group_url?: string | null
          whatsapp_number?: string | null
        }
        Relationships: []
      }
      documents: {
        Row: {
          active: boolean
          bucket: string
          created_at: string
          customer_id: string
          document_type: Database["public"]["Enums"]["document_type"]
          file_name: string
          id: string
          path: string
          source: Database["public"]["Enums"]["storage_source"]
        }
        Insert: {
          active?: boolean
          bucket: string
          created_at?: string
          customer_id?: string
          document_type: Database["public"]["Enums"]["document_type"]
          file_name: string
          id?: string
          path: string
          source?: Database["public"]["Enums"]["storage_source"]
        }
        Update: {
          active?: boolean
          bucket?: string
          created_at?: string
          customer_id?: string
          document_type?: Database["public"]["Enums"]["document_type"]
          file_name?: string
          id?: string
          path?: string
          source?: Database["public"]["Enums"]["storage_source"]
        }
        Relationships: [
          {
            foreignKeyName: "documents_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      job_boards: {
        Row: {
          created_at: string
          id: string
          key: string
          name: string
          url: string
        }
        Insert: {
          created_at?: string
          id?: string
          key: string
          name: string
          url: string
        }
        Update: {
          created_at?: string
          id?: string
          key?: string
          name?: string
          url?: string
        }
        Relationships: []
      }
      job_documents: {
        Row: {
          created_at: string
          customer_id: string
          customer_job_id: number
          document_id: string
          document_type: Database["public"]["Enums"]["document_type"]
          id: number
        }
        Insert: {
          created_at?: string
          customer_id?: string
          customer_job_id: number
          document_id?: string
          document_type?: Database["public"]["Enums"]["document_type"]
          id?: number
        }
        Update: {
          created_at?: string
          customer_id?: string
          customer_job_id?: number
          document_id?: string
          document_type?: Database["public"]["Enums"]["document_type"]
          id?: number
        }
        Relationships: [
          {
            foreignKeyName: "job_documents_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "job_documents_customer_job_id_fkey"
            columns: ["customer_job_id"]
            isOneToOne: false
            referencedRelation: "customer_jobs"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "job_documents_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
        ]
      }
      jobs: {
        Row: {
          account_required: boolean | null
          create_src: Database["public"]["Enums"]["job_create_src"]
          created_at: string
          customer_apply: boolean
          description: string
          employer: string
          id: string
          job_status: Database["public"]["Enums"]["job_status"]
          job_type: Database["public"]["Enums"]["job_type"]
          languages: string | null
          location: string
          modified_by: string | null
          modified_time: string | null
          pay_amount: number | null
          pay_currency: string | null
          pay_frequency: string | null
          src_id: string | null
          summary: string | null
          title: string
          url: string
          visa_required: string | null
        }
        Insert: {
          account_required?: boolean | null
          create_src?: Database["public"]["Enums"]["job_create_src"]
          created_at?: string
          customer_apply?: boolean
          description: string
          employer: string
          id?: string
          job_status?: Database["public"]["Enums"]["job_status"]
          job_type?: Database["public"]["Enums"]["job_type"]
          languages?: string | null
          location: string
          modified_by?: string | null
          modified_time?: string | null
          pay_amount?: number | null
          pay_currency?: string | null
          pay_frequency?: string | null
          src_id?: string | null
          summary?: string | null
          title: string
          url: string
          visa_required?: string | null
        }
        Update: {
          account_required?: boolean | null
          create_src?: Database["public"]["Enums"]["job_create_src"]
          created_at?: string
          customer_apply?: boolean
          description?: string
          employer?: string
          id?: string
          job_status?: Database["public"]["Enums"]["job_status"]
          job_type?: Database["public"]["Enums"]["job_type"]
          languages?: string | null
          location?: string
          modified_by?: string | null
          modified_time?: string | null
          pay_amount?: number | null
          pay_currency?: string | null
          pay_frequency?: string | null
          src_id?: string | null
          summary?: string | null
          title?: string
          url?: string
          visa_required?: string | null
        }
        Relationships: []
      }
      jobs_audit: {
        Row: {
          current_job_status: string | null
          id: string
          job_id: string | null
          modified_by: string | null
          modified_time: string
          new_job_status: string | null
        }
        Insert: {
          current_job_status?: string | null
          id?: string
          job_id?: string | null
          modified_by?: string | null
          modified_time?: string
          new_job_status?: string | null
        }
        Update: {
          current_job_status?: string | null
          id?: string
          job_id?: string | null
          modified_by?: string | null
          modified_time?: string
          new_job_status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "jobs_audit_job_id_fkey"
            columns: ["job_id"]
            isOneToOne: false
            referencedRelation: "jobs"
            referencedColumns: ["id"]
          },
        ]
      }
      nylas_customers: {
        Row: {
          code: string | null
          created_at: string
          customer_id: string
          grant: string | null
          id: number
          status: Database["public"]["Enums"]["nylas_status"]
        }
        Insert: {
          code?: string | null
          created_at?: string
          customer_id?: string
          grant?: string | null
          id?: number
          status?: Database["public"]["Enums"]["nylas_status"]
        }
        Update: {
          code?: string | null
          created_at?: string
          customer_id?: string
          grant?: string | null
          id?: number
          status?: Database["public"]["Enums"]["nylas_status"]
        }
        Relationships: [
          {
            foreignKeyName: "nylas_customers_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: true
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      onboarding_locations: {
        Row: {
          created_at: string
          id: number
          location: string
          onboarding_id: number | null
          pay_requirement: string | null
          work_authorized: boolean
        }
        Insert: {
          created_at?: string
          id?: number
          location: string
          onboarding_id?: number | null
          pay_requirement?: string | null
          work_authorized?: boolean
        }
        Update: {
          created_at?: string
          id?: number
          location?: string
          onboarding_id?: number | null
          pay_requirement?: string | null
          work_authorized?: boolean
        }
        Relationships: [
          {
            foreignKeyName: "onboarding_locations_onboarding_id_fkey"
            columns: ["onboarding_id"]
            isOneToOne: false
            referencedRelation: "customer_onboarding"
            referencedColumns: ["id"]
          },
        ]
      }
      payments: {
        Row: {
          amount_pennies: number
          created_at: string
          email: string
          event_id: string
          id: number
          name: string | null
          product_code: Database["public"]["Enums"]["product_codes"]
        }
        Insert: {
          amount_pennies?: number
          created_at?: string
          email: string
          event_id: string
          id?: number
          name?: string | null
          product_code: Database["public"]["Enums"]["product_codes"]
        }
        Update: {
          amount_pennies?: number
          created_at?: string
          email?: string
          event_id?: string
          id?: number
          name?: string | null
          product_code?: Database["public"]["Enums"]["product_codes"]
        }
        Relationships: []
      }
      plans: {
        Row: {
          created_at: string
          customer_id: string
          id: number
          payment_id: number
          product_id: number
          status: Database["public"]["Enums"]["order_status"]
          work_promised: number
        }
        Insert: {
          created_at?: string
          customer_id?: string
          id?: number
          payment_id: number
          product_id: number
          status?: Database["public"]["Enums"]["order_status"]
          work_promised: number
        }
        Update: {
          created_at?: string
          customer_id?: string
          id?: number
          payment_id?: number
          product_id?: number
          status?: Database["public"]["Enums"]["order_status"]
          work_promised?: number
        }
        Relationships: [
          {
            foreignKeyName: "orders_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_payment_id_fkey"
            columns: ["payment_id"]
            isOneToOne: true
            referencedRelation: "payments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      products: {
        Row: {
          created_at: string
          id: number
          label: string
          payment_link: string | null
          price_usd_pennies: number
          product_code: Database["public"]["Enums"]["product_codes"]
          status: Database["public"]["Enums"]["product_status"]
          work_promised: number
        }
        Insert: {
          created_at?: string
          id?: number
          label: string
          payment_link?: string | null
          price_usd_pennies: number
          product_code: Database["public"]["Enums"]["product_codes"]
          status?: Database["public"]["Enums"]["product_status"]
          work_promised: number
        }
        Update: {
          created_at?: string
          id?: number
          label?: string
          payment_link?: string | null
          price_usd_pennies?: number
          product_code?: Database["public"]["Enums"]["product_codes"]
          status?: Database["public"]["Enums"]["product_status"]
          work_promised?: number
        }
        Relationships: []
      }
      stats_daily_customer_jobs: {
        Row: {
          applied_customer_jobs: number
          approved_customer_jobs: number
          created_at: string
          customer_applied_customer_jobs: number
          date: string
          declined_customer_jobs: number
          deleted_customer_jobs: number
          dissatisfied_customer_jobs: number
          expired_customer_jobs: number
          id: string
          new_customer_jobs: number
          satisfied_customer_jobs: number
          total_customer_jobs: number
          total_customer_jobs_for_today: number
        }
        Insert: {
          applied_customer_jobs?: number
          approved_customer_jobs?: number
          created_at?: string
          customer_applied_customer_jobs?: number
          date: string
          declined_customer_jobs?: number
          deleted_customer_jobs?: number
          dissatisfied_customer_jobs?: number
          expired_customer_jobs?: number
          id?: string
          new_customer_jobs?: number
          satisfied_customer_jobs?: number
          total_customer_jobs?: number
          total_customer_jobs_for_today?: number
        }
        Update: {
          applied_customer_jobs?: number
          approved_customer_jobs?: number
          created_at?: string
          customer_applied_customer_jobs?: number
          date?: string
          declined_customer_jobs?: number
          deleted_customer_jobs?: number
          dissatisfied_customer_jobs?: number
          expired_customer_jobs?: number
          id?: string
          new_customer_jobs?: number
          satisfied_customer_jobs?: number
          total_customer_jobs?: number
          total_customer_jobs_for_today?: number
        }
        Relationships: []
      }
      stats_daily_customers: {
        Row: {
          created_at: string
          date: string
          id: string
          new_customers: number
          paid_customers: number
          total_customers: number
          total_customers_for_today: number
        }
        Insert: {
          created_at?: string
          date: string
          id?: string
          new_customers?: number
          paid_customers?: number
          total_customers?: number
          total_customers_for_today?: number
        }
        Update: {
          created_at?: string
          date?: string
          id?: string
          new_customers?: number
          paid_customers?: number
          total_customers?: number
          total_customers_for_today?: number
        }
        Relationships: []
      }
      stats_daily_jobs: {
        Row: {
          complete_jobs: number
          created_at: string
          date: string
          id: string
          open_jobs: number
          total_jobs: number
          total_jobs_for_today: number
        }
        Insert: {
          complete_jobs?: number
          created_at?: string
          date: string
          id?: string
          open_jobs?: number
          total_jobs?: number
          total_jobs_for_today?: number
        }
        Update: {
          complete_jobs?: number
          created_at?: string
          date?: string
          id?: string
          open_jobs?: number
          total_jobs?: number
          total_jobs_for_today?: number
        }
        Relationships: []
      }
      stats_daily_payments: {
        Row: {
          created_at: string
          date: string
          id: string
          payment_amount_today: number
          total_payment_amount: number
        }
        Insert: {
          created_at?: string
          date: string
          id?: string
          payment_amount_today?: number
          total_payment_amount?: number
        }
        Update: {
          created_at?: string
          date?: string
          id?: string
          payment_amount_today?: number
          total_payment_amount?: number
        }
        Relationships: []
      }
      stats_daily_plans: {
        Row: {
          complete_plans: number
          created_at: string
          date: string
          id: string
          open_plans: number
          total_plans: number
        }
        Insert: {
          complete_plans?: number
          created_at?: string
          date: string
          id?: string
          open_plans?: number
          total_plans?: number
        }
        Update: {
          complete_plans?: number
          created_at?: string
          date?: string
          id?: string
          open_plans?: number
          total_plans?: number
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      populate_stats_daily_customer_jobs: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      populate_stats_daily_customers: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      populate_stats_daily_jobs: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      populate_stats_daily_payments: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      populate_stats_daily_plans: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
    }
    Enums: {
      contact_status: "NEW" | "APPROVED" | "DECLINED" | "CONTACTED"
      customer_job_status:
        | "NEW"
        | "APPROVED"
        | "DECLINED"
        | "APPLIED"
        | "SATISFIED"
        | "DISSATISFIED"
        | "DELETED"
        | "CUSTOMER_APPLIED"
        | "EXPIRED"
      customer_orientation_status: "NEW" | "COMPLETE"
      customer_status: "PAID" | "NEW"
      document_type: "CV" | "COVER" | "SCREENSHOT"
      job_create_src: "AGENT"
      job_status: "OPEN" | "CLOSED"
      job_type:
        | "FULLTIME"
        | "PARTTIME"
        | "CONTRACT"
        | "INTERNSHIP"
        | "TEMPORARY"
      nylas_status:
        | "STARTED"
        | "AUTHENTICATED"
        | "READY"
        | "REVOKED"
        | "EXPIRED"
      order_status: "OPEN" | "COMPLETE"
      product_codes:
        | "APPS_20"
        | "APPS_50"
        | "APPS_100"
        | "OTHER"
        | "APPS_500"
        | "NETWORK_80"
        | "APPS_TRIAL_5"
        | "NETWORK_20"
      product_status: "ACTIVE" | "INACTIVE"
      search_status: "NEW" | "COMPLETE"
      storage_source: "SUPABASE"
      workplace_type: "ONSITE" | "REMOTE" | "HYBRID"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

