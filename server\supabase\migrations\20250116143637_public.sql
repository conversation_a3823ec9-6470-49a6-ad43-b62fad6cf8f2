drop policy "Enable all for authenticated users" on "public"."customer_onboarding";

CREATE INDEX idx_customer_onboarding_customer_id ON public.customer_onboarding USING btree (customer_id);

create policy "Customers can delete their own onboarding data"
on "public"."customer_onboarding"
as permissive
for delete
to authenticated
using (((auth.uid())::text = (customer_id)::text));


create policy "Customers can insert their own onboarding data"
on "public"."customer_onboarding"
as permissive
for insert
to authenticated
with check (((auth.uid())::text = (customer_id)::text));


create policy "Customers can update their own onboarding data"
on "public"."customer_onboarding"
as permissive
for update
to authenticated
using (((auth.uid())::text = (customer_id)::text))
with check (((auth.uid())::text = (customer_id)::text));


create policy "Customers can view their own onboarding data"
on "public"."customer_onboarding"
as permissive
for select
to authenticated
using (((auth.uid())::text = (customer_id)::text));



