alter type "public"."product_codes" rename to "product_codes__old_version_to_be_dropped";

create type "public"."product_codes" as enum ('APPS_20', 'APPS_50', 'APPS_100', 'OTHER', 'APPS_500', 'NETWORK_80', 'APPS_TRIAL_5', 'NETWORK_20');

alter table "public"."payments" alter column product_code type "public"."product_codes" using product_code::text::"public"."product_codes";

alter table "public"."products" alter column product_code type "public"."product_codes" using product_code::text::"public"."product_codes";

drop type "public"."product_codes__old_version_to_be_dropped";


