import { assertEquals, assertNotEquals } from "jsr:@std/assert";
import { parseRawEvent, setPostmark } from '../stripe-payment/index.ts'
import { startDeno } from '../stripe-payment/index.ts'
import {supabaseClient, PRODUCT_CODE } from '../shared/common.ts'
import { load } from 'jsr:@std/dotenv';
import { Database } from '../lib/database.types.ts'
import { SupabaseClient } from 'jsr:@supabase/supabase-js@2'
import { createCustomer, TestPostmark } from './test-common.ts'


    
const testEventData = { 
  id: "evt_3QsldxHx0xHIO08P0GSFqZ7v", 
  email: "<EMAIL>", 
  name:"Mr. Bigglesworth",
  product: PRODUCT_CODE.NETWORK_80,
  amount: 8000
}

interface PaymentData {
  id: number;
}

interface Plan {
  id: number,
  customerId: string,
  productId: number,
  paymentId: number,
}



Deno.test("ensures the payment event can be parsed", () => {
    load({ export:true });
    const testEvent = Deno.readTextFileSync('./test-payment-event.json');
    const payment = parseRawEvent(testEvent)!;
    console.log(`payment: ${payment}`)
    assertEquals(payment.event_id, testEventData.id, "event id was incorrect");
    assertEquals(payment.name, testEventData.name, "payment name was incorrect");
    assertEquals(payment.email, testEventData.email, "payment email was incorrect");
    assertEquals(payment.amount_in_pennies, testEventData.amount, "payment amount was incorrect");
    assertEquals(payment.product, testEventData.product, "payment product was incorrect");
});

Deno.test("integration test",
  async () => {
    await load({ export:true });
    startDeno()
    const postmark = new TestPostmark();
    setPostmark(postmark);
    const supabase = supabaseClient()
    await createCustomer(testEventData.email, supabase)

    await sendTestPayment('./test-payment-event.json')
    
    const payment = await assertPaymentSaved(supabase)
    const order = await getPlan(payment, supabase)
    console.log(`created order ${order}`)

    verifyNotification(postmark);
    assertNotEquals(order?.id, undefined)
    assertEquals(order!.paymentId, payment.id, "Order should have been created")

})

Deno.test("currency conversion integration test",
  async () => {
    await load({ export:true });
    startDeno();
    setPostmark(new TestPostmark());
    const supabase = supabaseClient()
    await createCustomer(testEventData.email, supabase)

    await sendTestPayment('./test-payment-eur-event.json')
    
    const payment = await assertPaymentSaved(supabase)
    const order = await getPlan(payment, supabase)
    console.log(`created order ${order}`)
    assertNotEquals(order?.id, undefined)
    assertEquals(order!.paymentId, payment.id, "Order should have been created")

})

 async function sendTestPayment(fileName: string) {
    const testEvent = await Deno.readTextFileSync(fileName);
    const request = new Request("http://0.0.0.0:8000/", {
        method: "POST",
        body: testEvent.toString(),
        headers: {
          "content-type": "application/json",
        },
      });
    const response = await fetch(request)
    console.log(`Response ${response.text()}`)
}

async function assertPaymentSaved(supabase: SupabaseClient<Database>): Promise<PaymentData> {
  
    const {data, error} = await supabase
      .from('payments')
      .select()
      .eq("event_id", testEventData.id);

    if (error) {
      console.log("Error occurred " + error.message)
    }
    
    console.log(`Payment data: ${JSON.stringify(data)}`)
    if (data && data.length > 0) {
        assertEquals(data[0].event_id, testEventData.id, "Payment not saved");
        assertEquals(data[0].name, testEventData.name, "Payment name not saved");
        assertEquals(data[0].email, testEventData.email, "Payment email not saved");
        assertEquals(data[0].amount_pennies, testEventData.amount, "Payment amount not saved");
        assertEquals(data[0].product_code, testEventData.product, "Payment product not saved");
    } else {
        throw new Error("Payment not found");
    }

    const payment: PaymentData = { id : data[0].id }
    return payment
}

async function getPlan(payment: PaymentData, supabase: SupabaseClient<Database>): Promise<Plan|undefined> {

  const {data, error} = await supabase
      .from("plans")
      .select("*")
      .eq("payment_id", payment.id)
      .select("*")
      .single();

  if (error) {
    console.log("order not found for payment :", payment.id);
    return undefined;
  }

  return { id: data.id,
    paymentId: data.payment_id,
    productId: data.product_id,
    customerId: data.customer_id
   };
}

function verifyNotification(postmark: TestPostmark) {
  assertEquals(postmark.sent.length, 1, "email did not send");
  const body = postmark.sent[0].body;
  assertEquals(body.includes(testEventData.email), true, "email did not contain customer email");
  assertEquals(body.includes(testEventData.product), true, "email did not contain product");
}

