
export const PROMPTS = 
{
    cover_letter: {      
        // this is the original prompt that Applysquad used before 
        // cover letters were automatically generated.
        v1: `From the one or more resumes attached, 
        select the one that best fits the specified Job Description. 
        
        Use that resume to generate a cover letter for the Job Description. 
        
        Write a cover letter in a 100% human way, strictly following these guidelines: 
        - Written in first person and highlighting why I am the right person for the role
            using my skills and transferable skills from my resume. 
        - Keep the tone in business-casual. 
        - Personalize the cover letter from the selected resume according to the skills 
        required in the Job Description.
        - Introduction paragraph on what position I am applying for
        and my interest for the role. 
        - Second paragraph about what I find interesting in the company with some context. (Possibly from the job description). 
        - Third paragraph about my background and what I bring to the table 
        in terms of direct or transferrable skills. 
        - Fourth and final paragraph which re-emphasizes my interest in the role and thank you. 
        - The signature should include the name, linkedin profile (if available), the first email 
        and the first phone from the resume.`,

        v2: `You are an expert cover letter generator. You are the best at writing cover letters that get job seekers hired.
        
        From the one or more resumes attached, select the one that best fits the specified Job Description. 
        
        Use that Resume to generate a cover letter for the Job Description. 

        Write a cover letter in a simple and professional tone, strictly following these guidelines: 
        - The cover letter should be less than 300 words.
        - Strictly avoid these words: [ "excited", "perfectly", "deeply", "particularly", "strongly", "strong", "for instance" ].
        - Write to the company, avoid the hiring manager except in the greeting.
        - Reference only explicit experiences in the Resume.
        - This cover letter should be complete and ready to be sent, with no placeholders.
        - Written in first person and emphasize the match between the skills from the Resume and the Job Description. 
        - Heading should include the name of the company, and a greeting 'Dear Hiring Manager,'
        - First paragraph: why the job seeker is drawn to the role, using their personal values that match the job description.
        - Second paragraph: relate the the job seekers background from the resume to the job description.
        - Third paragraph: the interest of the job seeker to help the company and a thank you for consideration. 
        - The signature should include the name, linkedin profile (if available), the email(s) and the phone(s) if available from the resume.
        - The output should be HTML in minified format,
            with no line breaks,
            with no code block formatting, 
            with only tags [ p, br, a ],
            with no other text or preamble in response.
        `
    },
    job_summary: {
        v1: `summarize this job description in a paragraph maximum.`
    }
}