import type { Database } from '~/lib/applysquad-common/database.types';
import { serverSupabaseClient } from '#supabase/server'
import { NYLAS_CUSTOMER_STATUS } from "~/common/common"
import Nylas from "nylas";
import type { SupabaseClient } from '@supabase/supabase-js';
import type { H3Event } from 'h3';


export default defineEventHandler(async (event:H3Event) => {

  const supabase = await serverSupabaseClient<Database>(event);
  const customerId = getQuery(event).customerId;
  if (!customerId) {
    throw new Error("invalid customer id");
  }
  const grant = await getNylasGrant(customerId!.toString(), supabase);
  const revoked = await revokeGrant(grant, event)
  await updateNylasCustomerRevoked(customerId!.toString(), supabase);
  
  return "OK"
})

async function getNylasGrant(customerId: string, supabase: SupabaseClient<Database>): Promise<string> {
  const {data, error} = await supabase
    .from("nylas_customers")
    .select("*")
    .eq("customer_id", customerId);
  if (error) {
    console.log("nylas update error", error);
    throw error;
  } else if (data.length < 1) {
    throw new Error("no grant found for customer");
  }
  return data[0].grant!;
}

async function updateNylasCustomerRevoked(customerId: string, supabase: SupabaseClient<Database>) {
  const {error} = await supabase
    .from("nylas_customers")
    .update({ status: NYLAS_CUSTOMER_STATUS.REVOKED })
    .eq("customer_id", customerId);
  if (error) {
    console.error("could not revoke nylas customer grant because", error);
    throw error;
  }
}

async function revokeGrant(grant: string, event:H3Event) {

  try {

    const config = useRuntimeConfig(event);
    const nylasConfig = {
      clientId: config.public.NYLAS_CLIENT_ID,
      callbackUri: `${config.public.SITE_URL}${config.public.EMAIL_SETUP_CALLBACK_URL}`,
      apiKey: config.public.NYLAS_API_KEY,
      apiUri: config.public.NYLAS_API_URL,
    };

    const nylas = new Nylas({
      apiKey: nylasConfig.apiKey,
      apiUri: nylasConfig.apiUri,
    });

    const response = await nylas.grants.destroy({grantId: grant});

    console.log('response from nylas', response);

  } catch (error) {
    
    console.error('error during fetch', error)
    // don't throw error 
    // TODO: more resilient error handling  
  }
}

