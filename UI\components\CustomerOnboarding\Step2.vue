<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import type { Customer } from "~/common/common";
import { useSupabaseClient } from "#imports";
import type { Database } from "~/lib/applysquad-common/database.types";
import { companyTypes, industries } from "../../common/common";

const props = defineProps<{
  customer: Customer;
  data?: {
    id: string;
    industries: string;
    industry_excludes: string;
    company_excludes: string;
    company_size: string[];
  };
  isEditing: boolean;
  isCustomer: boolean;
  agentTab: number;
  loadData: () => void;
}>();
const emit = defineEmits(["update:isEditing", "update:agentTab"]);

const supabase = useSupabaseClient<Database>();
const toast = useToast();

const selectedCompanySize = ref<any>([]);

const formState = reactive({
  industries:
    typeof props.data?.industries === "string"
      ? props.data.industries.split(",").map((item) => item.trim())
      : ([] as string[]),
  industry_excludes:
    typeof props.data?.industry_excludes === "string"
      ? props.data.industry_excludes.split(",").map((item) => item.trim())
      : ([] as string[]),
  company_size: props.data ? props.data.company_size : ([] as string[]),
  company_excludes: props.data ? props.data.company_excludes : "",
});

const allIndustries = computed(() => mergeIndustries(industries, props.data?.industries));

const excludeAllIndustries = computed(() => mergeIndustries(industries, props.data?.industry_excludes));

function mergeIndustries(base: string[], additional: string | undefined): string[] {
  if (typeof additional === 'string') {
    const newItems = additional
      .split(',')
      .map(i => i.trim())
      .filter(i => !base.includes(i));

    return Array.from(new Set([...base, ...newItems]));
  }

  return [...base];
}
 
// Add a watcher to update form state when props.data changes
watch(
  () => props.data,
  (newData) => {
    if (newData) {
      formState.industries = newData.industries
        .split(",")
        .map((item) => item.trim());
      formState.industry_excludes = newData.industry_excludes
        .split(",")
        .map((item) => item.trim());
      formState.company_excludes = newData.company_excludes;
      // ... other field updates
      if (Array.isArray(newData.company_size)) {
        formState.company_size = newData.company_size;
      }

      selectedCompanySize.value = Array.isArray(newData.company_size)
        ? newData.company_size.map((size) => ({
            id: companySizeOptions.value.find((option) => option.name === size)
              ?.id,
            name: size,
          }))
        : [];
    }
  },
  { deep: true }
);

const companySizeOptions = ref(
  companyTypes.map((size, index) => ({
    id: index + 1,
    name: size,
  }))
);

// Computed property for company size
const companySizeComputed = computed({
  get: () => selectedCompanySize.value,
  set: async (labels) => {
    if (!labels) {
      selectedCompanySize.value = [];
      return;
    }

    // Ensure formState.functional_preferences is always an array
    if (!Array.isArray(formState.company_size)) {
      formState.company_size = [];
    }

    const promises = labels.map(async (label: any) => {
      if (!label) return null;

      if (label.id) {
        formState.company_size = Array.from(
          new Set([...formState.company_size, label.name])
        );
        return label;
      }

      // Create a new option
      const response = {
        id: companySizeOptions.value.length + 1,
        name: label.name,
      };

      // Add the new option to functionalPreferenceOptions
      companySizeOptions.value = [...companySizeOptions.value, response];

      // Update formState.functional_preferences
      formState.company_size = Array.from(
        new Set([...formState.company_size, label.name])
      );

      return response;
    });

    selectedCompanySize.value = await Promise.all(promises);
  },
});

// Helper function to display array values
function displayArrayValue(arr: any[] | null | undefined): string {
  return Array.isArray(arr) && arr.length > 0
    ? arr.join(", ")
    : "Not specified";
}

const handleBackButtonClick = () => {
  emit("update:agentTab", 0);
};

// Handle form submission
async function submitForm() {
  try {
    const onboardingData = {
      customer_id: props.customer.customerId,
      industries:
        Array.isArray(formState?.industries) && formState.industries.length > 0
          ? formState.industries.join(", ")
          : null,
      industry_excludes:
        Array.isArray(formState?.industry_excludes) &&
        formState.industry_excludes.length > 0
          ? formState.industry_excludes.join(", ")
          : null,
      company_excludes: formState?.company_excludes || null,
      company_size:
        Array.isArray(formState?.company_size) &&
        formState.company_size.length > 0
          ? formState.company_size
          : null,
    };
    let error;
    if (props.data && props.data.id) {
      const { error: updateError } = await supabase
        .from("customer_onboarding")
        .update(onboardingData as object)
        .eq("id", Number(props.data.id))
        .eq("customer_id", props.customer.customerId);
      error = updateError;
    } else {
      const { error: insertError } = await supabase
        .from("customer_onboarding")
        .insert(onboardingData as object);
      error = insertError;
    }
    if (error) {
      console.error("Database operation error:", error);
      throw error;
    }
    await props.loadData();

    const { error: customerError } = await supabase
      .from("customers")
      .update({ onboarding_complete: true })
      .eq("id", props.customer.customerId);
    if (customerError) throw customerError;
    toast.add({
      title: "Success",
      description: "Profile updated successfully",
      color: "green",
    });
    emit("update:agentTab", 2);
  } catch (error) {
    console.error("Error saving form:", error);
    toast.add({
      title: "Error",
      description: "Failed to update profile",
      color: "red",
    });
  }
}

const handleNextClick = () => {
  emit("update:agentTab", 2);
};
</script>

<template>
  <div>
    <h2 class="text-base md:text-xl font-bold">
      Tell us about the company you'd like to work for
    </h2>
    <!-- Form -->
    <UForm :state="formState" class="space-y-4 pt-4" @submit="submitForm">
      <div class="flex flex-col md:flex-row space-y-4 w-full">
        <div class="flex flex-col gap-4 lg:gap-6 w-full">
          <!-- Industries -->
          <UFormGroup
            label="What industries would you like to work in?"
            class="my-2"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <template v-if="isEditing">
              <USelectMenu
                v-model="formState.industries"
                :options="allIndustries"
                multiple
                searchable
                :default-value="formState.industries"
                placeholder="Select Industry..."
              />
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{ displayArrayValue(formState.industries) }}
              </div>
            </template>
          </UFormGroup>

          <!-- other industries to exclude -->
          <UFormGroup
            label="Are there any other industries to exclude?"
            class="my-2"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <template v-if="isEditing">
              <USelectMenu
                v-model="formState.industry_excludes"
                :options="excludeAllIndustries"
                multiple
                searchable
                :default-value="formState.industry_excludes"
                placeholder="Select Industry..."
              />
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{
                  Array.isArray(formState.industry_excludes) &&
                  displayArrayValue(formState.industry_excludes)
                }}
              </div>
            </template>
          </UFormGroup>

          <!-- companySize -->
          <UFormGroup
            label="What size or type of company should we prioritize?"
            class="my-2"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <template v-if="isEditing">
              <USelectMenu
                v-model="companySizeComputed"
                :options="companySizeOptions"
                by="id"
                name="company_size"
                option-attribute="name"
                multiple
                searchable
                creatable
                :default-value="selectedCompanySize"
                placeholder="Select company preferences..."
              >
                <template #option="{ option }">
                  <span class="truncate">{{ option.name }}</span>
                </template>
                <template #option-create="{ option }">
                  <span class="flex-shrink-0">New size:</span>
                  <span class="block truncate">{{ option.name }}</span>
                </template>
              </USelectMenu>
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{ displayArrayValue(formState.company_size) }}
              </div>
            </template>
          </UFormGroup>

          <!-- companies excluded -->
          <UFormGroup
            label="What companies should be excluded entirely?"
            class="my-2"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <template v-if="isEditing">
              <UInput
                v-model="formState.company_excludes"
                placeholder="Enter excluded companies..."
              />
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{ formState.company_excludes || "Not specified" }}
              </div>
            </template>
          </UFormGroup>
        </div>
      </div>

      <!-- Submit Button -->

      <div class="flex justify-end items-center gap-3">
        <UButton
          type="button"
          color="orange"
          variant="outline"
          class="mt-4"
          @click="handleBackButtonClick"
        >
          Back
        </UButton>

        <UButton
          v-if="isEditing"
          type="submit"
          color="orange"
          variant="outline"
          class="mt-4"
        >
          Next
        </UButton>

        <UButton
          v-else
          type="button"
          color="orange"
          variant="outline"
          class="mt-4"
          @click="handleNextClick"
        >
          Next
        </UButton>
      </div>
    </UForm>
  </div>
</template>
