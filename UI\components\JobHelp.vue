<template>
  <UPopover>
    <UButton 
        variant="outline" 
        label="how it works" 
        class="text-blue-500 h-10"
        trailing-icon="la:question-circle-solid"
    />
    <template #panel>
      <UCard class="p-4 max-w-[355px] sm:max-w-xl text-md">
        <JobProcess/>
      </UCard>
    </template>
  </UPopover>
</template>

<script lang="ts" setup>
import { ref } from "vue";

</script>
