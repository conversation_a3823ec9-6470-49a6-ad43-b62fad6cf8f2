SET check_function_bodies = off;

create table "public"."stats_daily_jobs" (
    "id" uuid primary key default gen_random_uuid(),
    "date" date not null,
    "total_jobs" integer not null default 0,
    "open_jobs" integer not null default 0,
    "complete_jobs" integer not null default 0,
    "total_jobs_for_today" integer not null default 0,
    "created_at" timestamp with time zone not null default now()
);

CREATE OR REPLACE FUNCTION public.populate_stats_daily_jobs()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
declare
  report_date date := current_date -1;
  total_jobs int;
  open_jobs int;
  complete_jobs int;
  total_jobs_for_today int;
begin
  select 
    count(*) into total_jobs
  from public.jobs;

  select 
    count(*) filter (where job_status = 'OPEN') into open_jobs
  from public.jobs;

  select 
    count(*) filter (where job_status = 'CLOSED') into complete_jobs
  from public.jobs;

  select 
    count(*) filter (where created_at::date = report_date ) into total_jobs_for_today
  from public.jobs;

  insert into public.stats_daily_jobs (
    id,
    date,
    total_jobs,
    open_jobs,
    complete_jobs,
    total_jobs_for_today,
    created_at
  )
  values (
    gen_random_uuid(),
    report_date,
    total_jobs,
    open_jobs,
    complete_jobs,
    total_jobs_for_today,
    now()
  );
end;
$function$
;

CREATE EXTENSION IF NOT EXISTS pg_cron;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM cron.job WHERE jobname = 'daily_stats_job'
  ) THEN
    PERFORM cron.schedule(
      job_name => 'daily_stats_job',
      schedule => '5 0 * * *',
      command => $cron$SELECT public.populate_stats_daily_jobs();$cron$
    );
  END IF;
END
$$;