<template>
  <div class="mx-auto max-w-lg">
    <UCard class="shadow-2xl shadow-gray-600 bg-white my-10 mx-4">
      <template #header>
        <h4
          class="text-xl sm:text-2xl font-bold text-center text-gray-700 mb-2"
        >
          Forgot Password
        </h4>
      </template>

      <UForm @submit="sendPasswordReset">
        <UFormGroup label="Email" name="email" class="my-3">
          <UInput v-model="email" placeholder="<EMAIL>" required />
        </UFormGroup>

        <UButton
          type="submit"
          class="bg-orange-500 hover:bg-orange-600 my-4 justify-center py-3 sm:text-lg w-full"
        >
          Send password reset email
        </UButton>
      </UForm>

      <div v-if="errorMessage" class="text-red-500 text-center">
        {{ errorMessage }}
      </div>
      <div v-if="successMessage" class="text-green-500 text-center">
        {{ successMessage }}
      </div>
    </UCard>
  </div>
</template>

<script setup>
import { ref } from "vue";

// Define reactive state variables
const email = ref("");
const errorMessage = ref("");
const successMessage = ref("");
const config = useRuntimeConfig();
// Get Supabase and router instances
const supabase = useSupabaseClient();

// Login handler
const sendPasswordReset = async () => {
  // Reset previous error
  errorMessage.value = "";

  // Validate inputs
  if (!email.value) {
    errorMessage.value = "Please enter email";
    return;
  }

  try {
    // Attempt login
    const { error } = await supabase.auth.resetPasswordForEmail(email.value, {
      redirectTo: `${config.public.SITE_URL}/reset-pass`,
    });

    // Check for login error
    if (error) {
      throw error;
    }

    // If login successful, navigate to customer page
    successMessage.value = "Password reset email sent. Check your inbox.";
  } catch (error) {
    // Handle different types of errors
    errorMessage.value = error.message || "Login failed. Please try again.";
  }
};
</script>
