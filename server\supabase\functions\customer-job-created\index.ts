import { supabaseClient } from '../shared/common.ts';
import { SupabaseClient } from 'jsr:@supabase/supabase-js@2';
import { Database } from '../lib/database.types.ts';
import { generateCoverLetter } from "../lib/cover-letter-service.ts";
import { CustomerInfo } from '../lib/cover-letter-service.ts';
import { JobInfo } from '../lib/cover-letter-service.ts';

console.log("Customer Job Created Event Handler booting");

export function startDeno() {
  // just used by tests to start Deno.serve()
}

type CustomerJobRecord = {
  id: number;
  job_id: string;
  customer_id: string;
}

type InsertPayload = {
  record: CustomerJobRecord;
}



Deno.serve(async (req: Request) => {

  console.log("handling customer job created request");

  if (req.method !== "POST") {
    return new Response("Invalid method", { status: 405 });
  }
  
  const supabase = supabaseClient();
  const payload: InsertPayload = JSON.parse(decodeURI(await req.text()) );
  
  const custInfo = await buildCustInfo(payload.record.customer_id, supabase);
  const jobInfo = await buildJobInfo(payload.record.job_id, supabase);
  
  const coverLetter = await generateCoverLetter(custInfo, jobInfo);

  await saveCoverLetter(payload.record.id, coverLetter, supabase);

  console.log(`updated customer job ${payload.record.id} with cover letter`)

  return new Response("customer job created event handled - added generated cover letter")
})


async function saveCoverLetter(
  customerJobId: number, 
  coverLetter: string, 
  supabase: SupabaseClient<Database>) {

  const { error } = await supabase
    .from('cover_letters')
    .insert({customer_job_id: customerJobId, data: coverLetter})
    .eq('id', customerJobId);
  if (error) {
    throw error;
  }
}

async function buildJobInfo(jobId: string, supabase: SupabaseClient<Database>): Promise<JobInfo> {

  const { data: jobData, error: jobError } = await supabase
    .from("jobs")
    .select("*")
    .eq("id", jobId)
    .single();
  if (jobError) {
    throw jobError;
  }
  return {
    jobTitle: jobData.title,
    company: jobData.employer,
    jobDescription: jobData.description
  }
}

async function buildCustInfo(customerId: string, supabase: SupabaseClient<Database>): Promise<CustomerInfo> {

    // get all the CVs for the customer, we don't know which one to use, so we'll let AI decide
  const { data: documentData, error: documentError } = await supabase
    .from("documents")
    .select("*")
    .eq("customer_id", customerId)
    .eq("active", true)
    .eq("document_type", "CV");
  if (documentError) {
    throw documentError;
  }
  const cvs = await Promise.all(documentData.map(async (document) => 
    await fetchDocument(document.bucket, document.path, supabase)
  ));
  console.log("cvs text", cvs);

  const { data: onboarding, error } = await supabase
    .from("customer_onboarding")
    .select("*")
    .eq("customer_id", customerId)
    .single();
  if (error) {
    console.log("customer does not have onboarding");
  }
  return {
    cvs: cvs,
    functionalPreference: onboarding?.functional_preferences?.join(",")
  }
}

async function fetchDocument(bucket: string, path: string, supabase: SupabaseClient): Promise<Blob> {
  const { data, error } = await supabase.storage
    .from(bucket)
    .download(path);
  if (error) {
    throw error;
  }
  return data;
}

/**
 * Cover Letters are generated for display to the customer
 * in the HTML editor, generating a PDF for upload to job board.
 * This step tweaks the LLM output to make sure it works with
 * the editor and PDF generator.
 */
function _postProcess(coverLetter: string): string {
  // put a break in between all paragraphs
  return `<p>${new Date().toDateString()}</p><p><br/></p>`
    .concat(coverLetter)
    .replaceAll("</p><br/><p>", "</p><p><br/></p><p>");
}
