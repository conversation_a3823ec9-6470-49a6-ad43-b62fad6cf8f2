<template>
  <div class="mx-auto max-w-lg">
    <UCard class="shadow-2xl shadow-gray-700 bg-white my-10 mx-4">
      <template #header>
        <h4
          class="text-xl sm:text-2xl font-bold text-center text-gray-700 mb-2"
        >
          Reset Password
        </h4>
      </template>

      <UForm :state="state" @submit="handlePasswordReset">
        <UFormGroup label="Password" name="password" class="my-3">
          <UInput v-model="state.newPassword" type="password" required />
        </UFormGroup>

        <UFormGroup
          label="Confirm Password"
          name="confirmPassword"
          class="my-3"
        >
          <UInput v-model="state.confirmPassword" type="password" required />
        </UFormGroup>

        <UButton
          type="submit"
          class="bg-orange-500 hover:bg-orange-600 my-4 justify-center py-3 sm:text-lg w-full"
        >
          Reset password
        </UButton>
      </UForm>

      <div v-if="errorMessage" class="text-red-500 text-center">
        {{ errorMessage }}
      </div>
      <div v-if="successMessage" class="text-green-500 text-center">
        {{ successMessage }}
      </div>
    </UCard>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { isAdmin, isAgent, isCustomer } from "~/common/common";

// State variables
const state = reactive({
  newPassword: undefined,
  confirmPassword: undefined,
});
const successMessage = ref("");
const errorMessage = ref("");
const tokenHash = useRoute().query.token

if (!tokenHash || tokenHash == undefined) {
  errorMessage.value = "reset token not found, please follow link from  your email";
}

// Get Supabase client
const supabase = useSupabaseClient();

// Validate password
const validatePassword = () => {
  successMessage.value = "";
  errorMessage.value = "";
  // Basic password validation
  if (state.newPassword.length < 8) {
    errorMessage.value = "Password must be at least 8 characters";
    return false;
  }

  if (state.newPassword !== state.confirmPassword) {
    errorMessage.value = "Passwords do not match";
    return false;
  }

  return true;
};

// Handle password reset
const handlePasswordReset = async () => {
  // Reset previous errors
  errorMessage.value = "";
  successMessage.value = "";

  // Validate inputs
  if (!validatePassword()) {
    return;
  }

  try {

    const { error: otpError } = await supabase.auth
        .verifyOtp({ token_hash: tokenHash, type: 'email'})

    if (otpError) {
      errorMessage.value = otpError.message;
      throw otpError
    }

    // Get the current session
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session) {
      throw new Error(
        "No active session. Please request a new password reset link."
      );
    }

    // Attempt password reset
    const { error } = await supabase.auth.updateUser({
      password: state.newPassword,
    });

    if (error) {
      console.log("Error resetting pass:" + error.message);
      throw error;
    }

    // Success handling
    successMessage.value = "Password reset successfully!";

    
    // external=true forces a refresh. Otherwise the header doesn't refresh
    if (await isCustomer(session.user)) {
      navigateTo("/customer", { external: true });
    } else if (await isAgent(session.user)) {
      navigateTo("/agent", { external: true });
    } else if (await isAdmin(session.user, supabase)) {
      navigateTo("/admin", { external: true });
    } else {
      errorMessage.value = "Invalid user role. Please contact support.";
      console.error(
        `unknown user role: ${session.user.user_metadata.role} for user ${session.user.email}`
      );
      navigateTo("/login", { external: true });
    }

  } catch (error) {
    // Error handling
    errorMessage.value = error.message || "Failed to reset password";
  }
};

</script>
