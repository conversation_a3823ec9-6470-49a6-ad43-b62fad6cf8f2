set check_function_bodies = off;

create table "public"."stats_daily_customers" (
    "id" uuid primary key default gen_random_uuid(),
    "date" date not null,
    "total_customers" integer not null default 0,
    "new_customers" integer not null default 0,
    "paid_customers" integer not null default 0,
    "total_customers_for_today" integer not null default 0,
    "created_at" timestamp with time zone not null default now()
);


CREATE OR REPLACE FUNCTION public.populate_stats_daily_customers()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  report_date date := current_date - 1;
  total_customers int;
  new_customers int;
  paid_customers int;
  total_customers_for_today int;
BEGIN
  SELECT count(*) INTO total_customers
  FROM public.customers;

  SELECT count(*) INTO new_customers
  FROM public.customers
  WHERE status = 'NEW';

  SELECT count(*) INTO paid_customers
  FROM public.customers
  WHERE status = 'PAID';

  SELECT count(*) INTO total_customers_for_today
  FROM public.customers
  WHERE created_at::date = report_date;

  INSERT INTO public.stats_daily_customers (
    id,
    date,
    total_customers,
    new_customers,
    paid_customers,
    total_customers_for_today,
    created_at
  ) VALUES (
    gen_random_uuid(),
    report_date,
    total_customers,
    new_customers,
    paid_customers,
    total_customers_for_today,
    now()
  );
END;
$function$
;

CREATE EXTENSION IF NOT EXISTS pg_cron;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM cron.job WHERE jobname = 'daily_stats_customers'
  ) THEN
    PERFORM cron.schedule(
      job_name := 'daily_stats_customers',
      schedule := '15 0 * * *',
      command := 'SELECT public.populate_stats_daily_customers();'
    );
  END IF;
END
$$;