<template>
  <div class="h-full">
    <div class="max-w-lg mx-auto pt-5">
      <UCard class="shadow-2xl shadow-gray-600 bg-white my-10 m-4">
        <template #header>
          <h4
            class="text-xl sm:text-2xl font-bold text-center text-gray-700 mb-2"
          >
            Login
          </h4>
        </template>

        <UForm :schema="schema" :state="state" @submit="onSubmit">
          <UFormGroup label="Email" name="email" class="my-3">
            <UInput
              v-model="state.email"
              type="email"
              placeholder="<EMAIL>"
              />
          </UFormGroup>

          <UFormGroup label="Password" name="password" class="my-3">
            <UInput v-model="state.password" type="password"  />
          </UFormGroup>

          <ULink class="text-gradient text-sm" to="/forgot-password">
            Forgot your password?
          </ULink>

          <UButton
            type="submit"
            :loading="isLoading"
            :disabled="isLoading"
            class="button-bg-gradient my-4 justify-center py-3 sm:text-lg w-full"
          >
            Login
          </UButton>

          <div v-if="errorMessage" class="text-red-500 text-center">
            {{ errorMessage }}
          </div>
          <div v-if="successMessage" class="text-green-500 text-center">
            {{ successMessage }}
          </div>
        </UForm>
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { isAdmin, isAgent, isCustomer } from "~/common/common";
import type { Database } from "~/lib/applysquad-common/database.types";
import { z } from 'zod'
import type { FormSubmitEvent } from '#ui/types'

const schema = z.object({
  email: z.string().email('Invalid email'),
  password: z.string().min(5, "Password is required")
})

type Schema = z.output<typeof schema>


// Define reactive state variables
const isLoading = ref(false);
const errorMessage = ref("");
const successMessage = ref("");

// Get Supabase and router instances
const supabase = useSupabaseClient<Database>();

const state = reactive({
  email: "",
  password: "",
});

// Login handler
async function onSubmit(event: FormSubmitEvent<Schema>) {

  // Reset previous error
  errorMessage.value = "";

  // Set loading state
  isLoading.value = true;

  try {
    // Attempt login
    const { data, error } = await supabase.auth.signInWithPassword({
      email: state.email.toLowerCase().trim(),
      password: state.password,
    });

    // Check for login error
    if (error) {
      errorMessage.value = error.message;
      throw error;
    } else if (!data.user) {
      errorMessage.value = "Invalid email or password.";
      throw error;
    }

    successMessage.value = "login successful, redirecting you to your dashboard"

    // external=true forces a refresh. Otherwise the header doesn't refresh
    if (await isCustomer(data.user)) {
      navigateTo("/customer", { external: true });
    } else if (await isAgent(data.user)) {
      navigateTo("/agent", { external: true });
    } else if (await isAdmin(data.user, supabase)) {
      navigateTo("/admin", { external: true });
    } else {
      errorMessage.value = "Invalid user role. Please contact support.";
      console.error(
        `unknown user role: ${data.user.user_metadata.role} for user ${data.user.email}`
      );
      navigateTo("/login", { external: true });
    }
  } catch (error) {
    console.error(error);
    errorMessage.value = "Login failed. Please try again.";
  } finally {
    // Reset loading state
    isLoading.value = false;
  }
};
</script>
