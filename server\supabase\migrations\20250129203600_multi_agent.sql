-- Drop existing constraints if they exist
ALTER TABLE "public"."agent_customers" DROP CONSTRAINT IF EXISTS "agent_customers_pkey";
ALTER TABLE "public"."agent_customers" DROP CONSTRAINT IF EXISTS "agent_customers_agent_id_fkey";
ALTER TABLE "public"."agent_customers" DROP CONSTRAINT IF EXISTS "agent_customers_customer_id_fkey";

-- Add a new primary key that includes both agent_id and customer_id
ALTER TABLE "public"."agent_customers" 
ADD CONSTRAINT "agent_customers_pkey" 
PRIMARY KEY (agent_id, customer_id);

-- Add foreign key constraints
ALTER TABLE "public"."agent_customers" 
ADD CONSTRAINT "agent_customers_agent_id_fkey" 
FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE;

ALTER TABLE "public"."agent_customers" 
ADD CONSTRAINT "agent_customers_customer_id_fkey" 
FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE;