import { CustomerInfo, JobInfo, generateCoverLetter } from "../src/cover-letter-service.ts";
import { CoherenceMetric, DynamicMetric, evaluate, EvaluationExecutionReport } from '@evalkit/core';
import { expect, test } from 'vitest';
import * as fs from 'fs';
import * as dotenv from "dotenv";

test("generate cover letter", async () => {
    dotenv.config();
    const cvBlob = new Blob([fs.readFileSync("./test/resume-john-doe.pdf")]);
    const custInfo: CustomerInfo = {
        functionalPreference: "leadership, innovation, collaboration",
        cvs: [cvBlob]
    }
    const jobInfo: JobInfo = {
        jobTitle: jobTitle,
        company: jobCompany,
        jobDescription: jobDescription
    }

    const coverLetter = await generateCoverLetter(custInfo, jobInfo);

    console.log("cover letter:", coverLetter);
    expect(coverLetter).toBeDefined();

    //await testDynamic(coverLetter);
    //await testCoherence(coverLetter);
    
}, 30000);

/**
 * The Dynamic metric assesses how well the actual output from a model
 * aligns with an expected output based on a set of specified criteria.
 */
async function testDynamic(coverLetter: string) {
    
    const criteria = [
        { type: "Accuracy" },
        { type: "Relevance" }
    ];
    
    const score: EvaluationExecutionReport = await evaluate({
        input: "",
        actualOutput: coverLetter,
        expectedOutput: expectedCoverLetter,
        criteria: criteria
    }, [ DynamicMetric ]);
    
    expect(score.items.length).toBeGreaterThan(0);
    
    score.items.forEach( (item) => {
        console.log("evaluating", item.metricName);
        console.log("score:", item.result.score)
        expect(item.result.passed).toBeTruthy();
    });
}

/**
 * The Coherence metric assesses the logical flow and consistency
 * of paragraphs in text generated by a model
 */
async function testCoherence(coverLetter: string) {

    const score: EvaluationExecutionReport = await evaluate({
        output: coverLetter
    }, [ CoherenceMetric ]);
    
    expect(score.items.length).toBeGreaterThan(0);
    
    score.items.forEach( (item) => {
        console.log("evaluating", item.metricName);
        console.log("score:", item.result.score)
        expect(item.result.passed).toBeTruthy();
    });
}

const jobTitle = "Product Manager - Marketplace";
const jobCompany = "Curri";
const jobDescription = `About the job
What's the role?

Do you like building great products that help customers solve real-world problems? Do you enjoy collaborating with great people to turn it into a reality? Do you want responsibility and ownership beyond just shipping random features that your boss tells you need to be out by the end of the quarter?

Great! You might be a fit for our growing Product team at Curri.

We are looking for a Product Manager to help grow our marketplace here at Curri. Construction and industrial distributors use the Curri marketplace nationwide to deliver material to their customers. You will work closely with our customers to deeply understand their needs and innovate on their behalf to create amazing delivery experiences.

Join us in making "Deliver with Curri" the default option for construction and industrial supplies.

What will you do:

Understand our customers' needs better than they do and ensure that Curri's marketplace provides solutions that solve their business needs.
Collaborate with our sales, operations, product, and engineering teams to define and build cohesive, intuitive, and quality experiences for our customers.
Define and gather requirements and create shaping docs that ensure we build software that solves our customers' problems.
Work closely with the go-to-market teams to ensure that the Curri marketplace meets our customers' needs and is effectively presented to them.
Engage with leadership to participate in prioritizing software engineering work.
Prepare and present regular reports on the progress of initiatives to key stakeholders, including executive leadership.

What you will need to have:

A track record: 2+ years of experience doing software product management and a demonstrable ability to work with product stakeholders to bring ideas to market
A Customer First mentality: You prioritize engaging with customers to understand and meet customer needs.
Execution skills: A bias towards action and a record of working with engineering teams to ship software quickly and iteratively.
Problem-solving skills: The ability to understand complex problems and explain them clearly.
Prioritization: Working on and prioritizing multiple projects, understanding their connection, and making trade-offs when necessary.

What will make you stand out:

Product Chops: You excel at the nuts and bolts of product development, from user research to shaping, execution, and measuring success.
Clarity and Communication: Demonstrate that you can think clearly and communicate your thoughts. There are many moving parts at Curri, and your ability to understand and communicate these to others is vital to your success.
Collaboration: This role will involve working closely with several different teams, both internally and externally, and you'll need to be able to communicate with a wide variety of people effectively.
Growth: A desire to push yourself and hone your craft. This scope of work will grow fast, and you'll need to be in continuous learning mode.

What is in it for you?

You will have the opportunity to work for a dynamic and successful start-up on a diverse team where you can make a huge impact by doing meaningful work.
Significant and meaningful responsibilities from Day 1. The possibilities are limitless and depend on you.
Work in an environment with a flexible schedule. We don't micromanage and want to help you do great work.
There is no work/life—there is only life, and we want your time at Curri to be life-giving and foster the best version of you. We care about family and your own personal development and don't expect you to always be engaged with work.
We offer a competitive salary, and benefits including, but not limited to, health, dental, vision, 401K, and an equity compensation grant.`;

const expectedCoverLetter = `
<p>Curri</p><p><br></p><p>Dear Hiring Manager,</p><p><br></p><p>The opportunity to join Curri as a Product Manager - Marketplace is appealing to me because of the company's commitment to innovation and collaboration. I am drawn to the role because it aligns with my personal values of leadership, innovation, and collaboration, and I am eager to contribute to creating amazing delivery experiences for construction and industrial distributors.</p><p><br></p><p>My experience as a Graduate Trainee in Product Management at Salesflo has equipped me with the skills necessary for this role. I have successfully led a team to optimize core products, reducing sales queue time by 30%, and utilized client insights to create software products that improved value chain operations. My background in collaborating with clients such as L'Oréal and Nestle to identify operational gaps and my ability to employ data analysis tools like Tableau and Python to resolve inefficiencies align well with Curri's focus on understanding customer needs and delivering quality experiences.</p><p><br></p><p>I am eager to bring my product management experience and problem-solving skills to Curri, helping to grow the marketplace and enhance customer satisfaction. Thank you for considering my application. I look forward to the possibility of contributing to your team.</p><p><br></p><p>Sincerely,</p><p>John DOE</p><p><a href='https://www.linkedin.com/in/john-doe/' target='_blank'>https://www.linkedin.com/in/john-doe/</a></p><p><EMAIL> / <EMAIL></p><p>+33 7 69 12 63 75 / +92 302 826 6505</p>
`;