<script setup lang="ts">
import type { Database } from "~/lib/applysquad-common/database.types";
import {
  jobType,
  workSpace,
  seniorityLevels,
  functionalPreferences,
  type Customer,
} from "../../common/common";

const emit = defineEmits(["update:isEditing", "update:agentTab"]);
const supabase = useSupabaseClient<Database>();
const selectedFunctionalPreferences = ref<any>([]);

type JobType = Database["public"]["Enums"]["job_type"];
const toast = useToast();

const props = defineProps<{
  customer: Customer;
  data?: {
    id: string;
    job_types: JobType[];
    job_titles: string;
    workpalce_type: string[];
    role_excludes: string;
    functional_preferences: string[];
    seniority: string[];
    keywords: string;
  };
  isEditing: boolean;
  isCustomer: boolean;
  agentTab: number;
  loadData: () => void;
}>();

const formState = reactive({
  job_types: props.data ? props.data.job_types : ([] as JobType[]),
  job_titles: props.data ? props.data.job_titles : "",
  workplace_type: props.data ? props.data.workpalce_type : ([] as string[]),
  role_excludes: props.data ? props.data.role_excludes : "",
  functional_preferences: props?.data
    ? props?.data?.functional_preferences
    : [],
  seniority: props.data ? props.data.seniority : ([] as string[]),
  keywords: props.data ? props.data.keywords : "",
});

const functionalPreferenceOptions = ref(
  functionalPreferences.map((pref, index) => ({
    id: index + 1,
    name: pref,
  }))
);

const functionalPreferencesComputed = computed({
  get: () => selectedFunctionalPreferences.value,
  set: async (labels) => {
    if (!labels) {
      selectedFunctionalPreferences.value = [];
      return;
    }

    // Ensure formState.functional_preferences is always an array
    if (!Array.isArray(formState.functional_preferences)) {
      formState.functional_preferences = [];
    }

    const promises = labels.map(async (label: any) => {
      if (!label) return null;

      if (label.id) {
        formState.functional_preferences = Array.from(
          new Set([...formState.functional_preferences, label.name])
        );
        return label;
      }

      // Create a new option
      const response = {
        id: functionalPreferenceOptions.value.length + 1,
        name: label.name,
      };

      // Add the new option to functionalPreferenceOptions
      functionalPreferenceOptions.value = [
        ...functionalPreferenceOptions.value,
        response,
      ];

      // Update formState.functional_preferences
      formState.functional_preferences = Array.from(
        new Set([...formState.functional_preferences, label.name])
      );

      return response;
    });

    selectedFunctionalPreferences.value = await Promise.all(promises);
  },
});

// Add a watcher to update form state when props.data changes
watch(
  () => props.data,
  (newData) => {
    if (newData) {
      formState.job_types = newData.job_types;
      formState.job_titles = newData.job_titles;
      formState.workplace_type = newData.workpalce_type;
      formState.role_excludes = newData.role_excludes;
      formState.functional_preferences = newData.functional_preferences;
      formState.seniority = newData.seniority;
      formState.keywords = newData.keywords;

      selectedFunctionalPreferences.value = Array.isArray(
        newData.functional_preferences
      )
        ? newData.functional_preferences.map((pref: any) => ({
            id: functionalPreferenceOptions.value.find(
              (option) => option.name === pref
            )?.id,
            name: pref,
          }))
        : [];
    }
  },
  { deep: true }
);

// Helper function to display array values
function displayArrayValue(arr: any[] | null | undefined): string {
  return Array.isArray(arr) && arr.length > 0
    ? arr.join(", ")
    : "Not specified";
}

async function submitForm() {
  //   props.agentTab = 1;
  // return;
  try {
    const onboardingData = {
      customer_id: props.customer.customerId,
      job_types: formState.job_types.length > 0 ? formState.job_types : null,

      job_titles: formState.job_titles || null,

      workpalce_type:
        formState.workplace_type.length > 0 ? formState.workplace_type : null,

      functional_preferences:
        formState.functional_preferences?.length > 0
          ? formState.functional_preferences
          : null,
      seniority: formState.seniority?.length > 0 ? formState.seniority : null,
      keywords: formState.keywords || null,
      role_excludes: formState.role_excludes || null,
    };

    let error;

    if (props.data && props.data.id) {
      const { error: updateError } = await supabase
        .from("customer_onboarding")
        .update(onboardingData as object)
        .eq("id", Number(props.data.id))
        .eq("customer_id", props.customer.customerId);
      error = updateError;
    } else {
      const { error: insertError } = await supabase
        .from("customer_onboarding")
        .insert(onboardingData as object);
      error = insertError;
    }

    if (error) {
      console.error("Database operation error:", error);
      throw error;
    }

    await props.loadData();

    const { error: customerError } = await supabase
      .from("customers")
      .update({ onboarding_complete: true })
      .eq("id", props.customer.customerId);

    if (customerError) throw customerError;

    toast.add({
      title: "Success",
      description: "Profile updated successfully",
      color: "green",
    });
    emit("update:agentTab", 1);

    // emit("update:isEditing", !props.isEditing);
  } catch (error) {
    console.error("Error saving form:", error);
    toast.add({
      title: "Error",
      description: "Failed to update profile",
      color: "red",
    });
  }
}

const handleNextClick = () => {
  emit("update:agentTab", 1);
};
</script>

<template>
  <div>
    <h2 class="text-base md:text-xl font-bold">Tell us about the job you'd like to have</h2>
    <!-- Form -->
    <UForm :state="formState" class="space-y-4 pt-4" @submit="submitForm">
      <div class="flex flex-col md:flex-row space-y-4 w-full">
        <div class="flex flex-col gap-4 lg:gap-6 w-full">
          <!-- Job titles -->
          <UFormGroup
            label="What job roles/titles would you like?"
            class="my-2"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <template v-if="isEditing">
              <UInput
                v-model="formState.job_titles"
                placeholder="Enter job roles..."
              />
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{ formState.job_titles || "Not specified" }}
              </div>
            </template>
          </UFormGroup>

          <!-- Job Types -->
          <UFormGroup
            label="What kind of job do you want?"
            class="my-2"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <template v-if="isEditing">
              <USelectMenu
                v-model="formState.job_types"
                :options="jobType"
                multiple
                required
                :default-value="formState.job_types"
                placeholder="Select job types..."
              />
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{ displayArrayValue(formState.job_types) }}
              </div>
            </template>
          </UFormGroup>

          <!-- workplace -->
          <UFormGroup
            label="What type of workplace do you want?"
            class="my-2"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <template v-if="isEditing">
              <USelectMenu
                v-model="formState.workplace_type"
                :options="workSpace"
                multiple
                required
                :default-value="formState.workplace_type"
              />
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{ displayArrayValue(formState.workplace_type) }}
              </div>
            </template>
          </UFormGroup>

          <!-- level of seniority -->
          <UFormGroup
            label="What level of seniority are you targeting?"
            class="my-2"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <template v-if="isEditing">
              <USelectMenu
                v-model="formState.seniority"
                :options="seniorityLevels"
                multiple
              />
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{ displayArrayValue(formState.seniority) }}
              </div>
            </template>
          </UFormGroup>

          <!-- capture relevant roles keywords -->
          <UFormGroup
            label="Any specific keywords we should include to capture relevant roles?"
            class="my-2"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <template v-if="isEditing">
              <UInput
                v-model="formState.keywords"
                placeholder="Enter keywords..."
              />
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{ formState.keywords || "Not specified" }}
              </div>
            </template>
          </UFormGroup>

          <!-- functional preferences -->
          <UFormGroup
            label="What are your functional preferences?"
            class="my-2"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <template v-if="isEditing">
              <USelectMenu
                v-model="functionalPreferencesComputed"
                :options="functionalPreferenceOptions"
                by="id"
                name="functional_preferences"
                option-attribute="name"
                multiple
                searchable
                creatable
                :default-value="selectedFunctionalPreferences"
                placeholder="Select or add a new one..."
              >
                <template #option="{ option }">
                  <span class="truncate">{{ option.name }}</span>
                </template>
                <template #option-create="{ option }">
                  <span class="flex-shrink-0">New preference:</span>
                  <span class="block truncate">{{ option.name }}</span>
                </template>
              </USelectMenu>
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{ displayArrayValue(formState.functional_preferences) }}
              </div>
            </template>
          </UFormGroup>

          <!-- roles to exclude -->
          <UFormGroup
            label="Are there types of roles to exclude? (e.g., operational or entry-level roles)"
            class="my-2"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <template v-if="isEditing">
              <UInput
                v-model="formState.role_excludes"
                placeholder="Enter excluded roles..."
              />
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{ formState.role_excludes || "Not specified" }}
              </div>
            </template>
          </UFormGroup>
        </div>
      </div>

      <!-- Submit Button -->

      <div class="flex justify-end items-center gap-3">
        <UButton v-if="isEditing" type="submit" variant="outline" class="mt-4">
          Next
        </UButton>

        <UButton
          v-else
          type="button"
          color="orange"
          variant="outline"
          class="mt-4"
          @click="handleNextClick"
        >
          Next
        </UButton>
      </div>
    </UForm>
  </div>
</template>
