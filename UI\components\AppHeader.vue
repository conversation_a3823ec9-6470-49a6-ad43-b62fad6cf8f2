<template>
  <div>
    <!-- Header Section -->
    <header
      class="bg-gradient-to-r from-red-500 to-orange-400 py-4 sm:py-6 px-2.5 sm:px-6"
    >
      <div class="flex justify-between items-center">
        <!-- Logo -->
        <ULink to="/" class="sm:text-4xl font-bold text-white">
          APPLYSQUAD
        </ULink>

        <!-- Navigation -->
        <nav
          class="flex space-x-4 justify-center sm:space-x-6 items-center mt-0"
        >
          <ULink
            v-if="!loggedIn"
            to="/signup"
            :class="{
              'nav-link': true,
              'active-link': $route.path === '/signup',
            }"
          >
            <UIcon
              name="la:user-plus"
              title="Signup"
              class="w-5 h-5 sm:h-8 sm:w-8"
            />
            <span
              :class="{
                'link-animation': true,
                'active-link': $route.path === '/signup',
              }"
              >Sign up</span
            >
          </ULink>

          <!-- todo list-->
          <!-- <UPopover v-if="isCust" :popper="{ placement: 'auto-end' }">
            <div class="nav-link">
              <UIcon
                name="la:list-ul"
                title="Todo List"
                class="w-5 h-5 sm:h-8 sm:w-8"
              />
              <span class="link-animation">Todo</span>
            </div>
            <template #panel>
              <UCard class="block">
                <ULink
                  to="/purchase"
                  class="text-orange-600 hover:text-orange-700 block"
                  :class="{ 'line-through': hasActivePlan }"
                >
                  1. Purchase a Plan
                </ULink>
                <ULink
                  to="/customer/onboarding"
                  class="text-orange-600 hover:text-orange-700 block"
                  :class="{ 'line-through': hasOnboarding }"
                >
                  2. Complete Onboarding Survey
                </ULink>
              </UCard>
            </template>
          </UPopover> -->

          <ULink
            v-if="loggedIn"
            :to="homePath"
            :class="{
              'nav-link': true,
              'active-link': $route.path === homePath,
            }"
          >
            <UIcon name="la:home" title="Home" class="w-5 h-5 sm:h-8 sm:w-8" />
            <span>Home</span>
          </ULink>

          <ULink
            v-if="!loggedIn || (isCust && !hasActivePlan)"
            to="/purchase"
            :class="{
              'nav-link': true,
              'active-link': $route.path === '/purchase',
            }"
          >
            <UIcon
              name="la:shopping-bag"
              title="Purchase"
              class="w-5 h-5 sm:h-8 sm:w-8"
            />
            <span>{{ isCust ? "Purchase" : "Pricing" }}</span>
          </ULink>

          <ULink
            v-if="!loggedIn"
            to="/login"
            :class="{
              'nav-link': true,
              'active-link': $route.path === '/login',
            }"
          >
            <UIcon
              name="la:sign-in-alt"
              title="Login"
              class="w-5 h-5 sm:h-8 sm:w-8"
            />
            <span>Login</span>
          </ULink>
          <button v-if="loggedIn" class="nav-link" @click="logout">
            <UIcon
              name="la:sign-out-alt"
              title="Logout"
              class="w-5 h-5 sm:h-8 sm:w-8"
            />
            <span>Logout</span>
          </button>
        </nav>
      </div>
    </header>
  </div>
</template>

<script lang="ts" setup>
import { isAgent, isCustomer } from "~/common/common";
import { reloadNuxtApp } from "nuxt/app";
import type { Database } from "~/lib/applysquad-common/database.types";
import { fetchActivePlans, hasCompletedOnboarding } from "~/common/customer-service";

const hasActivePlan = ref(false);

const supabase = useSupabaseClient<Database>();
const user = useSupabaseUser();
const loggedIn = ref(user.value ? true : false);
const isCust = ref(isCustomer(user.value));
const email = (user.value ? user.value.email : "")!;
const homePath = ref(getHomePath());
const hasOnboarding = ref(await hasCompletedOnboarding(isCust.value, user.value, supabase));


// Check for afid parameter on mount
onMounted(async () => {
  const route = useRoute();
  const afid = route.query.afid as string;

  if (afid) {
    console.log("afid", afid);

    // Set cookie that expires in 30 days
    const affiliateCookie = useCookie("afid", {
      maxAge: 30 * 24 * 60 * 60,
      path: "/",
    });
    affiliateCookie.value = afid;
  }  

  if (isCust.value) {
    const activePlan = await fetchActivePlans(user.value!.id, supabase);
    hasActivePlan.value = activePlan.length > 0;
    console.log("customer hasActivePlan?", activePlan);
  }
});

function getHomePath() {
  if (user.value) {
    if (isAgent(user.value!)) {
      return "/agent";
    } else if (isCust.value) {
      return "/customer";
    } else {
      return "/admin";
    }
  } else {
    return "/login";
  }
}

async function logout() {
  await supabase.auth.signOut();
  reloadNuxtApp();
}
</script>

<style scoped>
.nav-link {
  @apply text-lg text-white hover:text-gray-200 flex flex-col items-center relative cursor-pointer bg-transparent border-none;
  @apply after:block after:content-[''] after:absolute after:h-[2.5px] after:bg-white after:w-full after:scale-x-0 after:hover:scale-x-100 after:transition after:duration-300 after:origin-left after:bottom-[-8px];
}

.nav-link span {
  @apply text-xs sm:text-sm md:text-base;
}

.active-link {
  @apply after:scale-x-100;
}
</style>
