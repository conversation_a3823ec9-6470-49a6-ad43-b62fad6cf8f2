<template>
  <div class="container mx-auto py-8">
    <EmailClient v-if="customer" :customer="customer" />

    <div v-else class="flex justify-center">
      <UIcon name="i-heroicons-arrow-path-20-solid" class="animate-spin" />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Customer } from "~/common/common";
import { fetchCustomer } from "~/common/customer-service";
import type { Database } from "~/lib/applysquad-common/database.types";

definePageMeta({
  middleware: ["auth"],
});

const route = useRoute();
const supabase = useSupabaseClient<Database>();
const customerId = route.query.customerId as string;
const customer = ref<Customer>(await fetchCustomer(customerId, supabase));

</script>
