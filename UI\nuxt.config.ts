// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',
  devtools: { enabled: true },
  modules: ['@nuxtjs/supabase', '@nuxt/ui', '@nuxt/eslint', '@nuxtjs/i18n', 'nuxt-security'],
  css: ['~/assets/css/tailwind.css'],
  colorMode: {
    preference: 'light',
    fallback: 'light',
    hid: 'nuxt-color-mode-script',
    globalName: '__NUXT_COLOR_MODE__',
    componentName: 'ColorScheme',
    classPrefix: '',
    classSuffix: '-mode',
    storageKey: 'nuxt-color-mode',
    dataValue: 'light'
  },
  postcss: {
    plugins: {
      tailwindcss: {},
      autoprefixer: {},
    },
  },
  runtimeConfig: {
    // Expose public environment variables to both client and server
    public: {
      SUPABASE_URL: process.env.SUPABASE_URL,
      SUPABASE_KEY: process.env.SUPABASE_KEY,
      SITE_URL: process.env.SITE_URL,
      NYLAS_API_URL: process.env.NYLAS_API_URL,
      NYLAS_API_KEY: process.env.NYLAS_API_KEY,
      NYLAS_CLIENT_ID: process.env.NYLAS_CLIENT_ID,
      // this must be registered in the Nylas dashboard and kept in sync
      EMAIL_SETUP_CALLBACK_URL: "/complete-email-setup",
    },
  },
  supabase: {
    redirectOptions: {
      login: '/login',
      callback: '/confirm',
      // these are the auth-protected pages
      include: ['/customer', '/agent', '/admin'],
      // WARNING: `undefined` exclude means no pages are auth protected except what is in the "include"
      exclude: ['/*'],
      cookieRedirect: false,
    }
  },
  i18n: {
    defaultLocale: 'en',
    locales: [
      { code: 'en', name: 'English', file: 'en.json' }
    ]
  },
  security: {
    headers: {
      crossOriginEmbedderPolicy: 'unsafe-none', // ✅ Required to allow YouTube iframe
    }
  },
});