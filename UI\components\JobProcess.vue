<template>
  <div>
    <div :class=" { helpBox: true } ">
      We'll put you in a whatsapp group with our
      agents that will assist you in finding and applying for jobs on your behalf.
    </div>
    <div :class=" { helpBox: true } ">
      Our agents will add jobs to your Applysquad dashboard we think you might be
      interested in, but it's up to you to Approve or Decline those jobs.
      We also generate a cover letter. Make sure you like it, and feel free
      to tweak it to add personal touches. Once you Approve a job we'll apply for you.
    </div>
    <div :class=" { helpBox: true } ">
      We put jobs that need actions from you at the top of the list with this icon:
      <UIcon
        name="la:exclamation-triangle"
        class="text-yellow-500 w-7 h-7 "/>
      If you see this, look at the job and follow the instructions.
    </div>
    <div :class=" { helpBox: true } ">
      If the job is a Linkedin Easy Apply, we'll provide the link to follow
      so you can click the "Apply" button on Linkedin. Then, back on your dashboard,
      click the "I applied" button so the job can be cleared from your action list.  
    </div>
    <div :class=" { helpBox: true } ">
      If the job application requires an account, we'll set this up for you, you'll
      just need to relay in the whatsapp chat the verification links and one time password (OTP)
      that are sent to your email.
    </div>
  </div>
</template>

<style>
.helpBox {
  @apply bg-slate-100 rounded-xl p-6 my-2;
}

</style>