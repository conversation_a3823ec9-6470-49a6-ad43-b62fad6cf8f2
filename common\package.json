{"name": "applysquad-common", "version": "1.0.0", "description": "common code for applysquad system", "main": "./dist/index.ts", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "test": "vitest", "lint": "eslint . "}, "author": "", "license": "ISC", "dependencies": {"@ai-sdk/anthropic": "^1.1.15", "@ai-sdk/mistral": "^1.1.14", "@ai-sdk/openai": "^1.2.1", "ai": "^4.1.53", "buffer": "^6.0.3", "pdf-ts": "^0.0.2", "stopwatch-node": "^1.1.0", "tsup": "^8.4.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@evalkit/core": "^0.0.2", "@jest/globals": "^29.7.0", "@types/jest": "^29.5.14", "@types/pdf-parse": "^1.1.4", "dotenv": "^16.4.7", "eslint": "^9.22.0", "globals": "^16.0.0", "ts-jest": "^29.2.6", "ts-node": "^10.9.2", "typescript": "^5.8.2", "typescript-eslint": "^8.26.1", "vitest": "^3.0.8"}}