<template>
  <UCard class="shadow-2xl shadow-gray-700 m-10 max-w bg-white">
    <template #header>
      <p class="text-center">Admin Dashboard</p>
      <p v-if="errorMessage" class="text-red-500 text-center">
        {{ errorMessage }}
      </p>
      <p v-if="successMessage" class="text-green-500 text-center">
        {{ successMessage }}
      </p>
    </template>

    <UTabs :items="tabs">
      <template #customers>
        <UCard class="w-full border-1 items-center my-4">
          <UTabs
            :items="customerItems"
            orientation="vertical"
            :ui="{
              wrapper: 'flex items-start gap-4',
              list: { width: 'w-48' },
            }"
            @change="onChangeCustomers"
          >
            <template #item="{ item }">
              <div class="p-2">
                <p>email: {{ item.data.customer.email }}</p>
                <p>
                  assigned agents:
                  <USelectMenu
                    v-model="item.data.selectedAgentIds"
                    :options="agentSelect"
                    multiple
                    option-attribute="agentName"
                    value-attribute="agentId"
                    class="w-56 inline-block"
                    @change="
                      updateAgentAssignments(
                        item.data.selectedAgentIds,
                        item.data.customer.customerId
                      )
                    "
                  >
                    <template #select-label>
                      {{ getSelectedAgentsLabel(item.data.selectedAgentIds) }}
                    </template>
                  </USelectMenu>
                </p>
                <p>
                  whatsapp group url:
                  <UInput
                    v-model="item.data.customer.whatsappGroupUrl"
                    placeholder="Whatsapp Group URL"
                    class="max-w-56 inline-block"
                    @change="
                      updateWhatsappUrl(item.data.customer.customerId, $event)
                    "
                  />
                </p>
                <p class="mt-2">
                  Auto Search:
                  <UToggle
                    v-model="item.data.customer.autoSearch"
                    @change="
                      updateAutoSearch(item.data.customer.customerId, $event)
                    "
                    class="ml-2"
                  />
                </p>
              </div>
            </template>
          </UTabs>
        </UCard>
      </template>

      <template #agents>
        <UCard class="tabContent shadow-lg w-full">
          <UAccordion
            :items="agentSections"
            multiple
            color="white"
            variant="outline"
            size="sm"
          >
            <template #item="{ item }">
              <div class="p-2">
                <p>email: {{ item.data.agent.agentEmail }}</p>
                <p>
                  assigned customers:
                  {{
                    item.data.assignedCustomers
                      .map((c: Customer) => c.name)
                      .join(", ")
                  }}
                </p>
              </div>
            </template>
          </UAccordion>
        </UCard>
      </template>

      <template #payments="{ item }">
        <UCard class="tabContent shadow-lg w-max">
          <p>Payments will be here</p>
        </UCard>
      </template>
    </UTabs>
  </UCard>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { type Agent, type Customer, type CustomerLite } from "~/common/common";
import type { Database } from "~/lib/applysquad-common/database.types";

definePageMeta({
  middleware: ["auth"],
});

interface CustomerAgent {
  customerId: string;
  name: string;
  email: string;
}

interface AgentSection {
  label: string;
  data: {
    agent: Agent;
    assignedCustomers: CustomerAgent[];
  };
}

interface CustomerSection {
  label: string;
  data: {
    customer: CustomerLite;
    selectedAgentIds: string[];
  };
}

// Define reactive state variables
const errorMessage = ref("");
const successMessage = ref("");
const supabase = useSupabaseClient<Database>();
const tabs = [
  {
    slot: "customers",
    label: "Customers",
  },
  {
    slot: "agents",
    label: "Agents",
  },
  {
    slot: "payments",
    label: "Payments",
  },
];

async function onChangeCustomers(index: number) {
  try {
    // If we already have selectedAgentIds, no need to fetch again
    if (customerItems.value[index].data.selectedAgentIds?.length > 0) {
      return;
    }

    const customer = customerItems.value[index].data.customer;

    // Only fetch assigned agents if not already fetched
    const assignedAgents = await getAssignedAgents(customer.customerId);

    // Update the selected agent IDs
    customerItems.value[index].data.selectedAgentIds = assignedAgents.map(
      (agent) => agent.agentId
    );
  } catch (error) {
    console.error("Error fetching agent data:", error);
    errorMessage.value = "Failed to fetch agent data";
  }
}

// customers
async function getAssignedAgents(customerId: string): Promise<Agent[]> {
  const { data: assignments, error: assignmentError } = await supabase
    .from("agent_customers")
    .select("agent_id")
    .eq("customer_id", customerId)
    .eq("active", true);

  if (assignmentError) {
    console.error("Error fetching agent assignments:", assignmentError);
    return [];
  }

  if (!assignments || assignments.length === 0) {
    return [];
  }

  const agentIds = assignments.map((a) => a.agent_id);
  const { data: agents, error: agentError } = await supabase
    .from("agents")
    .select("*")
    .in("id", agentIds);

  if (agentError) {
    console.error("Error fetching agents:", agentError);
    return [];
  }

  return agents.map((a) => ({
    agentId: a.id,
    agentName: a.name,
    agentEmail: a.email,
    whatsappNumber: a.whatsapp_number,
    gender: a.gender,
    dateOfBirth: a.date_of_birth,
  }));
}

// Add this function in the script section
async function updateAutoSearch(customerId: string, enabled: boolean) {
  try {
    const { error } = await supabase
      .from("customers")
      .update({ auto_search: enabled })
      .eq("id", customerId);

    if (error) {
      console.error("Failed to update  job search:", error);
      return;
    }

    successMessage.value = "Auto-search setting updated successfully";
    setTimeout(() => (successMessage.value = ""), 3000);
  } catch (error) {
    console.error("Error updating auto-search setting:", error);
    errorMessage.value = "Failed to update auto-search setting";
    setTimeout(() => (errorMessage.value = ""), 5000);
  }
}

async function getCustomerSections(): Promise<CustomerSection[]> {
  const { data: customers, error } = await supabase
    .from("customers")
    .select("id, name, email, whatsapp_group_url, auto_search")
    .order("created_at", { ascending: false });

  if (error) {
    console.error("Error fetching customers:", error);
    return [];
  }

  const sections: CustomerSection[] = [];
  for (const customer of customers) {
    sections.push({
      label: customer.name,
      data: {
        customer: {
          customerId: customer.id,
          name: customer.name,
          email: customer.email,
          whatsappGroupUrl: customer.whatsapp_group_url,
          autoSearch: customer.auto_search,
        },
        selectedAgentIds: [],
      },
    });
  }

  return sections;
}

async function updateAgentAssignments(agentIds: string[], customerId: string) {
  try {
    // Start with deactivating all assignments for this customer
    const { error: deactivateError } = await supabase
      .from("agent_customers")
      .update({ active: false })
      .eq("customer_id", customerId);

    if (deactivateError) {
      console.error("Error deactivating assignments:", deactivateError);
      errorMessage.value = "Failed to update agent assignments";
      return;
    }

    // If there are selected agents, activate or create their assignments
    if (agentIds && agentIds.length > 0) {
      const assignments = agentIds.map((agentId) => ({
        agent_id: agentId,
        customer_id: customerId,
        active: true,
      }));

      // Use upsert to handle existing assignments
      const { error: upsertError } = await supabase
        .from("agent_customers")
        .upsert(assignments, {
          onConflict: "agent_id,customer_id",
          ignoreDuplicates: false,
        });

      if (upsertError) {
        console.error("Error updating assignments:", upsertError);
        errorMessage.value = "Failed to update agent assignments";
        return;
      }
    }

    successMessage.value = "Agent assignments updated successfully";
    setTimeout(() => {
      successMessage.value = "";
    }, 3000);

    // Clean up old inactive assignments
    const { error: cleanupError } = await supabase
      .from("agent_customers")
      .delete()
      .eq("customer_id", customerId)
      .eq("active", false);

    if (cleanupError) {
      console.error("Error cleaning up inactive assignments:", cleanupError);
      // Don't show this error to the user since the main operation succeeded
    }

    // Refresh the sections
    customerItems.value = await getCustomerSections();
    agentSections.value = await getAgentSections();
  } catch (error) {
    console.error("Error in updateAgentAssignments:", error);
    errorMessage.value =
      "An unexpected error occurred while updating agent assignments";
  }
}

function getSelectedAgentsLabel(agentIds: string[]): string {
  if (!agentIds || agentIds.length === 0) return "No agents assigned";
  const selectedAgents = agentSelect.value.filter((a) =>
    agentIds.includes(a.agentId)
  );
  return selectedAgents.map((a) => a.agentName).join(", ");
}

const agentSelect = ref<Agent[]>([]);
const { data: agentData, error: agentError } = await supabase
  .from("agents")
  .select("*")
  .eq("active", true)
  .order("name");

if (agentError) {
  console.error("Error fetching agents:", agentError);
  errorMessage.value = "Failed to fetch agent data:" + agentError.message;
}

if (agentData) {
  agentSelect.value = agentData.map((agent) => ({
    agentId: agent.id,
    agentName: agent.name,
    agentEmail: agent.email,
    whatsappNumber: agent.whatsapp_number,
    gender: agent.gender,
    dateOfBirth: agent.date_of_birth,
  }));
}

// Initialize sections
const customerItems = ref(await getCustomerSections());
async function getAgentSections(): Promise<AgentSection[]> {
  return Promise.all(
    agentSelect.value.map(async (agent) => ({
      label: agent.agentName,
      data: {
        agent: agent,
        assignedCustomers: await getAssignedCustomers(agent.agentId),
      },
    }))
  );
}

const agentSections = ref(await getAgentSections());

async function getAssignedCustomers(agentId: string): Promise<CustomerAgent[]> {
  const { data, error } = await supabase
    .from("agent_customers")
    .select("customer:customers(id, name, email)")
    .eq("agent_id", agentId)
    .eq("active", true);

  if (error) {
    console.error("Error fetching assigned customers:", error);
    return [];
  }

  const transformedData = await Promise.all(
    data?.map(async (item) => {
      return {
        customerId: item.customer.id,
        name: item.customer.name,
        email: item.customer.email,
      };
    }) || []
  );

  return transformedData;
}

// payments
const { data: paymentData, error: paymentError } = await supabase
  .from("payments")
  .select("*");

if (paymentError) {
  errorMessage.value = "Failed to fetch payment data:" + paymentError.message;
}

async function updateWhatsappUrl(customerId: string, whatsappGroupUrl: string) {
  const { error } = await supabase
    .from("customers")
    .update({ whatsapp_group_url: whatsappGroupUrl })
    .eq("id", customerId);

  if (error) {
    console.error("Error updating whatsapp url:", error);
    errorMessage.value = "Failed to update WhatsApp URL";
    return;
  }

  successMessage.value = "WhatsApp URL updated successfully";
  setTimeout(() => {
    successMessage.value = "";
  }, 3000);
}
</script>

<style scoped></style>
