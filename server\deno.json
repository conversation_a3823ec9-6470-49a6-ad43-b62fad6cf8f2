{"nodeModulesDir": "auto", "compilerOptions": {"lib": ["deno.window"], "strict": true}, "lint": {"include": ["supabase/functions"], "exclude": ["supabase/functions/tests"], "rules": {"tags": ["recommended"], "include": [], "exclude": ["no-unused-vars", "ban-untagged-todo", "no-empty-interface", "require-await"]}}, "fmt": {"files": {"include": ["server/"]}, "options": {"useTabs": false, "lineWidth": 80, "indentWidth": 2, "singleQuote": true, "proseWrap": "always"}}, "imports": {"@ai-sdk/anthropic": "npm:@ai-sdk/anthropic@1.0.7", "@ai-sdk/mistral": "npm:@ai-sdk/mistral@1.0.9", "@ai-sdk/openai": "npm:@ai-sdk/openai@^1.1.12", "@supabase/auth-js": "npm:@supabase/auth-js@2.67.0", "@supabase/supabase-js": "jsr:@supabase/supabase-js@^2.47.6", "@types/pdf-parse": "npm:@types/pdf-parse@^1.1.4", "@upstash/qstash": "npm:@upstash/qstash@^2.8.1", "@vento/vento": "jsr:@vento/vento@^1.12.15", "ai": "npm:ai@^4.1.54", "ai-sdk": "npm:ai-sdk@1.0.9", "buffer": "npm:buffer@^6.0.3", "date-fns": "npm:date-fns@^4.1.0", "pdf-ts": "npm:pdf-ts@^0.0.2", "postmark": "npm:postmark@^4.0.5", "process": "npm:process@^0.11.10", "stopwatch-node": "npm:stopwatch-node@^1.1.0", "uuid": "npm:uuid@^11.0.5"}}