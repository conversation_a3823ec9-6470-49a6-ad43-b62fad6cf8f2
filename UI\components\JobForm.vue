<template>
  <UModal key="jobModal" v-model="modalOpen" class="max-w-6xl" prevent-close>
    <UCard :ui="{ ring: 'ring-0' }">
      <template #header>
        <div class="grid grid-cols-2 w-full">
          <div class="w-max">Adding job for: {{ customer.name }}</div>
          <ULink class="justify-self-end" @click="modal.close">
            <UIcon
              name="la:times-circle-solid"
              class="text-red-600 h-8 w-8 justify-start"
            />
          </ULink>
        </div>
      </template>

      <template v-if="isReadOnly">
        <div class="bg-blue-50 p-4 rounded-lg mb-4">
          <p class="text-blue-700 text-sm">
            This job exists for another customer. Review the details and click
            "Add to Customer" to add it to this customer.
          </p>
        </div>
      </template>

      <UForm :state="state" @submit.prevent="onSubmit">
        <div class="">
          <UFormGroup label="Job URL" class="">
            <div class="flex gap-2">
              <UInput
                v-model="state.url"
                :disabled="isReadOnly"
                required
                placeholder="https://"
                type="text"
                :color="formErrors.url ? 'red' : undefined"
                class="flex-1"
                @update:model-value="debouncedCheckUrl"
              />
              <UButton
                v-if="!isReadOnly && state.url"
                color="gray"
                variant="ghost"
                icon="i-heroicons-x-mark-20-solid"
                size="sm"
                title="Clear form"
                @click="resetForm"
              />
            </div>
            <p v-if="formErrors.url" class="text-red-500 text-sm mt-1">
              {{ formErrors.url }}
            </p>
          </UFormGroup>

          <div :class="{ 'opacity-75': isReadOnly }">
            <UFormGroup label="Employer" class="py-2">
              <UInput
                v-model="state.employer"
                :disabled="isReadOnly"
                required
                :color="formErrors.employer ? 'red' : undefined"
              />
              <p v-if="formErrors.employer" class="text-red-500 text-sm mt-1">
                {{ formErrors.employer }}
              </p>
            </UFormGroup>

            <UFormGroup label="Job Title" class="py-2">
              <UInput
                v-model="state.jobTitle"
                :disabled="isReadOnly"
                required
                :color="formErrors.jobTitle ? 'red' : undefined"
              />
              <p v-if="formErrors.jobTitle" class="text-red-500 text-sm mt-1">
                {{ formErrors.jobTitle }}
              </p>
            </UFormGroup>

            <UFormGroup label="Description" class="py-2">
              <UTextarea
                v-model="state.description"
                :disabled="isReadOnly"
                :color="formErrors.description ? 'red' : undefined"
              />
              <p
                v-if="formErrors.description"
                class="text-red-500 text-sm mt-1"
              >
                {{ formErrors.description }}
              </p>
            </UFormGroup>

            <UFormGroup label="Location" class="py-2">
              <UInput
                v-model="state.location"
                :disabled="isReadOnly"
                required
                :color="formErrors.location ? 'red' : undefined"
              />
              <p v-if="formErrors.location" class="text-red-500 text-sm mt-1">
                {{ formErrors.location }}
              </p>
            </UFormGroup>

            <UFormGroup label="Job Type" class="py-2">
              <USelectMenu
                v-model="state.jobType"
                :options="jobTypes"
                option-attribute="label"
                value-attribute="value"
                :disabled="isReadOnly"
                :color="formErrors.jobType ? 'red' : undefined"
              />
              <p v-if="formErrors.jobType" class="text-red-500 text-sm mt-1">
                {{ formErrors.jobType }}
              </p>
            </UFormGroup>

            <UFormGroup label="Pay" class="py-2">
              <div class="flex gap-2">
                <div class="flex-1">
                  <UInput
                    v-model.number="state.payAmount"
                    type="number"
                    step="1"
                    :disabled="isReadOnly"
                    class="w-full"
                    :color="formErrors.payAmount ? 'red' : undefined"
                  />
                  <p
                    v-if="formErrors.payAmount"
                    class="text-red-500 text-sm mt-1"
                  >
                    {{ formErrors.payAmount }}
                  </p>
                </div>
                <div class="w-28">
                  <USelectMenu
                    v-model="state.payCurrency"
                    :options="currencyOptions"
                    value-attribute="value"
                    option-attribute="label"
                    :disabled="isReadOnly"
                    class="w-full"
                    :color="formErrors.payCurrency ? 'red' : undefined"
                  />
                  <p
                    v-if="formErrors.payCurrency"
                    class="text-red-500 text-sm mt-1"
                  >
                    {{ formErrors.payCurrency }}
                  </p>
                </div>
                <div class="w-28">
                  <USelectMenu
                    v-model="state.payFrequency"
                    :options="payFrequencies"
                    :disabled="isReadOnly"
                    class="w-full"
                    :color="formErrors.payFrequency ? 'red' : undefined"
                  />
                  <p
                    v-if="formErrors.payFrequency"
                    class="text-red-500 text-sm mt-1"
                  >
                    {{ formErrors.payFrequency }}
                  </p>
                </div>
              </div>
            </UFormGroup>

            <UFormGroup label="Languages (select all)" class="py-2">
              <USelectMenu
                v-model="state.languages"
                :options="languages"
                :disabled="isReadOnly"
                multiple
                :color="formErrors.languages ? 'red' : undefined"
              />
              <p v-if="formErrors.languages" class="text-red-500 text-sm mt-1">
                {{ formErrors.languages }}
              </p>
            </UFormGroup>

            <UFormGroup label="Visa Required" class="py-2">
              <UInput
                v-model="state.visaRequired"
                :disabled="isReadOnly"
                :color="formErrors.visaRequired ? 'red' : undefined"
              />
              <p
                v-if="formErrors.visaRequired"
                class="text-red-500 text-sm mt-1"
              >
                {{ formErrors.visaRequired }}
              </p>
            </UFormGroup>

            <UFormGroup
              label="Does this job require applicant to create a new account on the employer's website?"
              class="py-2"
              title="new account required?"
            >
              <UToggle
                v-model="state.accountRequired"
                :disabled="isReadOnly"
                :color="formErrors.accountRequired ? 'red' : 'orange'"
              />
              <p
                v-if="formErrors.accountRequired"
                class="text-red-500 text-sm mt-1"
              >
                {{ formErrors.accountRequired }}
              </p>
            </UFormGroup>

            <UFormGroup
              label="LinkedIn Easy apply"
              class="py-2"
              title="LinkedIn Easy apply?"
            >
              <UToggle
                v-model="state.easyApply"
                :disabled="isReadOnly"
                :color="formErrors.easyApply ? 'red' : 'orange'"
              />
              <p v-if="formErrors.easyApply" class="text-red-500 text-sm mt-1">
                {{ formErrors.easyApply }}
              </p>
            </UFormGroup>
          </div>
        </div>

        <UButton
          type="submit"
          class="button-bg-gradient my-4 justify-center py-3 text-base w-full"
          :disabled="!state.url || !state.employer || !state.jobTitle || !state.location"
        >
          {{ isReadOnly ? "Add to Customer" : "Add Job" }}
        </UButton>
      </UForm>
    </UCard>
  </UModal>
</template>

<script lang="ts" setup>
import type { Customer, Agent, CustomerJob } from "../common/common";
import {
  JobStatus,
  JobType,
  CustomerJobStatus,
  languages,
  payFrequencies,
  currencyOptions,
  jobTypes,
} from "../common/common";
import type { Database } from "~/lib/applysquad-common/database.types";
import { v4 as uid } from "uuid";
import { z } from "zod";
import { useDebounceFn } from "@vueuse/core";
import { toCustomerJob } from "~/common/customer-service";

interface Props {
  customer: Customer;
  agent: Agent;
  success: (job: CustomerJob) => void;
}

type JobFormSchema = z.infer<typeof jobFormSchema>;
const formErrors = reactive<Partial<Record<keyof JobFormSchema, string>>>({});
const modal = useModal();
const props = defineProps<Props>();
const supabase = useSupabaseClient<Database>();
const modalOpen = ref(false);
const toast = useToast();
const toastTimeout = 4000;
const isReadOnly = ref(false);
const existingJob = ref<any>(null);
const originalUrl = ref("");

// Zod validation schema
const jobFormSchema = z.object({
  url: z
    .string()
    .min(1, "URL is required")
    .refine((val) => {
      try {
        new URL(val);
        return true;
      } catch {
        return false;
      }
    }, "Please enter a valid URL")
    .refine(
      (val) => val.startsWith("https://"),
      "URL must start with https://"
    ),
  employer: z.string().min(1, "This field is required"),
  jobTitle: z.string().min(1, "This field is required"),
  jobType: z.nativeEnum(JobType, {
    errorMap: () => ({ message: "Please select a valid Job type" }),
  }),
  location: z.string().min(1, "This field is required"),
  payAmount: z.number().optional(),
  payCurrency: z.string().optional(),
  payFrequency: z.string().optional(),
  description: z.string().optional(),
  languages: z.array(z.string()),
  accountRequired: z.boolean(),
  visaRequired: z.string().optional(),
  easyApply: z.boolean(),
});

// Form state
const state = reactive<JobFormSchema>({
  url: "",
  employer: "",
  jobTitle: "",
  jobType: JobType.FULLTIME as JobType,
  location: "",
  payAmount: 0,
  payCurrency: "",
  payFrequency: "",
  description: "",
  languages: [],
  accountRequired: false,
  visaRequired: "",
  easyApply: false,
});

// Only validate on form submission
const validateForm = (): boolean => {
  try {
    jobFormSchema.parse(state);
    Object.keys(formErrors).forEach((key) => {
      delete formErrors[key as keyof JobFormSchema];
    });
    return true;
  } catch (error) {
    if (error instanceof z.ZodError) {
      error.errors.forEach((err) => {
        const field = err.path[0] as keyof JobFormSchema;
        formErrors[field] = err.message;
      });
    }
    return false;
  }
};

// Load existing job data into form
const loadExistingJob = (job: any) => {
  state.url = job.url;
  state.employer = job.employer;
  state.jobTitle = job.title;
  state.jobType = job.job_type;
  state.location = job.location;
  state.payAmount = job.pay_amount;
  state.payCurrency = job.pay_currency;
  state.payFrequency = job.pay_frequency;
  state.description = job.description;
  state.languages = job.languages ? job.languages.split(",") : [];
  state.accountRequired = job.account_required;
  state.visaRequired = job.visa_required;
  state.easyApply = job.customer_apply;
  isReadOnly.value = true;
  existingJob.value = job;
};

const debouncedCheckUrl = useDebounceFn(async () => {
  if (!state.url) {
    formErrors.url = "URL is required";
    return;
  }
  try {
    new URL(state.url);
    if (!state.url.startsWith('https://')) {
      formErrors.url = "URL must start with https://";
      return;
    }
    await checkDuplicateUrl();
  } catch {
    formErrors.url = "Please enter a valid URL";
  }
}, 300);

watch(
  () => state.url,
  async (newUrl, oldUrl) => {
    if (isReadOnly.value) {
      state.url = originalUrl.value;
      return;
    }

    // Clear previous errors
    delete formErrors.url;

    // Only check for duplicates if we have a complete URL
    if (newUrl && newUrl.startsWith("https://")) {
      await debouncedCheckUrl();
    }
  },
  { immediate: false }
);

async function checkDuplicateUrl() {
  if (!state.url) return;

  const { data: existingJobs, error: jobError } = await supabase
    .from("jobs")
    .select("*")
    .eq("url", state.url)
    .limit(1);

  if (jobError) {
    console.error("Error checking for duplicate job:", jobError);
    return;
  }

  if (!existingJobs || existingJobs.length === 0) {
    formErrors.url = "";
    return;
  }

  const job = existingJobs[0];

  const { data: customerJobs, error: customerJobError } = await supabase
    .from("customer_jobs")
    .select("*")
    .eq("job_id", job.id)
    .eq("customer_id", props.customer.customerId)
    .limit(1);

  if (customerJobError) {
    console.error("Error checking customer jobs:", customerJobError);
    return;
  }

  if (customerJobs && customerJobs.length > 0) {
    toast.add({
      description: "Customer already has this job",
      color: "orange",
      timeout: toastTimeout,
    });
    formErrors.url = "Customer already has this job";
    return;
  }

  loadExistingJob(job);
  toast.add({
    description:
      "This job exists for another customer, do you want to add it to this customer?",
    color: "blue",
    timeout: toastTimeout,
  });
}

async function addExistingJobToCustomer() {
  if (!existingJob.value) return;

  const { data: customerJob, error: customerJobError } = await supabase
    .from("customer_jobs")
    .insert({
      customer_id: props.customer.customerId,
      job_id: existingJob.value.id,
      agent_id: props.agent.agentId,
      status: CustomerJobStatus.NEW,
    })
    .select("*, job:jobs!inner(*)")
    .single();

  if (customerJobError) {
    toast.add({
      description: "Error adding job to customer",
      color: "red",
      timeout: toastTimeout,
    });
    throw customerJobError;
  }

  toast.add({
    description: `${state.jobTitle}@${state.employer} added for ${props.customer.name}`,
    color: "green",
    timeout: toastTimeout,
  });

  props.success(toCustomerJob(customerJob, customerJob.job));
}

async function onSubmit() {
  // If in read-only mode, just add the existing job to customer
  if (isReadOnly.value) {
    await addExistingJobToCustomer();
    return;
  }

  // Validate form only on submit
  if (!validateForm()) return;

  await checkDuplicateUrl();
  if (formErrors.url) return;

  const jobId = uid();

  const { error: jobError } = await supabase.from("jobs").insert({
    id: jobId,
    url: state.url!,
    employer: state.employer!,
    title: state.jobTitle!,
    location: state.location!,
    pay_amount: state.payAmount,
    pay_currency: state.payCurrency,
    pay_frequency: state.payFrequency,
    description: state.description!,
    languages: state.languages.join(","),
    visa_required: state.visaRequired,
    account_required: state.accountRequired,
    job_type: state.jobType,
    customer_apply: state.easyApply,
    create_src: "AGENT",
    src_id: props.agent.agentId,
  });

  if (jobError) {
    toast.add({
      description: "Error creating job",
      color: "red",
      timeout: toastTimeout,
    });
    throw jobError;
  }

  const { data: customerJob, error: customerJobError } = await supabase
    .from("customer_jobs")
    .insert({
      customer_id: props.customer.customerId,
      job_id: jobId,
      agent_id: props.agent.agentId,
      status: CustomerJobStatus.NEW,
    })
    .select("*, job:jobs!inner(*)")
    .single();

  if (customerJobError) {
    toast.add({
      description: "Error adding job to customer",
      color: "red",
      timeout: toastTimeout,
    });
    throw customerJobError;
  }

  toast.add({
    description: `${state.jobTitle}@${state.employer} added for ${props.customer.name}`,
    color: "green",
    timeout: toastTimeout,
  });

  props.success(toCustomerJob(customerJob, customerJob.job));
}

function resetForm() {
  state.url = "";
  state.employer = "";
  state.jobTitle = "";
  state.jobType = JobType.FULLTIME as JobType;
  state.location = "";
  state.payAmount = 0;
  state.payCurrency = "";
  state.payFrequency = "";
  state.description = "";
  state.languages = [];
  state.accountRequired = false;
  state.visaRequired = "";
  state.easyApply = false;
}
</script>
