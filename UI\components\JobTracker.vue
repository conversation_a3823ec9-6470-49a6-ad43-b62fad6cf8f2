<template>
  <div>
    <div v-if="isLoading">
      <div class="flex flex-col items-center justify-center h-80">
        <UIcon
          name="i-heroicons-arrow-path-20-solid"
          class="w-8 h-8 animate-spin"
        />
        <p class="mt-2 text-sm">Updating status...</p>
      </div>
    </div>
    <div class="flex gap-5">
      <div class="w-52">
        <UFormGroup label="Filter Jobs" class="my-3">
          <USelectMenu
            :model-value="selectedJobFilter"
            :options="[
              'ALL',
              'NEW',
              'OTP',
              'EASY APPLY',
              'APPROVED',
              'DECLINED',
              'APPLIED',
            ]"
            @change="filterJobs($event)"
          />
        </UFormGroup>
        <div v-if="filteredJobSections.length === 0  && !isLoading">
          No jobs found
        </div>
      </div>
    </div>

    <UAccordion
      v-show="!isLoading"
      ref="accordionRef"
      :items="filteredJobSections"
      multiple
      color="white"
      variant="outline"
      size="sm"
    >
      <template #default="{ item, open }">
        <UButton
          color="gray"
          variant="soft"
          class="border border-gray-300 text-slate-700 mb-3"
          :ui="{ rounded: 'rounded-lg', padding: { sm: 'p-3' } }"
        >
          <slot name="leading"/>

          <span class="flex items-center">
            <ULink
              :to="`/jobs/${item.job.id}`"
              class="text-orange-600 hover:text-orange-400 hover:underline mr-2"
            >
              {{ item.job.title }}
            </ULink>

            @ {{ item.job.employer }} ({{
              $t(`job.status.${item.job.status}`)
            }})
            <ClientOnly>
              <UTooltip
                v-if="item.job.id"
                text="Click to copy job link"
                :popper="{
                  arrow: false,
                  strategy: 'fixed',
                  placement: 'top',
                }"
              >
                <UButton
                  icon="la:link"
                  variant="ghost"
                  color="gray"
                  size="xs"
                  class="ml-2 p-1"
                  @click.stop="copyJobUrl(item.job.id)"
                />
              </UTooltip>
            </ClientOnly>
          </span>
          
          <UIcon
            v-if="item.requiresAction"
            name="la:exclamation-triangle"
            class="text-yellow-500 w-8 h-8"
            
          />
          <template #trailing>
            <UIcon
              name="la:chevron-down"
              class="w-5 h-5 ms-auto transform transition-transform duration-200"
              :class="[open && 'rotate-180']"
            />
          </template>
        </UButton>
      </template>

      <template #item="{ item }">
        <JobDetail
          v-model:job="item.job"
          v-model:job-documents="jobDocuments"
          v-model:documents="documents"
          :is-customer="props.isCustomer"
        />
      </template>
    </UAccordion>

  </div>
</template>

<script lang="ts" setup>
import type { Database } from "~/lib/applysquad-common/database.types";
import {
  CustomerJobStatus,
  DocumentType,
  isCustomer,
  type Customer,
  type CustomerJob,
  type Document,
  type JobDocument,
} from "../common/common";
import { fetchJobDocumentsForCustomer } from "~/common/customer-service";

interface Props {
  customer: Customer;
  isCustomer: boolean;
}

interface CustomerJobSection {
  key: number;
  label: string;
  job: CustomerJob;
  requiresAction: boolean;
}

const props = defineProps<Props>();
const jobs = defineModel<CustomerJob[]>("jobs", { default: [] });
const documents = defineModel<Document[]>("documents", { default: [] });

const i18n = useI18n().t;
const supabase = useSupabaseClient<Database>();
// built from all the customer jobs, key is customerJob.id
const customerJobSections = ref<CustomerJobSection[]>([]);
// built from customerJobSections + jobFilter
const filteredJobSections = ref<CustomerJobSection[]>([]);

const toast = useToast();
const jobDocuments = ref<JobDocument[]>([]);
const isLoading = ref(false);
const accordionRef = ref<any>(null);
const config = useRuntimeConfig();

const selectedJobFilter = ref("ALL");
const jobFilters: Record<string, (section: CustomerJobSection) => boolean> = {
    "EASY APPLY": (section) => section.job.customerApply === true,
    "OTP": (section) => section.job.accountRequired === true,
    "NEW": (section) => section.job.status === CustomerJobStatus.NEW,
    "APPROVED": (section) => section.job.status === CustomerJobStatus.APPROVED,
    "DECLINED": (section) =>
      [CustomerJobStatus.DECLINED, CustomerJobStatus.DISSATISFIED].includes(
        section.job.status
      ),
    "APPLIED": (section) =>
      [CustomerJobStatus.APPLIED, CustomerJobStatus.CUSTOMER_APPLIED, CustomerJobStatus.SATISFIED].includes(
        section.job.status
      ),
    "ALL": (section) => true
  };




async function refreshCustomerJobSections() {

  isLoading.value = true;

  const jobSections = await Promise.all(
    jobs.value.map(async (job: CustomerJob) => {
      return await buildJobSection(job);
    })
  );

  customerJobSections.value = sortJobSections(jobSections);
  await filterJobs(selectedJobFilter.value);
  isLoading.value = false;
}


async function buildJobSection(job: CustomerJob): Promise<CustomerJobSection> {

  const section: CustomerJobSection = {
        key: job.id,
        label:
          job.title +
          " @ " +
          job.employer +
          "   (" +
          i18n(`job.status.${job.status}`) +
          ")",
        requiresAction: requiresAction(job, props.isCustomer),
        job: job
      };
  return section;
}

function requiresAction(job: CustomerJob, isCustomer: boolean): boolean {
  if (isCustomer) {
    return (
      job.status === CustomerJobStatus.NEW ||
      job.status === CustomerJobStatus.APPLIED
    );
  } else {
    return job.status === CustomerJobStatus.APPROVED;
  }
}

function  sortJobSections(sections: CustomerJobSection[]): CustomerJobSection[] {
  /**
   * Sort order depends on whether this is a customer or agent view. We want
   * actionable jobs to appear first.
   */
  const customerStatusRank = {
    NEW: 1,
    APPLIED: 2,
    APPROVED: 3,
    CUSTOMER_APPLIED: 4,
    SATISFIED: 5,
    DECLINED: 6,
    DISSATISFIED: 7,
    EXPIRED: 8,
    DELETED: 9,
  };
  const agentStatusRank = {
    APPROVED: 1,
    NEW: 2,
    APPLIED: 3,
    CUSTOMER_APPLIED: 4,
    SATISFIED: 5,
    DECLINED: 6,
    DISSATISFIED: 7,
    EXPIRED: 8,
    DELETED: 9,
  };
  const customerSorter = (
    a: CustomerJobSection,
    b: CustomerJobSection
  ): number => {
    if (customerStatusRank[a.job.status] < customerStatusRank[b.job.status]) {
      return -1;
    } else if (
      customerStatusRank[a.job.status] > customerStatusRank[b.job.status]
    ) {
      return 1;
    } else {
      /* if (customerStatusRank[a.job.status] === customerStatusRank[b.job.status]) { */
      return 0; // TODO: date comparision
    }
  };
  const agentSorter = (
    a: CustomerJobSection,
    b: CustomerJobSection
  ): number => {
    if (agentStatusRank[a.job.status] < agentStatusRank[b.job.status]) {
      return -1;
    } else if (agentStatusRank[a.job.status] > agentStatusRank[b.job.status]) {
      return 1;
    } else {
      /* if (agentStatusRank[a.job.status] === agentStatusRank[b.job.status]) { */
      return 0; // TODO: date comparision
    }
  };

  if (props.isCustomer) {
    sections.sort(customerSorter);
  } else {
    sections.sort(agentSorter);
  }

  return sections;
}

async function filterJobs(selected: string,) {

  // save the selected filter so we can reuse when a job changes
  selectedJobFilter.value = selected;

  filteredJobSections.value = customerJobSections.value.filter(jobFilters[selected]);
};


function closeAll() {
  if (!accordionRef.value?.buttonRefs) return;
  accordionRef.value.buttonRefs.forEach((btn: any) => btn?.close?.());
}

function copyJobUrl(jobId: number) {
  const baseUrl = config.public.SITE_URL || window.location.origin;
  // Ensure the URL has a protocol
  const url = baseUrl.startsWith("http") ? baseUrl : `https://${baseUrl}`;

  const fullUrl = `${url}/jobs/${jobId}`;

  navigator.clipboard.writeText(fullUrl);
  toast.add({
    description: "Job URL has been copied to clipboard",
    color: "green",
    timeout: 1000,
  });
}

watch(
  () => jobs.value?.map(job => ({  status: job.status })),
  async () => {
    const currentScroll = window.scrollY;
    await refreshCustomerJobSections();
    closeAll();
    // Restore scroll after DOM has re-rendered
    requestAnimationFrame(() => {
      window.scrollTo(0, currentScroll);
    });
  },
  { deep: false }
);



watch(
  documents.value,
  async () => {
    console.log('tracker updating docs');
    jobDocuments.value = await fetchJobDocumentsForCustomer(props.customer.customerId, supabase);
  },
  // don't need a deep watch, as docs themselves don't change. 
  // Just need to know if docs are added or removed from list.
  { deep: false }
)

onMounted(async () => {
  jobDocuments.value = await fetchJobDocumentsForCustomer(props.customer.customerId, supabase);
  await refreshCustomerJobSections();
})

</script>

<style></style>
