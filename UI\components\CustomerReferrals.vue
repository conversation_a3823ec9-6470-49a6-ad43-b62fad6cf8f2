<script lang="ts" setup>
import type { Customer } from "~/common/common";
import { useSupabaseClient } from "#imports";
import type { Database } from "~/lib/applysquad-common/database.types";
import { format } from "date-fns"; // Import date-fns
import Papa from "papaparse";

const props = defineProps<{
  customer: Customer;
}>();

const supabase = useSupabaseClient<Database>();
const getCustomers = ref<any>([]);

const loadCustomerReferrals = async () => {
  try {
    // Fetch data from the Supabase table
    const { data, error } = await supabase
      .from("customers")
      .select("name, created_at, affiliate_id")
      .eq("referred_by_id", props.customer.affiliateId as string);

    if (error) {
      console.error("Error loading documents:", error);
      return;
    }

    if (data) {
      getCustomers.value = data.map((customer) => ({
        ...customer,
        created_at: format(new Date(customer.created_at), "MMMM d, yyyy"), // Format date
      }));
    }
  } catch (err) {
    console.error("Unexpected error loading documents:", err);
  }
};

const documentColumns = [
  {
    label: "Customer Name",
    key: "name",
    sortable: true,
  },
  {
    label: "Signup Date",
    key: "created_at",
    sortable: true,
  },
  {
    label: "Affiliate Id",
    key: "affiliate_id",
    sortable: true,
  },
];

// Function to export data to CSV
const exportToCSV = () => {
  if (getCustomers.value.length === 0) {
    alert("No data to export.");
    return;
  }

  // Transform data to include only name, created_at, and affiliate_id
  const filteredData = getCustomers.value.map((customer: any) => ({
    name: customer.name, // No manual quotes
    created_at: customer.created_at, // No manual quotes
    affiliate_id: customer.affiliate_id, // No manual quotes
  }));

  // Convert data to CSV using Papa Parse
  const csv = Papa.unparse(filteredData, {
    header: true, // Include headers in the CSV
    quotes: true, // Ensure quotes are added around strings
    escapeFormulae: true, // Escape formulae to prevent CSV injection
  });

  // Create a Blob and download the CSV file
  const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = "customer_referrals.csv"; // File name
  link.click();
  URL.revokeObjectURL(link.href);
};

// Initialize component
onMounted(async () => {
  await loadCustomerReferrals();
});
</script>
<template>
  <div class="w-full">
    <div class="flex justify-between items-center">
      <h3 class="text-lg font-semibold text-orange-600">Referrals</h3>
      <div v-if="getCustomers.length" class="flex justify-end mb-4">
        <UButton
          icon="i-la-file-csv"
          label="Export to CSV"
          variant="outline"
          @click="exportToCSV"
        />
      </div>
    </div>
    <UTable
      :rows="getCustomers"
      :columns="documentColumns"
      :ui="{
        wrapper: 'relative overflow-x-auto',
        base: 'min-w-full table-fixed',
        td: {
          base: 'truncate max-w-[10px]',
          padding: 'px-1 sm:px-4 py-2',
        },
        th: {
          padding: 'px-1 sm:px-4 py-2',
          font: 'font-medium',
        },
      }"
      :empty-state="{
        icon: 'i-heroicons-circle-stack-20-solid',
        label: 'No Record Found.',
      }"
    />
  </div>
</template>
