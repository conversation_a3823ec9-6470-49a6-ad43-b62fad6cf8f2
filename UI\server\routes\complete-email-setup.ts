import type { Database } from '~/lib/applysquad-common/database.types';
import { serverSupabaseClient } from '#supabase/server'
import { NYLAS_CUSTOMER_STATUS } from "~/common/common"
import Nylas from "nylas";
import type { SupabaseClient } from '@supabase/supabase-js';
import type { H3Event } from 'h3';


export default defineEventHandler(async (event:H3Event) => {

  const supabase = await serverSupabaseClient<Database>(event);
  const customerId = getQuery(event).state;
  const nylasCode = getQuery(event).code;
  const error = getQuery(event).error;
  
  if (error) {
    const errorCode = getQuery(event).error_code;
    const errorDescription = getQuery(event).error_description;
    const errorMessage = `coulde not setup email access because: ${errorDescription}`;
    console.error(errorMessage, error, errorCode);
    return sendRedirect(event, `/customer?error_message=${errorMessage}`);
  }
  if (!nylasCode) {
    throw new Error("no nylas code");
  }

  await updateNylasCustomerCode(customerId!.toString(), nylasCode.toString(), supabase);

  const grant = await exchangeCodeForGrant(customerId!.toString(), nylasCode.toString(), event);

  await updateNylasCustomerGrant(customerId!.toString(), grant, supabase);

  return sendRedirect(event, "/customer")
})

async function updateNylasCustomerCode(customerId: string, code: string, supabase: SupabaseClient<Database>) {
  const {error} = await supabase
    .from("nylas_customers")
    .update({ code: code, status: NYLAS_CUSTOMER_STATUS.AUTHENTICATED })
    .eq("customer_id", customerId);
  if (error) {
    console.log("nylas update error", error);
    throw error;
  }
}

async function updateNylasCustomerGrant(customerId: string, grant: string, supabase: SupabaseClient<Database>) {
  const {error} = await supabase
    .from("nylas_customers")
    .update({grant: grant, status: NYLAS_CUSTOMER_STATUS.READY })
    .eq("customer_id", customerId);
  if (error) {
    console.error("could not update nylas customer grant because", error);
    throw error;
  }
}

async function exchangeCodeForGrant(customerId: string, code: string, event:H3Event): Promise<string> {

  try {

    const config = useRuntimeConfig(event);
    const nylasConfig = {
      clientId: config.public.NYLAS_CLIENT_ID,
      callbackUri: `${config.public.SITE_URL}${config.public.EMAIL_SETUP_CALLBACK_URL}`,
      apiKey: config.public.NYLAS_API_KEY,
      apiUri: config.public.NYLAS_API_URL,
    };

    const nylas = new Nylas({
      apiKey: nylasConfig.apiKey,
      apiUri: nylasConfig.apiUri,
    });

    const response = await nylas.auth.exchangeCodeForToken({ 
      clientId: nylasConfig.clientId,
      clientSecret: nylasConfig.apiKey,
      redirectUri: nylasConfig.callbackUri,
      code: code,
    })

    const { grantId } = response

    return grantId;

  } catch (error) {
    console.error('error during fetch', error)
    throw error;
  }
}

