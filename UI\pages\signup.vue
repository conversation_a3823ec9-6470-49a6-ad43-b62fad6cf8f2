<template>
  <div
    class="data-entry-container py-16 px-8 bg-gradient-to-r from-red-500 to-orange-400"
  >
    <UCard class="shadow-2xl shadow-gray-700 mx-auto max-w-xl bg-white">
      <template #header>
        <h1
          class="text-xl sm:text-4xl font-bold text-center text-gray-700 mb-2"
        >
          Sign Up Now!
        </h1>
      </template>

      <UForm :state="state" @submit="onSubmit">
        <div class="text-sm font-medium text-gray-700">
          <!-- Name -->
          <UFormGroup label="Name" name="name" class="my-3">
            <UInput v-model="state.name" required />
          </UFormGroup>

          <UFormGroup label="Email" name="email" class="my-3">
            <UInput
              v-model="state.email"
              type="email"
              placeholder="<EMAIL>"
              required
            />
          </UFormGroup>

          <UFormGroup label="WhatsApp Number" name="whatsapp" class="my-3">
            <UInput v-model="state.whatsapp" required />
          </UFormGroup>

          <!-- <UFormGroup label="University" name="university" class="my-3">
            <UInput v-model="state.university" />
          </UFormGroup> -->

          <UFormGroup label="Linkedin URL" name="linkedinUrlo" class="my-3">
            <UInput
              v-model="state.linkedinUrl"
              placeholder="paste your linkedin profile URL"
              required
            />
          </UFormGroup>

          <UFormGroup
            label="Do you use Linkedin Premium?"
            name="linkedinPremium"
            class="my-3"
          >
            <UToggle v-model="state.linkedinPremium" color="orange" />
          </UFormGroup>

          <UFormGroup
            label="What are your pain points regarding job search, job applications, and doing networking outreach?"
            name="painPoints"
            class="my-3"
          >
            <USelectMenu
              v-model="state.painPoints"
              value-attribute="value"
              option-attribute="label"
              multiple
              :options="painPointOptions"
              required
            />
          </UFormGroup>

          <UFormGroup
            label="Other pain points"
            name="otherPainPoints"
            class="my-3"
          >
            <UTextarea v-model="state.otherPainPoints" />
          </UFormGroup>

          <UFormGroup
            label="If you were referred by someone, what is their email?"
            name="referredBy"
            class="my-3"
          >
            <UInput
              v-model="state.referredBy"
              type="email"
              placeholder="<EMAIL>"
            />
          </UFormGroup>

          <UFormGroup label="Password" name="password" class="my-3">
            <UInput v-model="state.password" type="password" required />
          </UFormGroup>

          <UButton
            type="submit"
            :disabled="submitDisabled"
            class="button-bg-gradient my-4 justify-center py-3 sm:text-lg w-full"
          >
            Signup
          </UButton>

          <div v-if="errorMessage" class="text-red-500 text-center">
            {{ errorMessage }}
          </div>
          <div v-if="successMessage" class="text-green-500 text-center">
            {{ successMessage }}
          </div>
        </div>
      </UForm>
    </UCard>
  </div>
</template>

<script setup lang="ts">
import type { Database } from "~/lib/applysquad-common/database.types";
import adler32 from "adler-32";

const affiliateCookie = useCookie("afid");
const config = useRuntimeConfig();
const supabase = useSupabaseClient<Database>();
const painPointOptions = [
  { value: "HASSLE", label: "It's a hassle" },
  { value: "TIME", label: "I don't have the time" },
  { value: "REPETITIVE", label: "It's too repetitive" },
  { value: "BORING", label: "It's a mundane and boring" },
  { value: "OTHER", label: "Something else" },
];
const successMessage = ref("");
const errorMessage = ref("");
const submitDisabled = ref(false);

const state = reactive({
  email: "",
  password: undefined,
  name: undefined,
  whatsapp: undefined,
  // university: undefined,
  linkedinUrl: undefined,
  linkedinPremium: false,
  painPoints: [],
  otherPainPoints: undefined,
  referredBy: undefined,
});

async function onSubmit() {
  errorMessage.value = "";
  successMessage.value = "please wait...signing you up";

  // Generate the affiliateId from the user's email
  const affiliateId = adler32.str(state.email!);
  // Optionally, convert to an 8-character hex string if needed:
  const affiliateIdHex = affiliateId.toString(16).padStart(8, "0");

  try {


    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: state.email?.toLowerCase().trim(),
      password: state.password!,
      options: {
        emailRedirectTo: `${config.public.SITE_URL}/login`,
        data: {
          role: "customer",
        },
      },
    });
    if (authError) {
      const authErrorMessage = "Error signing up. " + authError.message;
      errorMessage.value = authErrorMessage;
      successMessage.value = "";
      console.log(authErrorMessage);
      return;
    }

    // no auth error, so user.id will be available
    // eslint-disable-next-line
    const userId = authData.user?.id!;

    // create storage bucket
    const { error: storageError } = await supabase.storage.createBucket(userId);

    if (storageError) {
      console.error("could not create customer storage bucket", storageError);
      successMessage.value = "";
      throw storageError;
    }

    // create customer
    const { error } = await supabase.from("customers").insert({
      id: userId,
      name: state.name!,
      email: state.email?.toLowerCase().trim(),
      whatsapp_number: state.whatsapp,
      linkedin_url: state.linkedinUrl,
      linkedin_premium: state.linkedinPremium,
      pain_points: state.painPoints.join(","),
      other_pain_points: state.otherPainPoints,
      referred_by: state.referredBy,
      affiliate_id: affiliateIdHex,
      referred_by_id: affiliateCookie.value,
    });

    if (error) {
      // Delete the user's bucket if customer creation fails
      const { error: storageDeleteError } = await supabase.storage.deleteBucket(
        userId
      );

      if (storageDeleteError) {
        console.error(
          "Failed to cleanup storage after customer creation error:",
          storageDeleteError
        );
      }

      console.error(error);
      successMessage.value = "";
      errorMessage.value =
        "There is already an account associated with this email";
      return;
    } else {
      successMessage.value =
        "Thank you for signing up! Check your email to confirm your account before login.";
      submitDisabled.value = true;
      affiliateCookie.value = null;
    }
  } catch (error: any) {
    errorMessage.value = error.message;
  }
}
</script>

<style scoped>
/* Custom styles for the form */
</style>
