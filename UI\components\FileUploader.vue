<script setup lang="ts">
import { type Document, DocumentType, enumFromKey, type JobDocument, notNull, toDocument, toJobDocument } from "~/common/common";
import type { Database } from "~/lib/applysquad-common/database.types";

const props = defineProps<{
  documentTypes: DocumentType[];
  customerId: string;
  documentAddedCallback?: (document: Document) => void;
}>();
const toast = useToast();
const supabase = useSupabaseClient<Database>();
const user = useSupabaseUser();
const isUploading = ref(false);
const isPopoverVisible = ref(false);

interface DocumentFormData {
  documentName: string;
  documentType: string;
  file: File | null;
}

const state = ref<DocumentFormData>({
  documentName: "",
  documentType: props.documentTypes.length === 1 ? props.documentTypes[0] : "",
  file: null,
});

const validation = {
  documentName: {
    validate: (value: string) => !!value,
    errorMessage: "Document name is required",
  },
  documentType: {
    validate: (value: string) => {
      return props.documentTypes.length !== 1 || !!value;
    },
    errorMessage:
      props.documentTypes.length === 1
        ? "Document type is automatically set and cannot be empty."
        : "Document type is required.....",
  },
  file: {
    validate: (value: File | null) => !!value,
    errorMessage: "Please select a file",
  },
};

const validateForm = () => {
  const errors = [];
  if (!state.value.documentName) {
    errors.push("Document name is required");
  }

  // Validate document type only if there is more than one document type
  if (props.documentTypes.length > 1 && !state.value.documentType) {
    errors.push("Document type is required.pppp");
  }
  if (!selectedFiles.value || selectedFiles.value.length === 0) {
    errors.push("Please select a file");
  }

  return errors;
};

const convertToUnderscore = () => {
  state.value.documentName = state.value.documentName.replace(/\s+/g, "_");
};

const selectedFiles = ref<File[]>([]);

const handleFileChange = (event: Event | FileList) => {
  let files: FileList | null = null;

  // If event is a FileList (directly emitted), use it.
  if (event instanceof FileList) {
    files = event;
  } else if (event && (event.target || event.currentTarget)) {
    // Otherwise, try to extract from a native event.
    const target = (event.target || event.currentTarget) as HTMLInputElement;
    files = target.files;
  }

  if (files && files.length > 0) {
    selectedFiles.value = Array.from(files);
  } else {
    console.warn("No files found on event", event);
  }
};

const checkFileExists = async (path: string) => {
  const { data, error } = await supabase.storage
    .from(`${props.customerId}`)
    .list(path.split("/")[0], {
      search: path.split("/")[1],
    });

  if (error) throw error;
  return data && data.length > 0;
};

const uploadDocument = async () => {
  const errors = validateForm();
  if (errors.length > 0) {
    toast.add({
      description: "fix the errors: " + errors.join("\n"),
      color: "red",
      timeout: 4000,
    });
    return;
  }

  try {
    isUploading.value = true;

    if (!user.value) throw new Error("User not authenticated");

    // Track the number of files being uploaded
    const totalFiles = selectedFiles.value.length;
    let uploadedFiles = 0;

    for (const file of selectedFiles.value) {
      const fileExt = file?.name.split(".").pop();
      const path = `${state.value.documentType}/${
          state.value.documentName
        }_${Date.now()}.${fileExt}`;;
      
      // Check if file with same name exists
      const fileExists = await checkFileExists(path);
      if (fileExists) {
        toast.add({
          description:
            "A file with this name already exists. Please choose a unique name.",
          color: "orange",
          timeout: 3000,
        });
        continue;
      }

      const pathName =
        props.documentTypes[0] === DocumentType.SCREENSHOT
          ? `${state.value.documentName}_${Date.now()}.${fileExt}`
          : `${state.value.documentName}.${fileExt}`;
      // Insert into database
      const { data, error } = await supabase
        .from("documents")
        .upsert({
          bucket: `${props.customerId}`,
          customer_id: props.customerId,
          path: path,
          document_type: enumFromKey(
            DocumentType,
            state.value.documentType as DocumentType
          ),
          file_name: pathName,
        })
        .select()
        .single();

      if (error) throw error;

      const document = toDocument(data);

      try {
        // Upload to storage
        const { error: uploadError } = await supabase.storage
          .from(`${props.customerId}`)
          .upload(path, file, { upsert: true });

        if (uploadError) {
          // If upload fails, delete the database record
          await supabase.from("documents").delete().match({ id: data.id });
          throw uploadError;
        }

        // Increment the uploaded files counter
        uploadedFiles++;

        // Call successHandler only after all files are uploaded
        if (uploadedFiles === totalFiles && props.documentAddedCallback) {
          props.documentAddedCallback(document);
        }
      } catch (error) {
        // Clean up any partial uploads
        try {
          await supabase.storage.from("documents").remove([path]);
        } catch (cleanupError) {
          console.error("Error cleaning up partial upload:", cleanupError);
        }
        try {
          await supabase.from("documents").delete().eq("id", data.id);
        } catch (deleteError) {
          console.error("Error deleting document:", deleteError);
        }
        throw error;
      }
    }

    // Reset form
    selectedFiles.value = [];
    state.value = {
      documentName: "",
      documentType:
        props.documentTypes.length === 1 ? props.documentTypes[0] : "",
      file: null,
    };

    isPopoverVisible.value = false;
  } catch (error: any) {
    console.error("Error uploading document:", error);
    toast.add({
      description: "Failed to upload document. Please try again.",
      color: "red",
      timeout: 3000,
    });
  } finally {
    isUploading.value = false;
  }
};
</script>

<template>
  <div class="w-min hover:cursor-default">
    <UPopover
      v-model:open="isPopoverVisible"
      :popper="{ placement: 'top-start' }"
    >
      <slot id="button">
        <div class="text-left">
          <UIcon
            class="text-green-500 h-8 w-8 hover:cursor-pointer"
            title="upload a new document"
            name="la:plus-circle"
          />
        </div>
      </slot>

      <template #panel>
        <UContainer class="p-6 rounded-lg">
          <UForm
            :state="state"
            :validation="validation"
            class="flex flex-col gap-4"
            @submit="uploadDocument"
          >
            <UFormGroup label="Document Name" name="documentName">
              <UInput
                v-model="state.documentName"
                placeholder="Enter document name"
                @update:model-value="convertToUnderscore"
              />
            </UFormGroup>

            <UFormGroup
              v-if="props.documentTypes.length > 1"
              label="Document Type"
              name="documentType"
            >
              <USelect
                v-model="state.documentType"
                :options="documentTypes"
                placeholder="Select..."
              />
            </UFormGroup>

            <UFormGroup label="Upload Document" name="file">
              <UInput
                v-if="props.documentTypes[0] === DocumentType.SCREENSHOT"
                type="file"
                size="sm"
                icon="i-heroicons-folder"
                placeholder="upload image"
                multiple
                accept=".jpg,.jpeg,.png"
                @change="handleFileChange"
              />
              <UInput
                v-else
                type="file"
                size="sm"
                icon="i-heroicons-folder"
                placeholder="upload image"
                accept=".pdf,.doc,.docx,.txt,.rtf,.md"
                @change="handleFileChange"
              />
            </UFormGroup>

            <UButton
              type="submit"
              :loading="isUploading"
              :disabled="isUploading"
              class="w-fit"
            >
              Upload
            </UButton>
          </UForm>
        </UContainer>
      </template>
    </UPopover>
  </div>
</template>
