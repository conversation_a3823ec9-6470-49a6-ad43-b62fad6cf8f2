<template>
  <div>
    <h2 class="text-base md:text-xl font-bold">
      What locations would you like to work?
    </h2>
    <div class="divide-y divide-gray-200">
      <div
        v-for="(location, index) in locations"
        :key="index"
        class="md:gap-4 mb-4 divide-y flex flex-col md:flex-row w-full justify-between items-end"
      >
        <div
          class="md:gap-2 mt-2 flex flex-col md:flex-row w-full justify-between"
        >
          <UFormGroup
            label="What job location?"
            class="mt-4 w-full"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <template v-if="isEditing">
              <UInput
                v-model="location.location"
                placeholder="Enter your location"
              />
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{ location.location || "Not specified" }}
              </div>
            </template>
          </UFormGroup>

          <UFormGroup
            label="Authorized to work"
            class="mt-6 w-full px-4 flex flex-col justify-end"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <template v-if="isEditing">
              <UToggle
                v-model="location.work_authorized"
                label="Work Authorized"
              />
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{ location.work_authorized ? "Yes" : "No" }}
              </div>
            </template>
          </UFormGroup>

          <UFormGroup
            label="Salary"
            class="mt-4 w-full"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <template v-if="isEditing">
              <UInput
                v-model="location.pay_requirement"
                placeholder="Enter pay requirement (e.g., 30,000 USD)"
                required
              />
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{ location.pay_requirement || "Not specified" }}
              </div>
            </template>
          </UFormGroup>
        </div>

        <!-- Delete button for each location -->
        <UButton
          v-if="isEditing"
          color="red"
          variant="outline"
          icon="la:trash"
          class="w-fit h-fit mt-5"
          @click="removeLocation(index)"
        />
      </div>

      <!-- Add new location button -->
      <UButton
        v-if="isEditing"
        color="green"
        icon="i-heroicons-plus"
        @click="addLocation"
      >
        Add Location
      </UButton>
    </div>

    <!-- Save button -->
    <div class="flex justify-end items-center gap-3">
      <UButton
        type="button"
        color="orange"
        variant="outline"
        class="mt-4"
        @click="handleBackButtonClick"
      >
        Back
      </UButton>
      <UButton
        type="button"
        variant="outline"
        class="mt-4"
        @click="saveLocations"
      >
        Next
      </UButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type { Database } from "~/lib/applysquad-common/database.types";
import type { Customer } from "~/common/common";

const props = defineProps<{
  customer: Customer;
  data: {
    id: string;
  };
  isEditing: boolean;
  isCustomer: boolean;
  agentTab: number;
  loadData: () => void;
}>();

const emit = defineEmits(["update:isEditing", "update:agentTab"]);

const supabase = useSupabaseClient<Database>();
const toast = useToast();

// Define the structure of a location
interface Location {
  id?: string; // Add id for existing locations
  location: string;
  work_authorized: boolean;
  pay_requirement: string;
}

// Define the payload for updating a location
interface LocationPayload {
  location: string;
  work_authorized: boolean;
  pay_requirement: string;
}

// Reactive array to store locations
const locations = ref<Location[]>([]);

// Fetch existing locations if props.data.id is present
const fetchLocations = async () => {
  if (props?.data?.id) {
    try {
      const { data, error } = await supabase
        .from("onboarding_locations")
        .select("*")
        .eq("onboarding_id", Number(props.data.id));

      if (error) throw error;

      // Populate the locations array with fetched data
      locations.value = data.map((loc: any) => ({
        id: loc.id,
        location: loc.location,
        work_authorized: loc.work_authorized,
        pay_requirement: loc.pay_requirement,
      }));
    } catch (error) {
      console.error("Error fetching locations:", error);
    }
  }
};

// Function to add a new location
const addLocation = () => {
  locations.value.push({
    location: "",
    work_authorized: false,
    pay_requirement: "",
  });
};

// Function to remove a location
const removeLocation = async (index: number) => {
  const location = locations.value[index];

  // If the location has an id, delete it from the database
  if (location.id && props.data.id) {
    try {
      const { error } = await supabase
        .from("onboarding_locations")
        .delete()
        .eq("id", Number(location.id))
        .eq("onboarding_id", Number(props.data.id));

      if (error) throw error;

      locations.value.splice(index, 1);
      toast.add({
        title: "Success",
        description: "Locations removed successfully",
        color: "green",
      });
    } catch (error) {
      console.error("Error deleting location:", error);
      return;
    }
  } else {
    // Remove the location from the array
    locations.value.splice(index, 1);
  }
};

// Function to save locations to Supabase
const saveLocations = async () => {
  try {
    // Insert or update each location in the `onboarding_locations` table
    const promises = locations.value.map(async (location) => {
      if (location.id) {
        // Update existing location
        const payload: LocationPayload = {
          location: location.location,
          work_authorized: location.work_authorized,
          pay_requirement: location.pay_requirement,
        };
        const { data, error } = await supabase
          .from("onboarding_locations")
          .update(payload)
          .eq("id", Number(location.id)); // Only use `id` to match the row

        if (error) throw error;
        return data;
      } else {
        // Insert new location
        const { data, error } = await supabase
          .from("onboarding_locations")
          .insert({
            onboarding_id: props.data.id,
            location: location.location,
            work_authorized: location.work_authorized,
            pay_requirement: location.pay_requirement,
          } as any);

        if (error) throw error;
        return data;
      }
    });

    await Promise.all(promises);
    emit("update:agentTab", 3);
    toast.add({
      title: "Success",
      description: "Locations saved successfully",
      color: "green",
    });
  } catch (error) {
    console.error("Error saving locations:", error);
  }
};

const handleBackButtonClick = () => {
  emit("update:agentTab", 1);
};

watch(
  () => props.data,
  (newData) => {
    if (newData) {
      fetchLocations();
    }
  },
  { deep: true }
);
// Fetch locations when the component is mounted
onMounted(() => {
  fetchLocations();
});
</script>
