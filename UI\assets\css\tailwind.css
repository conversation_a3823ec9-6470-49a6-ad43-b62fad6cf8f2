@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  /* in order to disable 'dark-mode' browser theme settings */
  height: 100%; /* Ensure the body takes the full height of the viewport */
  margin: 0; /* Remove default margin */
  background-color: white;
  color: rgb(51 65 85); /* text-slate-700 */
  /* font-family: "Lucida Sans", "Lucida Sans Regular", "Lucida Grande",
    "Lucida Sans Unicode", Geneva, Verdana, sans-serif; */

  font-family: "Josefin Sans", sans-serif;
}
input {
  background-color: white;
}

.button-bg-gradient {
  @apply bg-gradient-to-r from-red-500 to-orange-400 hover:bg-gradient-to-r hover:from-orange-400 hover:to-red-500;
}

.text-gradient {
  @apply bg-gradient-to-r from-red-500 to-orange-400 bg-clip-text text-transparent;
}

.vc-highlight-content-solid {
  @apply bg-orange-400;
}

.ql-snow .ql-tooltip {
  @apply !left-0 !top-[161px];
}
