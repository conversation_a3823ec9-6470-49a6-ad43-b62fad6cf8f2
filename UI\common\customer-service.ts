import type { SupabaseClient, User } from "@supabase/supabase-js";
import type { Database } from "../lib/applysquad-common/database.types";
import { NYLAS_CUSTOMER_STATUS, type CustomerJob, CustomerJobStatus, JobStatus, JobType, type Customer, type CustomerContact, CustomerContactStatus, type Plan, enumFromKey, PlanStatus, PRODUCT_CODE, CustomerOrientationStatus, toBoolean, DocumentType, type Document, toJobDocument, type JobDocument, toDocument } from './common';

export async function getCustomerDocuments(
    customerId: string, 
    supabase: SupabaseClient<Database>): Promise<Document[]> {

  const { data, error } = await supabase
    .from("documents")
    .select("*")
    .eq("customer_id", customerId)
    .eq("active", true)
    .in("document_type", ["CV", "COVER"])
    .order("created_at", { ascending: false });

  if (error) {
    throw error;
  }

  return data.map((doc) => ({
    id: doc.id,
    bucket: doc.bucket,
    path: doc.path,
    name: doc.file_name,
    customerId: doc.customer_id,
    type: enumFromKey(DocumentType, doc.document_type),
    createdAt: doc.created_at,
  }));
}

export async function hasCompletedOnboarding(
    isCustomer: boolean, 
    user: User|null, 
    supabase: SupabaseClient<Database>): Promise<boolean> {

  if (isCustomer && user != null) {
    console.log("isCust", isCustomer);
    try {
      const { data, error } = await supabase
        .from("customer_onboarding")
        .select("count")
        .eq("customer_id", user.id)
        .single();

      if (error) {
        console.error(error);
        return false;
      }
      console.log("customer onboarding data", data);
      return data.count > 0;
    } catch (error) {
      console.error("Error checking onboarding status:", error);
      return false;
    }
  } else {
    return false;
  }
}


export async function fetchActivePlans(
  customerId:string,
  supabase: SupabaseClient<Database>): Promise<Plan[]> {
  
  const planKey = customerId + ":plans";
  
  const plansCached: Plan[]|undefined = customerPlanCache.get(customerId);
  if (plansCached) {
    return plansCached;
  }
  const { data, error } = await supabase
      .from("plans")
      .select("*, product:products(*)")
      .eq("customer_id", customerId)
      .eq("status", "OPEN")
      .order("created_at");
  if (error) {
    throw error;
  }
  const plans: Plan[] = data.map((plan) => ({
    id: plan.id,
    customerId: customerId,
    product: {
      productCode: enumFromKey(PRODUCT_CODE, plan.product.product_code),
      label: plan.product.label,
      work: plan.product.work_promised
    },
    status: enumFromKey(PlanStatus, plan.status),
    workPromised: plan.work_promised
  }));
  customerPlanCache.set(planKey, plans);
  return plans;
}
/**
 * simple cache for now. A lib with TTL would be nice improvement.
 */
const customerPlanCache: Map<string, Plan[]> = new Map();



export async function fetchNetworkingPlan(
    customerId: string,
    supabase: SupabaseClient<Database>): Promise<Plan|undefined> {
  console.log("searching for network products for ", customerId)
  const plans = await fetchActivePlans(customerId, supabase);
  const networkingFound = plans.find((d) => d.product.productCode.startsWith("NETWORK"))
  return networkingFound;
}


export async function fetchJobPlan(
    customerId: string,
    supabase: SupabaseClient<Database>): Promise<Plan|undefined> {
  const plans = await fetchActivePlans(customerId, supabase);
  const jobPlans = plans.find((d) => d.product.productCode.startsWith("APPS"));
  return jobPlans;
}

export async function hasCustomerSetupEmailAccess(
  customerId: string,
  supabase: SupabaseClient<Database>): Promise<boolean> {

  try {
    const { data, error } = await supabase
      .from("nylas_customers")
      .select("count")
      .eq("customer_id", customerId)
      .eq("status", NYLAS_CUSTOMER_STATUS.READY)
      .single();

    console.log("has email access setup? ", data)

    if (error) throw error;

    return data.count > 0;
  } catch (error) {
    console.error("Error checking email access status:", error);
    return false;
  }
}

export async function fetchCustomer(
  customerId: string,
  supabase: SupabaseClient<Database>): Promise<Customer> {

  try {
    const { data, error } = await supabase
      .from("customers")
      .select("*")
      .eq("id", customerId)
      .single();

    if (error) throw error;

    return {
      customerId: data.id,
      name: data.name,
      email: data.email,
      whatsappGroupUrl: data.whatsapp_group_url,
      linkedinUrl: data.linkedin_url,
      whatsappNumber: data.whatsapp_number,
      affiliateId: data.affiliate_id,
      agentNotes: data.agent_notes,
      orientationStatus: enumFromKey(CustomerOrientationStatus, data.orientation_status)
    };

  } catch (error) {
    console.error("Error fetching customer:", error);
    throw error;
  }
}

export async function fetchCustomerJobs(
  customerId: string,
  customerJobStatus: CustomerJobStatus[],
  supabase: SupabaseClient<Database>): Promise<CustomerJob[]> {

  const query = supabase
    .from("customer_jobs")
    .select("*, job:jobs(*)")
    .eq("customer_id", customerId)
    .order("created_at", { ascending: false });
  if (customerJobStatus.length > 0) {
    query.in("status", customerJobStatus);
  }

  const { data: jobData, error: jobError } = await query;

  if (jobError) {
    throw jobError;
  }

  const jobs = jobData.map((customerJob) => (toCustomerJob(customerJob, customerJob.job)));

  return jobs;
}

export function toCustomerJob(
  customerJobData: Database['public']['Tables']['customer_jobs']['Row'],
  jobData: Database['public']['Tables']['jobs']['Row']
): CustomerJob {
  const customerJob = { 
      id: customerJobData.id,
      jobId: customerJobData.job_id,
      customerId: customerJobData.customer_id,
      status: CustomerJobStatus[customerJobData.status],
      jobStatus: JobStatus[jobData.job_status],
      url: jobData.url,
      description: jobData.description,
      summary: jobData.summary,
      title: jobData.title,
      employer: jobData.employer,
      jobType: JobType[jobData.job_type],
      location: jobData.location,
      pay: formatPay(jobData),
      languages: jobData.languages,
      visaRequired: jobData.visa_required,
      accountRequired: toBoolean(jobData.account_required),
      customerNotes: customerJobData.customer_notes,
      agentNotes: customerJobData.agent_notes,
      customerApply: jobData.customer_apply,
      matchProbability: customerJobData.match_probability,
      positiveMatchReasons: customerJobData.positive_match_reasons || [],
      negativeMatchReasons: customerJobData.negative_match_reasons || [],
      oldGeneratedCover: customerJobData.cover_letter
  };
  return customerJob;
}

/**
 * pass the results of .select("*, contact:contacts!inner(*)")
 */
export function toCustomerContact(
  customerContactData: Database['public']['Tables']['customer_contacts']['Row'],
  contactData: Database['public']['Tables']['contacts']['Row']
): CustomerContact {
  const customerContact = { 
    id: customerContactData.id,
    contactId: contactData.id,
    customerId: customerContactData.customer_id,
    contactStatus: CustomerContactStatus[customerContactData.status],
    name: contactData.name!,
    company: contactData!.company,
    title: contactData!.title,
    phone: contactData!.phone,
    email: contactData.email,
    linkedinUrl: contactData.linkedin_url,
    description: contactData.description,
    message: customerContactData.message,
    customerNotes: customerContactData.customer_notes,
    agentNotes: customerContactData.agent_notes,
  };
  return customerContact;
}

export async function fetchCustomerContacts(
  customerId: string,
  supabase: SupabaseClient<Database>): Promise<CustomerContact[]> {

    const { data: contactData, error: contactError } = await supabase
    .from("customer_contacts")
    .select("*, contact:contacts!inner(*)")
    .eq("customer_id", customerId)
    .order("created_at", { ascending: false });

  if (contactError) {
    throw contactError;
  }

  const contacts = contactData.map((data) => (
    toCustomerContact(data, data.contact!)
  ));

  return contacts;
}

export async function fetchCustomerDocuments(
    customerId: string, 
    supabase: SupabaseClient<Database>): Promise<Document[]> {

  const { data, error } = await supabase
    .from("documents")
    .select("*")
    .eq("customer_id", customerId)
    .eq("active", true)
    .in("document_type", ["CV", "COVER"])
    .order("created_at", { ascending: false });

  if (error) {
    throw error;
  }

  const docs = data.map((doc) => toDocument(doc));
  return docs;
}

export async function fetchJobDocumentsForCustomer(
  customerId: string,
  supabase: SupabaseClient<Database>
): Promise<JobDocument[]> {

  const { data: documentData, error: documentError } = await supabase
    .from("job_documents")
    .select("*, document:documents!inner(*)")
    .eq("customer_id", customerId)
    .eq("document.active", true);

  if (documentError) {
    throw documentError;
  }
  return documentData.map((jobDoc) => toJobDocument(jobDoc));
}

function formatPay(job: Database['public']['Tables']['jobs']['Row']) {
  if (!job.pay_amount) {
    return "";
  } else {
    nullToEmpty(job!.pay_amount) +
      " " +
      nullToEmpty(job!.pay_currency) +
      "/" +
      nullToEmpty(job!.pay_frequency)
  }
}

function nullToEmpty(d: any): string {
  if (!d || d == null || d == undefined) {
    return "";
  } else {
    return d;
  }
}