{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@ai-sdk/anthropic": "^1.1.15", "@ai-sdk/mistral": "^1.1.15", "@ai-sdk/openai": "^1.2.1", "@intlify/core-base": "^11.1.3", "@intlify/shared": "^11.1.3", "@nuxt/eslint": "^0.7.4", "@nuxt/ui": "^2.20.0", "@nuxtjs/dotenv": "^1.4.2", "@nuxtjs/i18n": "^9.5.2", "@nuxtjs/supabase": "^1.4.4", "@nuxtjs/tailwindcss": "^6.12.2", "@supabase/supabase-js": "^2.47.7", "@types/papaparse": "^5.3.15", "@vuesimple/vs-datepicker": "^3.0.7", "@vueup/vue-quill": "^1.2.0", "adler-32": "^1.3.1", "ai": "^4.1.54", "date-fns": "^2.30.0", "eslint": "^9.17.0", "jspdf": "^3.0.0", "nuxt": "^3.15.2", "nuxt-security": "^2.2.0", "nylas": "^7.7.4", "papaparse": "^5.5.2", "pdf-ts": "^0.0.2", "stopwatch-node": "^1.1.0", "uuid": "^11.0.4", "v-calendar": "^3.1.2", "vue": "^3.5.13", "vue-i18n": "^11.1.3", "vue-router": "^4.5.0", "zod": "^3.24.1"}, "devDependencies": {"@iconify-json/la": "^1.2.1", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "tailwindcss": "^3.4.16"}}