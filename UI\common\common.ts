import type { SupabaseClient, User } from "@supabase/supabase-js"
import type { Database } from "~/lib/applysquad-common/database.types"
import currenciesData from './currencies.json'
import countriesData from './countries.json'

// Export countries as objects with name and alpha3 code
export const countries = countriesData.map(country => ({
  label: country.name,
  value: country.alpha3
}));

export function enumFromKey<T>(enumType: T, key: keyof T): T[keyof T] {
  return enumType[key];
}

export function toBoolean(value: boolean | undefined | null): boolean {
  if (value == undefined || value == null) {
    return false;
  } else {
    return value;
  }
}

/**
 * To simplify code from complex checks - meant to be inclusive of all the
 * ways that javascript objects can be "null" (not exists, be defined, etc).
 * Javascript & Typescript have differing ways of determining this, so this
 * method allows for checking all the things, thus simplifying code.
 * @param value any object
 * @returns whether the object exists, is not null, and not undefined.
 */
export function notNull(value: any): boolean {
  return value && (value !== null) && (value !== undefined);
}

export function isNull(value: any): boolean {
  return !notNull(value);
}

export interface Agent {
  agentId: string,
  agentName: string,
  agentEmail: string,
  whatsappNumber: string,
  gender: string,
  dateOfBirth: string
}

export interface Customer {
  customerId: string,
  name: string,
  email: string,
  whatsappGroupUrl: string | undefined | null,
  whatsappNumber: string | undefined | null,
  linkedinUrl: string | undefined | null,
  affiliateId: string | undefined | null,
  agentNotes: string | undefined | null,
  orientationStatus: CustomerOrientationStatus
}

export interface CustomerLite {
  customerId: string,
  name: string,
  email: string,
  whatsappGroupUrl: string | undefined | null,
  autoSearch: boolean | null
}

export interface CustomerJobDocuments {
  cv: JobDocument | undefined | null;
  coverLetter: JobDocument | undefined | null;
  screenshots: JobDocument[] | undefined | null;
}

export interface CustomerJob {
  id: number,
  jobId: string,
  customerId: string,
  description: string,
  summary: string | undefined | null,
  url: string,
  employer: string,
  title: string,
  jobType: JobType,
  location: string | undefined,
  languages: string | undefined | null,
  pay: string | undefined,
  visaRequired: string | undefined | null,
  accountRequired: boolean,
  jobStatus: JobStatus,
  status: CustomerJobStatus,
  customerNotes: string | undefined | null,
  agentNotes: string | undefined | null,
  customerApply: boolean,
  matchProbability: number|null,
  positiveMatchReasons: string[],
  negativeMatchReasons: string[],
  /**
   * @deprecated - use most recent from table "cover_letters" instead
   * original place to store cover letter, will be removed in future
   */
  oldGeneratedCover: string | undefined | null
}

export interface CustomerContact {
  id: number,
  contactId: string,
  customerId: string,
  name: string,
  company: string | undefined | null,
  title: string | undefined | null,
  phone: string | undefined | null,
  email: string | undefined | null,
  linkedinUrl: string | undefined | null,
  description: string | undefined | null,
  message: string | undefined | null,
  contactStatus: CustomerContactStatus,
  customerNotes: string | undefined | null,
  agentNotes: string | undefined | null,
}

export interface Document {
  id: string,
  bucket: string,
  path: string,
  name: string,
  customerId: string,
  type: DocumentType,
  createdAt: string
}

export interface JobDocument {
  id: number,
  customerJobId: number,
  customerId: string,
  type: DocumentType,
  document: Document,
}

export enum PRODUCT_CODE {
  APPS_20 = "APPS_20",
  APPS_50 = "APPS_50",
  APPS_100 = "APPS_100",
  APPS_500 = "APPS_500",
  NETWORK_80 = "NETWORK_80",
  NETWORK_20 = "NETWORK_20",
  APPS_TRIAL_5 = "APPS_TRIAL_5",
  OTHER = "OTHER"
}

export interface Product {
  productCode: PRODUCT_CODE,
  label: string,
  work: number
}

export enum PlanStatus {
  OPEN = "OPEN",
  COMPLETE = "COMPLETE"
}

/**
 * Represents the chunk of work purchased by the customer,
 * and a contract from us to fulfill it.
 */
export interface Plan {
  id: number,
  customerId: string,
  product: Product,
  status: PlanStatus,
  workPromised: number
}

export enum JobType {
  FULLTIME = "FULLTIME",
  PARTTIME = "PARTTIME",
  INTERNSHIP = "INTERNSHIP",
  CONTRACT = "CONTRACT",
  TEMPORARY = "TEMPORARY",
}

export enum JobStatus {
  OPEN = "OPEN",
  CLOSED = "CLOSED"
}

export enum CustomerJobStatus {
  // a job was newly matched to a customer
  NEW = "NEW",
  // the customer wants us to apply to this job
  APPROVED = "APPROVED",
  // the customer does not want us to apply to this job
  DECLINED = "DECLINED",
  // we have applied to the job for the customer
  APPLIED = "APPLIED",
  // the customer has applied to the job themselves
  CUSTOMER_APPLIED = "CUSTOMER_APPLIED",
  // the customer is satisfied with the application
  SATISFIED = "SATISFIED",
  // the customer was not satisfied with the applicaiton
  DISSATISFIED = "DISSATISFIED",
  // the agent was not satisfied with the applicaiton
  DELETED = "DELETED",
  // the job posting is no longer valid
  EXPIRED = "EXPIRED"
}

export enum CustomerOrientationStatus {
  NEW = "NEW",
  COMPLETE = "COMPLETE"
}

export enum JobNextStep {
  CUSTOMER_APPROVAL = "CUSTOMER_APPROVAL",
  CUSTOMER_APPLY = "CUSTOMER_APPLY",
  AGENT_APPLY = "AGENT_APPLY",
  CUSTOMER_FEEDBACK = "CUSTOMER_FEEDBACK",
  COMPLETE = "COMPLETE"
}

export enum CustomerContactStatus {
  // a contact was newly added to a customer
  NEW = "NEW",
  // the customer wants us to communicate with this contact
  APPROVED = "APPROVED",
  // the customer does not want us to communicate with this contact
  DECLINED = "DECLINED",
  // we have communicated with the contact for the customer
  CONTACTED = "CONTACTED",
}

export enum DocumentType {
  CV = "CV",
  COVER = "COVER",
  SCREENSHOT = "SCREENSHOT"
}

export enum NYLAS_CUSTOMER_STATUS {
  STARTED = "STARTED",
  AUTHENTICATED = "AUTHENTICATED",
  READY = "READY",
  REVOKED = "REVOKED"
}

export const languages = ['English', 'Spanish', 'French', 'German', 'Italian', 'Japanese', 'Chinese', 'Russian', 'Arabic', 'Portuguese',]
export const workSpaces = ["On-site", "Remote", "Hybrid"]
export const workSpace = ["ONSITE", "REMOTE", "HYBRID"]
export const genders = ["MALE", "FEMALE", "OTHER", "PREFER NOT TO SAY"]
export const seniorityLevels = ["0-3", "4-6", "7-10", "11-15", "15+"];
export const companyTypes = ["Startup", "Small/Medium Business", "Established/Corporate"]
export const regions = ["US", "UK", "Europe", "Middle East", "Africa", "South Asia",]
export const functionalPreferences = ["Marketing", "Sales", "Finance", "Operations", "Internal Strategy", "Strategy Consulting", "Investment and Asset Management", "R&D", "Manufacturing"]
export const payFrequencies = ['Hourly', 'Daily', 'Weekly', 'Monthly', 'Yearly']
export const jobType = ['FULLTIME', 'PARTTIME', 'CONTRACT', 'INTERNSHIP', 'TEMPORARY']
export const industries = ['Accommodation Services', 'Administrative and Support Services', 'ConstructionConsumer Services', 'Education', 'Entertainment Providers', 'Farming, Ranching, Forestry', 'Financial Services', 'Government Administration', 'Holding Companies', 'Hospitals and Health Care', 'ManufacturingOil, Gas, and Mining', 'Professional Services', 'Real Estate and Equipment Rental Services', 'Retail', 'Technology, Information and Media', 'Transportation, Logistics, Supply Chain and Storage', 'Utilities', 'Wholesale']
export const jobTypes = [
  { label: "full time", value: JobType.FULLTIME },
  { label: "part time", value: JobType.PARTTIME },
  { label: "contract", value: JobType.CONTRACT },
  { label: "temporary", value: JobType.TEMPORARY },
  { label: "internship", value: JobType.INTERNSHIP }
]
export const currencyOptions = currenciesData.map((currency) => ({ label: `${currency.symbol_native} ${currency.code}`, value: currency.code }))


export function formatUsdPenniesToDollars(pennies: number): string {
  return `$${pennies / 100}.00`
}

export function jobNextStep(job: CustomerJob): JobNextStep {
  if (CustomerJobStatus.NEW == job.status && job.customerApply) {
    return JobNextStep.CUSTOMER_APPLY;
  } else if (CustomerJobStatus.NEW == job.status) {
    return JobNextStep.CUSTOMER_APPROVAL;
  } else if (CustomerJobStatus.APPROVED == job.status) {
    return JobNextStep.AGENT_APPLY;
  } else if (CustomerJobStatus.APPLIED == job.status) {
    return JobNextStep.CUSTOMER_FEEDBACK;
  } else {
    return JobNextStep.COMPLETE;
  }
}

/**
 * @param jobDoc - from database, assumes the join from job_documents -> documents
 *                 like this: select("*, document:documents!inner(*)")
 */
export function toJobDocument(jobDoc: any): JobDocument {
  return {
    id: jobDoc.id,
    customerJobId: jobDoc.customer_job_id,
    customerId: jobDoc.customer_id,
    type: enumFromKey(DocumentType, jobDoc.document_type),
    document: toDocument(jobDoc.document),
  };
}

/**
 * @param documentData - from database
 * @returns 
 */
export function toDocument(documentData: Database['public']['Tables']['documents']['Row']): Document {
  return {
    id: documentData.id,
    bucket: documentData.bucket,
    path: documentData.path,
    name: documentData.file_name,
    customerId: documentData.customer_id,
    type: enumFromKey(DocumentType, documentData.document_type),
    createdAt: documentData.created_at,
  };
}

export function emptyDocument(docType: DocumentType): Document {
  return {
    id: "",
    name: "None",
    bucket: "",
    path: "",
    customerId: "",
    type: docType,
    createdAt: "",
  }
};

export async function isAdmin(user: User | null | undefined, supabase: SupabaseClient<Database>) {
  console.log('checking admin access for ', user)
  if (!user || user == null || user == undefined) {
    return false;
  } else {
    const { data, error } = await supabase
      .from('admins')
      .select('*')
      .eq('user_id', user.id)
    if (error) {
      console.error('Failed to check admin status:', error)
      return false
    }
    console.log(`User ${user.email} is an admin: ${data.length > 0}`)
    return data.length > 0
  }
}

export function isAgent(user: User | null | undefined): boolean {
  if (!user || user == null || user == undefined) {
    return false;
  } else {
    const role = user.user_metadata.role
    console.log(`User ${user.email} is an agent: ${role === 'agent'}`)
    return role === 'agent'
  }
}

export function isCustomer(user: User | null | undefined): boolean {
  if (!user || user == null || user == undefined) {
    return false;
  } else {
    const role = user.user_metadata.role
    console.log(`User ${user.email} is an customer: ${role === 'customer'}`)
    return role === 'customer'
  }
}

