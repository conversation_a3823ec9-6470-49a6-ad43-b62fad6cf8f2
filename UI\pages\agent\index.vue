<template>
  <UCard class="shadow-2xl shadow-gray-700 m-10 max-w bg-white h-max">
    <template #header>
      <p class="text-center">Agent Dashboard</p>
    </template>

    <p v-if="errorMessage" class="text-red-500">{{ errorMessage }}</p>

    <UTabs :items="tabs">
      <template #my-customers>
        <UCard class="tabContent border-1 w-max items-center">
          <div
            v-if="isLoading"
            class="h-full flex items-center justify-center pt-10"
          >
            <BaseSpinner :show-loader="isLoading" size="md" />
          </div>
          <UContainer v-else-if="customers.length == 0">
            You have no customers. Please check back soon!
          </UContainer>
          <UTabs
            v-else
            :items="customers"
            orientation="vertical"
            :ui="{
              wrapper: 'flex items-start gap-4',
              list: { base: 'basis-1/5 left-0 top-0', width: 'min-w-48' },
            }"
            @change="onChangeCustomers"
          >
            <template #item="{ item }">
              <UCard>
                <template #header>
                  <div class="flex flex-wrap justify-between container">
                    <div>
                      <div class="flex items-center gap-6">
                        <p class="py-2">
                          {{ item.customer.name }}
                        </p>
                        <ULink
                          title="Customer Notes"
                          @click="toggleAgentModal(item.customer.customerId)"
                        >
                          <UIcon size="lg" name="la:clipboard-list" />
                        </ULink>

                        <UModal
                          v-model="agentNotesModal[item.customer.customerId]"
                          prevent-close
                        >
                          <UCard
                            :ui="{
                              ring: '',
                              divide:
                                'divide-y divide-gray-100 dark:divide-gray-800',
                            }"
                          >
                            <template #header>
                              <div class="flex items-center justify-between">
                                <h3
                                  class="text-base leading-6 text-gray-900 dark:text-white"
                                >
                                  Add Customer Notes
                                </h3>
                                <UIcon
                                  name="la-times-circle"
                                  class="size-7 cursor-pointer text-orange-500"
                                  @click="
                                    closeAgentModal(item.customer.customerId)
                                  "
                                />
                              </div>
                            </template>

                            <div class="w-full sm:pr-2">
                              <UTextarea
                                v-model="agentNotes[item.customer.customerId]"
                                autoresize
                                padded
                                color="gray"
                                class="text-slate-700"
                                :disabled="!isCustomer"
                                :placeholder="'Customer Notes ...'"
                              />
                            </div>

                            <div class="mt-5 flex justify-end">
                              <UButton
                                label="Save"
                                class="w-fit"
                                :disabled="
                                  !agentNotes[item.customer.customerId]
                                "
                                @click="
                                  saveAgentNotes(item.customer.customerId)
                                "
                              />
                            </div>
                          </UCard>
                        </UModal>
                      </div>
                      <div>
                        <UButton
                          size="lg"
                          color="orange"
                          variant="outline"
                          icon="la:file-alt"
                          @click="toggleModal(item.customer.customerId)"
                          >Onboarding Survey
                        </UButton>

                        <UModal
                          v-model="modalStates[item.customer.customerId]"
                          :ui="{ width: 'w-full sm:max-w-7xl' }"
                        >
                          <div
                            class="flex justify-end p-4 w-fit absolute right-0"
                          >
                            <UButton
                              color="gray"
                              variant="ghost"
                              icon="i-heroicons-x-mark-20-solid"
                              class="-my-1"
                              @click="closeModal(item.customer.customerId)"
                            />
                          </div>
                          <div class="p-4">
                            <CustomerOnboarding
                              :customer="item.customer"
                              :is-customer="false"
                              :document-deleted="false"
                              @document-deleted="() => {}"
                              @get-customer-documents="() => {}"
                            />
                          </div>
                        </UModal>
                      </div>
                      <div class="pt-2">
                        <UButton
                          :to="`mailto:${item.customer.email}`"
                          target="_blank"
                          color="red"
                          size="lg"
                          icon="la:envelope"
                          variant="outline"
                          :label="item.customer.email"
                        />
                      </div>
                      <div v-if="item.customer.whatsappGroupUrl" class="pt-2">
                        <UButton
                          :to="item.customer.whatsappGroupUrl"
                          color="green"
                          variant="outline"
                          target="_blank"
                          icon="la:whatsapp"
                          size="lg"
                        >
                          Whatsapp Group
                        </UButton>
                      </div>
                      <div v-if="item.customer.linkedinUrl" class="pt-2">
                        <UButton
                          :to="item.customer.linkedinUrl"
                          color="blue"
                          variant="outline"
                          target="_blank"
                          icon="la:linkedin"
                          size="lg"
                        >
                          Linkedin Profile
                        </UButton>
                      </div>
                      <div>
                        <UButton
                          v-if="emailAccess[item.customer.customerId]"
                          :to="`/agent/email?customerId=${item.customer.customerId}`"
                          target="_blank"
                          icon="i-heroicons-envelope"
                          class="button-bg-gradient"
                          size="lg"
                        >
                          View Emails
                        </UButton>
                      </div>
                    </div>

                    <div class="justify-self-end">
                      <JobPlanSummaryList
                        :is-customer="false"
                        :customer="item.customer"
                        :customer-jobs="customerJobs[item.customer.customerId]"
                      />
                    </div>
                  </div>
                </template>

                <UTabs
                  v-model="agentTab"
                  :items="getTabsWithState(item.hasNetworking)"
                  class="w-full"
                >
                  <template #jobs>
                    <div
                      class="py-2 flex items-center space-x-4 justify-between"
                    >
                      

                      <UButton
                        size="lg"
                        class="button-bg-gradient"
                        @click="openAddJobModal(item.customer, agent!)"
                      >
                        <UIcon name="la:plus-circle" class="size-5" />
                        Add a Job
                      </UButton>
                    </div>

                    <div
                      v-if="
                        isJobsLoading && !customerJobs[item.customer.customerId]
                      "
                      class="h-full flex items-center justify-center pt-10"
                    >
                      <BaseSpinner :show-loader="isJobsLoading" size="md" />
                    </div>
                    <JobTracker
                      v-else-if="!isJobsLoading"
                      :customer="item.customer"
                      :documents="customerDocuments[item.customer.customerId]"
                      :jobs="customerJobs[item.customer.customerId]"
                      :is-customer="false"
                       />
                  </template>

                  <template #networking>
                    <div class="py-2 flex flex-nowrap gap-4 justify-self-end">
                      <UButton
                        size="lg"
                        class="bg-blue-500 hover:bg-blue-600"
                        @click="openAddContactModal(item.customer, agent!)"
                      >
                        <UIcon name="la:plus-circle" class="h-5 w-5" />
                        Add a Contact
                      </UButton>

                      <ContactUploader
                        :agent-id="agent!.agentId"
                        :customer-id="item.customer.customerId"
                        :refresh-contacts="
                          () => loadContacts(item.customer.customerId)
                        "
                      />
                    </div>
                    <ContactTracker
                      v-if="item.hasNetworking"
                      :customer="item.customer"
                      :is-customer="false"
                      :agent="agent"
                      :customer-contacts="
                        customerContacts[item.customer.customerId]
                      "
                      :refresh-contacts="
                        () => loadContacts(item.customer.customerId)
                      "
                    />
                    <div v-else>No networking found</div>
                  </template>
                </UTabs>
              </UCard>
            </template>
          </UTabs>
        </UCard>
      </template>

      <template #profile>
        <UCard class="border-1 rounded-md">
          <table class="border-0 mx-auto w-full block">
            <tbody>
              <tr>
                <td class="p-2 w-50">name:</td>
                <td class="p-2">{{ agent?.agentName }}</td>
              </tr>
              <tr>
                <td class="p-2 w-50">email:</td>
                <td>{{ agent?.agentEmail }}</td>
              </tr>
              <tr>
                <td class="p-2">whatsapp #:</td>
                <td>{{ agent?.whatsappNumber }}</td>
              </tr>
              <tr>
                <td class="p-2">birthday:</td>
                <td>{{ agent?.dateOfBirth }}</td>
              </tr>
            </tbody>
          </table>
        </UCard>
      </template>
    </UTabs>
  </UCard>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import {
  CustomerOrientationStatus,
  enumFromKey,
  isCustomer,
  type Agent,
  type Customer,
  type CustomerContact,
  type CustomerJob,
  type Document,
} from "~/common/common";
import { JobForm, ContactForm, UIcon } from "#components";
import type { Database } from "../../lib/applysquad-common/database.types";
import ContactTracker from "~/components/ContactTracker.vue";
import {
  fetchNetworkingPlan,
  fetchCustomerContacts,
  fetchCustomerJobs,
  hasCustomerSetupEmailAccess,
  fetchCustomerDocuments,
} from "~/common/customer-service";

definePageMeta({
  middleware: ["auth"],
});

interface CustomerItem {
  label: string;
  slot: string;
  hasNetworking: boolean;
  customer: Customer;
}

// Define reactive state variables
const modal = useModal();
const errorMessage = ref("");
const isOpen = ref(false);
const agentTab = ref(0);
const agent = ref<Agent>();
const customers = ref<CustomerItem[]>([]);
const supabase = useSupabaseClient<Database>();
const user = useSupabaseUser();
const isLoading = ref(true);
const isJobsLoading = ref(false);
// customer jobs, by customerId
const customerJobs = ref<Record<string, CustomerJob[]>>({});
// customer docs, by customerId
const customerDocuments = ref<Record<string, Document[]>>({});
// customer contacts, by customerId
const customerContacts = ref<Record<string, CustomerContact[]>>({});
const modalStates = ref<Record<string, boolean>>({});
const agentNotesModal = ref<Record<string, boolean>>({});
const emailAccess = ref<Record<string, boolean>>({});
const networkingAccess = ref<Record<string, boolean>>({});
const agentNotes = ref<Record<string, string>>({});
const toast = useToast();
const jobsFetched = ref<Record<string, boolean>>({}); // Track checked customers

const saveAgentNotes = async (customerId: string) => {
  const { error: customerError } = await supabase
    .from("customers")
    .update({ agent_notes: agentNotes.value[customerId] })
    .eq("id", customerId);

  if (customerError) throw customerError;
  toast.add({
    title: "Success",
    description: "Customer Notes Added Successfully",
    color: "green",
  });

  closeAgentModal(customerId);
};

const baseAgentTabs = [
  { slot: "jobs", label: "Jobs" },
  { slot: "networking", label: "Contacts" },
];

function getTabsWithState(hasNetworking: boolean) {
  return baseAgentTabs.map((tab) => ({
    ...tab,
    disabled: tab.slot === "networking" && !hasNetworking,
  }));
}
const tabs = [
  { slot: "my-customers", label: "My Customers" },
  { slot: "profile", label: "My Profile" },
];

// Agent Profile data
const { data: profile, error: profileError } = await supabase
  .from("agents")
  .select(
    "agentId:id, agentName:name, agentEmail:email, whatsappNumber:whatsapp_number, gender, dateOfBirth:date_of_birth"
  )
  .eq("email", user.value!.email!);
if (profileError) {
  errorMessage.value = "error retrieving agent profile " + profileError.message;
  throw profileError;
}
agent.value = profile[0];

// Fetch customer data for agent
async function initializeCustomers(agent: Agent) {
  const { data: agentCustomers, error: customersError } = await supabase
    .from("agent_customers")
    .select("*, customer:customers(*)")
    .eq("agent_id", agent.agentId)
    .eq("active", true);

  if (customersError) {
    console.error("error retrieving agent's customers ", customersError);
    errorMessage.value =
      "error retrieving agent's customers " + customersError.message;
    throw customersError;
  }

  if (agentCustomers.length > 0) {
    const customerSections = agentCustomers.map(async (a, index) => {
      const customerId = a.customer!.id;
      const hasNetworking = (await fetchNetworkingPlan(
        a.customer!.id,
        supabase
      )) != undefined;
      networkingAccess.value[customerId] = hasNetworking;

      // Load jobs and contacts only for the first customer
      if (index === 0) {
        await loadCustomerData(customerId);
      } else {
        // For other customers, initialize jobs and contacts as empty arrays
        customerJobs.value[customerId] = [];
        customerContacts.value[customerId] = [];
      }

      agentNotes.value[customerId] = a.customer!.agent_notes || "";

      return {
        label: a.customer!.name,
        slot: "item",
        hasNetworking: hasNetworking,
        customer: {
          customerId: customerId,
          email: a.customer!.email,
          name: a.customer!.name,
          whatsappGroupUrl: a.customer!.whatsapp_group_url,
          whatsappNumber: a.customer!.whatsapp_number,
          linkedinUrl: a.customer!.linkedin_url,
          affiliateId: a.customer!.affiliate_id,
          agentNotes: a.customer!.agent_notes,
          orientationStatus: enumFromKey(CustomerOrientationStatus, a.customer!.orientation_status)
        },
      };
    });

    customers.value = await Promise.all(customerSections);    
  }
}

const onChangeCustomers = async (index: number) => {
  isJobsLoading.value = true;
  const selectedCustomer = customers.value[index]; // Get selected customer
  const customerId = selectedCustomer.customer.customerId;

  try {
    // If jobs were already checked before, do not reload
    if (jobsFetched.value[customerId]) {
      console.log("Jobs already fetched, skipping reload.");
    } else {
      await loadCustomerData(customerId)
    }
  } catch (error) {
    console.error("Error loading jobs or contacts:", error);
  } finally {
    isJobsLoading.value = false; // Ensure loading stops even if an error occurs
  }
};

async function loadCustomerData(customerId: string) {
  await loadJobs(customerId);
  await loadDocuments(customerId);
  if (networkingAccess.value[customerId]) {
    await loadContacts(customerId);
  }
}

async function loadJobs(customerId: string) {
  const jobs = await fetchCustomerJobs(customerId, [], supabase);
  customerJobs.value[customerId] = jobs;
  jobsFetched.value[customerId] = true;
}

async function loadContacts(customerId: string) {
  const contacts = await fetchCustomerContacts(customerId, supabase);
  customerContacts.value = {
    ...customerContacts.value,
    [customerId]: contacts,
  };
}

async function loadDocuments(customerId: string) {
  const documents: Document[] = await fetchCustomerDocuments(customerId, supabase);
  customerDocuments.value[customerId] = documents;
}

function openAddJobModal(customer: Customer, agent: Agent) {
  // console.log(
  //   `opening add job modal for ${customer.name} for agent ${agent.agentEmail}`
  // );
  modal.open(JobForm, {
    customer: customer,
    agent: agent,
    success: (customerJob: CustomerJob) => {
      // console.log("job success handler called ");
      modal.close();
      customerJobs.value[customer.customerId].push(customerJob);
      loadJobs(customer.customerId);
    },
  });
}

function openAddContactModal(customer: Customer, agent: Agent) {
  // console.log(
  //   `opening add contact modal for ${customer.name} for agent ${agent.agentEmail}`
  // );
  modal.open(ContactForm, {
    customer: customer,
    agent: agent,
    success: (contact: CustomerContact) => {
      // console.log("contact success handler called ");
      modal.close();
      customerContacts.value[customer.customerId].push(contact);
      loadContacts(customer.customerId);
    },
  });
}

function toggleAgentModal(customerId: string) {
  agentNotesModal.value[customerId] = !agentNotesModal.value[customerId];
}

function closeAgentModal(customerId: string) {
  agentNotesModal.value[customerId] = false;
}

function toggleModal(customerId: string) {
  modalStates.value[customerId] = !modalStates.value[customerId];
}

function closeModal(customerId: string) {
  modalStates.value[customerId] = false;
}

// Initialize modalStates for all customers
customers.value.forEach((customer) => {
  modalStates.value[customer.customer.customerId] = false;
});

onMounted(async () => {
  isLoading.value = true;
  await initializeCustomers(agent.value!);
  isLoading.value = false;
});
</script>

<style scoped>
.tabContent {
  margin-bottom: 10px;
  margin-bottom: 0;
  width: 100%;
}
</style>
