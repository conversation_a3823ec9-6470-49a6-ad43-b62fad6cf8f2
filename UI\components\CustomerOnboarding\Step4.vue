<script setup lang="ts">
import type { Customer, Document } from "~/common/common";
import { useSupabaseClient } from "#imports";
import type { Database } from "~/lib/applysquad-common/database.types";
import { format } from "date-fns";
import {
  languages,
  DocumentType,
  enumFromKey,
  genders,
  countries,
} from "../../common/common";

const props = defineProps<{
  customer: Customer;
  data: {
    id: string;
    current_address: string;
    university: string;
    date_of_birth: string;
    gender: string;
    languages_fluent: string[];
    languages_learning: string[];
    nationality: string;
  };
  isEditing: boolean;
  isCustomer: boolean;
  agentTab: number;
  loadData: () => void;
}>();

const emit = defineEmits(["update:isEditing", "update:agentTab"]);


const supabase = useSupabaseClient<Database>();
const toast = useToast();
const documents = ref<Document[]>([]);
const customerCVs = ref<Document[]>([]);
const customerCoverLetters = ref<Document[]>([]);
const hasOnboarding = defineModel<boolean>("hasOnboarding");

const today = new Date();
const sixteenYearsAgo = new Date();
sixteenYearsAgo.setFullYear(today.getFullYear() - 16);

const disabledDates = ref([
  { start: new Date(sixteenYearsAgo.getTime() + 1), end: null },
]);
// Initialize empty arrays for selected values
const selectedLanguagesFluent = ref<any>([]);
const selectedLanguagesLearning = ref<any>([]);
const languagesRef = ref<string[]>(languages);

const formState = reactive({
  current_address: props.data ? props.data.current_address : "",
  university: props.data ? props.data.university : "",
  date_of_birth: props.data ? props.data.date_of_birth : "",
  gender: props.data ? props.data.gender : "",
  languages_fluent: props.data ? props.data.languages_fluent : ([] as string[]),
  languages_learning: props.data
    ? props.data.languages_learning
    : ([] as string[]),
  nationality: props.data
    ? props.data.nationality
    : ("" as string | { label: string; value: string }),
});

// Add a watcher to update form state when props.data changes
watch(
  () => props.data,
  (newData) => {
    if (newData) {
      formState.current_address = newData.current_address;
      formState.university = newData.university;
      formState.date_of_birth = newData.date_of_birth;
      formState.gender = newData.gender;
      formState.languages_fluent = newData.languages_fluent;
      formState.languages_learning = newData.languages_learning;
      if (newData.nationality) {
        const nationalityOption = countries.find(
          (opt) => opt.value === newData.nationality
        );
        formState.nationality = nationalityOption
          ? { label: nationalityOption.label, value: nationalityOption.value }
          : { label: "", value: "" };
      } else {
        formState.nationality = { label: "", value: "" };
      }

      // Safely handle languages_fluent
      if (Array.isArray(newData.languages_fluent)) {
        selectedLanguagesFluent.value = newData.languages_fluent.map((lang) => {
          const option = languageOptions.value.find((opt) => opt.name === lang);
          return option || { id: languageOptions.value.length + 1, name: lang };
        });
        formState.languages_fluent = newData.languages_fluent;
      } else {
        selectedLanguagesFluent.value = [];
        formState.languages_fluent = [];
      }

      // Safely handle languages_learning
      if (Array.isArray(newData.languages_learning)) {
        selectedLanguagesLearning.value = newData.languages_learning.map(
          (lang) => {
            const option = languageOptions.value.find(
              (opt) => opt.name === lang
            );
            return (
              option || { id: languageOptions.value.length + 1, name: lang }
            );
          }
        );
        formState.languages_learning = newData.languages_learning;
      } else {
        selectedLanguagesLearning.value = [];
        formState.languages_learning = [];
      }
      //   formState.nationality = newData.nationality;
    }
  },
  { deep: true }
);

// Convert languages array to ref with proper type checking
const languageOptions = ref(
  Array.isArray(languagesRef.value)
    ? languagesRef.value.map((lang: string, index: number) => ({
        id: index + 1,
        name: lang,
      }))
    : []
);

const languagesFluent = computed({
  get: () => selectedLanguagesFluent.value,
  set: async (labels) => {
    const promises = labels.map(async (label: any) => {
      if (label.id) {
        // Use Set to ensure unique values
        formState.languages_fluent = Array.from(
          new Set([...formState.languages_fluent, label.name])
        );
        return label;
      }

      const response = {
        id: languageOptions.value.length + 1,
        name: label.name,
      };

      languageOptions.value.push(response);
      // Use Set to ensure unique values
      formState.languages_fluent = Array.from(
        new Set([...formState.languages_fluent, label.name])
      );
      return response;
    });

    selectedLanguagesFluent.value = await Promise.all(promises);
  },
});

const languagesLearning = computed({
  get: () => selectedLanguagesLearning.value,
  set: async (labels) => {
    const promises = labels.map(async (label: any) => {
      if (label.id) {
        // Use Set to ensure unique values
        formState.languages_learning = Array.from(
          new Set([...formState.languages_learning, label.name])
        );
        return label;
      }

      const response = {
        id: languageOptions.value.length + 1,
        name: label.name,
      };

      languageOptions.value.push(response);
      // Use Set to ensure unique values
      formState.languages_learning = Array.from(
        new Set([...formState.languages_learning, label.name])
      );
      return response;
    });

    selectedLanguagesLearning.value = await Promise.all(promises);
  },
});

// Helper function to display array values
function displayArrayValue(arr: any[] | null | undefined): string {
  return Array.isArray(arr) && arr.length > 0
    ? arr.join(", ")
    : "Not specified";
}

function formatDateToUTC(date: any) {
  if (!date) return null;

  // Create a UTC date to avoid timezone shifts
  const utcDate = new Date(
    Date.UTC(
      new Date(date).getFullYear(),
      new Date(date).getMonth(),
      new Date(date).getDate()
    )
  );

  // Convert to YYYY-MM-DD format
  return utcDate.toISOString().split("T")[0];
}

// Handle form submission
async function submitForm() {
  try {
    const onboardingData = {
      customer_id: props.customer.customerId,

      languages_fluent:
        formState.languages_fluent.length > 0
          ? formState.languages_fluent
          : null,
      languages_learning:
        formState.languages_learning.length > 0
          ? formState.languages_learning
          : null,
      current_address: formState.current_address || null,

      gender: formState.gender || null,

      nationality:
        (typeof formState.nationality === "object" && formState.nationality
          ? formState.nationality.value
          : formState.nationality) || null,

      // Send only the date part to database
      date_of_birth: formatDateToUTC(formState.date_of_birth),
      university: formState.university || null,
    };

    let error;
    if (props.data && props.data.id) {
      const { error: updateError } = await supabase
        .from("customer_onboarding")
        .update(onboardingData as object)
        .eq("id", Number(props.data.id))
        .eq("customer_id", props.customer.customerId);
      error = updateError;
    } else {
      const { error: insertError } = await supabase
        .from("customer_onboarding")
        .insert(onboardingData as object);
      error = insertError;
    }
    if (error) {
      console.error("Database operation error:", error);
      throw error;
    }
    await props.loadData();
    const { error: customerError } = await supabase
      .from("customers")
      .update({ onboarding_complete: true })
      .eq("id", props.customer.customerId);
    if (customerError) throw customerError;
    toast.add({
      title: "Success",
      description: "Profile updated successfully",
      color: "green",
    });
    emit("update:agentTab", 2);
    hasOnboarding.value = true
  } catch (error) {
    console.error("Error saving form:", error);
    toast.add({
      title: "Error",
      description: "Failed to update profile",
      color: "red",
    });
  }
}
</script>

<template>
  <div>
    <h2 class="text-base md:text-xl font-bold">
      We need some personal info to fill out job applications
    </h2>
    <!-- Form -->
    <UForm :state="formState" class="space-y-4 pt-4" @submit="submitForm">
      <div class="flex flex-col md:flex-row space-y-4 w-full">
        <div class="flex flex-col gap-4 lg:gap-6 w-full">
          <!-- Nationality -->
          <UFormGroup label="What is your nationality?" class="my-2">
            <template v-if="isEditing">
              <USelectMenu
                v-model="formState.nationality"
                :options="countries"
                option-attribute="label"
                searchable
                placeholder="Select your nationality"
              />
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{
                  formState.nationality
                    ? typeof formState.nationality === "object"
                      ? formState.nationality.label
                      : formState.nationality
                    : "Not specified"
                }}
              </div>
            </template>
          </UFormGroup>

          <!-- Date of Birth -->
          <UFormGroup
            label="What is your date of birth? (DD/MM/YYYY)"
            class="my-2"
          >
            <template v-if="isEditing">
              <UPopover :popper="{ placement: 'bottom-start' }">
                <UButton
                  icon="i-heroicons-calendar-days-20-solid"
                  :label="
                    formState.date_of_birth &&
                    format(new Date(formState.date_of_birth), 'dd/MM/yyyy')
                  "
                  variant="outline"
                  :ui="{
                    base: 'w-full',
                    truncate: 'flex justify-center w-full',
                  }"
                  truncate
                />
                <template #panel="{ close }">
                  <DatePicker
                    v-model="formState.date_of_birth"
                    is-required
                    :disabled-dates="disabledDates"
                    @close="close"
                  />
                </template>
              </UPopover>
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{
                  formState.date_of_birth
                    ? new Date(formState.date_of_birth).toLocaleDateString()
                    : "Not specified"
                }}
              </div>
            </template>
          </UFormGroup>

          <!-- University -->
          <UFormGroup label="What university did you attend?" class="my-2">
            <template v-if="isEditing">
              <UInput
                v-model="formState.university"
                placeholder="Enter your university"
              />
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{ formState.university || "Not specified" }}
              </div>
            </template>
          </UFormGroup>

          <!-- Gender -->
          <UFormGroup
            label="What is your gender?"
            class="my-2"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <template v-if="isEditing">
              <USelectMenu v-model="formState.gender" :options="genders" />
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{ formState.gender || "Not specified" }}
              </div>
            </template>
          </UFormGroup>

          <!-- current address -->
          <UFormGroup
            label="what is your current address? (city, country)"
            class="my-2"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <template v-if="isEditing">
              <UInput
                v-model="formState.current_address"
                placeholder="Enter locations..."
              />
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{ formState.current_address || "Not specified" }}
              </div>
            </template>
          </UFormGroup>

          <!-- languagesFluent -->
          <UFormGroup
            label="What languages do you speak fluently?"
            class="my-2"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <template v-if="isEditing">
              <USelectMenu
                v-model="languagesFluent"
                :options="languageOptions"
                by="id"
                name="languages_fluent"
                option-attribute="name"
                multiple
                searchable
                creatable
                :default-value="selectedLanguagesFluent"
                placeholder="Select languages..."
              >
                <template #option="{ option }">
                  <span class="truncate">{{ option.name }}</span>
                </template>
                <template #option-create="{ option }">
                  <span class="flex-shrink-0">New language:</span>
                  <span class="block truncate">{{ option.name }}</span>
                </template>
              </USelectMenu>
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{ displayArrayValue(formState.languages_fluent) }}
              </div>
            </template>
          </UFormGroup>

          <!-- learning Language -->
          <UFormGroup
            label="What languages are you learning?"
            class="my-2"
            :ui="{
              label: {
                base: 'block font-medium text-gray-700 dark:text-gray-200',
              },
            }"
          >
            <template v-if="isEditing">
              <USelectMenu
                v-model="languagesLearning"
                :options="languageOptions"
                by="id"
                name="languages_learning"
                option-attribute="name"
                multiple
                searchable
                creatable
                :default-value="selectedLanguagesLearning"
                placeholder="Select languages..."
              >
                <template #option="{ option }">
                  <span class="truncate">{{ option.name }}</span>
                </template>
                <template #option-create="{ option }">
                  <span class="flex-shrink-0">New language:</span>
                  <span class="block truncate">{{ option.name }}</span>
                </template>
              </USelectMenu>
            </template>
            <template v-else>
              <div class="text-sm text-gray-700">
                {{ displayArrayValue(formState.languages_learning) }}
              </div>
            </template>
          </UFormGroup>
        </div>
      </div>

      <!-- Submit Button -->
      <div class="flex justify-end items-center gap-3">
        <UButton type="submit" color="orange" variant="outline" class="mt-4">
          Back
        </UButton>
      </div>
    </UForm>
  </div>
</template>
