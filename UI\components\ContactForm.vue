<template>
    <UModal key="contactModal" v-model="contactModalOpen" class="max-w-6xl" prevent-close>

      <UCard :ui="{ring:'ring-0'}" >

        <template #header>
          <div class="grid grid-cols-2 w-full">

            <div class="w-max">Adding contact for: {{ customer.name }}</div>
            <ULink class="justify-self-end" @click="contactModal.close" >
              <UIcon name="la:times-circle-solid" class="text-red-600 h-8 w-8 justify-start" /> 
            </ULink>
          </div>
        </template>
        
        <UForm :state="state" @submit.prevent="onSubmit">
          <div class="">

              <UFormGroup label="Name" class="" >
                <UInput v-model="state.name" required/>
              </UFormGroup>

              <UFormGroup label="Company" class="py-2">
                <UInput v-model="state.company" required/>
              </UFormGroup>

              <UFormGroup label="Title" class="py-2">
                <UInput v-model="state.title" />
              </UFormGroup>

              <UFormGroup label="Phone" class="py-2">
                <UInput v-model="state.phone"/>
              </UFormGroup>

              <UFormGroup label="Email" class="py-2">
                <UInput v-model="state.email" />
              </UFormGroup>

              <UFormGroup label="Linkedin Profile" class="py-2">
                <UInput v-model="state.linkedinUrl" />
              </UFormGroup>

              <UFormGroup label="Description" class="py-2">
                <UInput v-model="state.description" class="py-2"/>
              </UFormGroup>

              <UFormGroup label="Proposed Message" class="py-2">
                <UTextarea 
                    v-model="state.proposedMessage" 
                    :rows="10" 
                    autoresize 
                    resize
                    placeholder="Enter the message you'd like to send to the contact..."/>
              </UFormGroup>

            </div>
            <UButton type="submit" class="button-bg-gradient my-4 justify-center py-3 text-md w-full">
              Add Contact
            </UButton>
        </UForm>
      </UCard>
    </UModal>
</template>

<script lang="ts" setup>

import type { Customer, Agent, CustomerContact } from '../common/common';
import type { Database } from '~/lib/applysquad-common/database.types';
import { v4 as uid } from 'uuid';
import { toCustomerContact } from '~/common/customer-service';


interface Props {
  customer: Customer;
  agent: Agent;
  success: (contact: CustomerContact) => void;
}
const props = defineProps<Props>()
const contactModal = useModal()
const supabase = useSupabaseClient<Database>()
const state = reactive({
  customerId: undefined,
  agentId: undefined,
  name: undefined,
  company: undefined,
  title: undefined,
  phone: undefined,
  email: undefined,
  linkedinUrl: undefined,
  description: undefined,
  minutesSpent: undefined,
  proposedMessage: undefined,
})
const contactModalOpen = ref(false)


console.log("adding contact for customer:", props.customer.customerId, "agent:", props.agent.agentId)

async function onSubmit() {
  console.log('creating contact')

  const contactId = uid()

  const { data: contactData, error: contactError } = await supabase
    .from('contacts')
    .insert({
      id: contactId,
      name: state.name!,
      company: state.company!,
      title: state.title,
      phone: state.phone,
      email: state.email,
      linkedin_url: state.linkedinUrl,
      description: state.description,
      
    }).select('*')

    if (contactError) {
      throw contactError
    }

    const {data: customerContactData, error: customerContactError } = await supabase
      .from('customer_contacts')
      .insert({
        customer_id: props.customer.customerId,
        contact_id: contactId,
        src_id: props.agent.agentId,
        message: state.proposedMessage
      })
      .select('*, contact:contacts!inner(*)')
      .single();

    if (customerContactError) {
      throw customerContactError
    }

    props.success(toCustomerContact(customerContactData, customerContactData.contact!))
}

</script>