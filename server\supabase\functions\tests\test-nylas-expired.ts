import { assertEquals } from "jsr:@std/assert";
import { parseRawEvent } from '../nylas-webhook/index.ts'
import { startDeno } from '../nylas-webhook/index.ts'
import {Customer, supabaseClient } from '../shared/common.ts'
import { load } from 'jsr:@std/dotenv';
import { Database } from '../lib/database.types.ts'
import { createCustomer, get, post } from './test-common.ts';
import { SupabaseClient } from 'jsr:@supabase/supabase-js@2';
import { v4 as uid } from 'npm:uuid';

    
const testEventData = {
  id: "nylas-mock-id", 
  type: "grant.expired", 
  grantId: "NYLAS_GRANT_ID",
  email: "<EMAIL>"
}

Deno.test("ensures the nylas event can be parsed", () => {
    load({ export:true });
    const testEvent = Deno.readTextFileSync('./test-nylas-event.json');
    const grantExpired = parseRawEvent(testEvent)!;
    console.log(`payment: ${grantExpired}`)
    assertEquals(grantExpired.id, testEventData.id, "event id was incorrect");
    assertEquals(grantExpired.grant, testEventData.grantId, "grant was incorrect");
});

Deno.test("integration test",
    async () => {
      await load({ export:true });
      startDeno();
      const supabase = supabaseClient();
      const customer = await createCustomer(testEventData.email, supabase);
      await createNylasCustomer(customer, testEventData.grantId, supabase);
      
      const statusCode = await post(await Deno.readTextFileSync('./test-nylas-event.json'));
      assertEquals(statusCode, 200, "post should have succeeded");

      await assertGrantExpired(customer, supabase);
    }
)

Deno.test("ensure the challenge is returned", async () => {
  const challenge = uid();
  const response = await get(`?challenge=${challenge}`);
  const responseText = await response.text();
  console.log("response", responseText)
  assertEquals(responseText, challenge);
})

async function createNylasCustomer(
  customer: Customer,
  grant: string,
  supabase: SupabaseClient<Database>) {
    const {error} = await supabase
        .from("nylas_customers")
        .upsert({
          customer_id: customer.id,
          grant: grant,
          status: "READY"
        }, {onConflict: "customer_id", ignoreDuplicates: true})
    if (error) {
      throw error
    }    
    console.log(`created nylas customer ${customer.id}`)
}

async function assertGrantExpired(
  customer: Customer,
  supabase: SupabaseClient<Database>) {
    const {data, error} = await supabase
        .from("nylas_customers")
        .select("*")
        .eq("customer_id", customer.id)
    if (error) {
      throw error
    }
    console.log(`nylad grant status: ${data![0].status}`)
    assertEquals(data![0].status, "EXPIRED", "grant should have been expired")
}

