import { StopWatch } from "stopwatch-node";
import { generateText } from "ai";
import { Buffer } from 'node:buffer';
import * as AI   from "./ai.ts";
import { PROMPTS } from "./system-prompts.ts";
import { pdfToText } from "pdf-ts";



export interface CustomerInfo {
  functionalPreference?: string,
  cvs: Blob[],
}

export interface JobInfo {
  jobTitle: string,
  company: string,
  jobDescription: string
}

export async function generateCoverLetter(
    custInfo: CustomerInfo,
    jobInfo: JobInfo
    ): Promise<string> {

    console.log("Generating cover letter");
    // concat all the CVs together with delimiters to allow AI to distinguish between them
    const cvText = await Promise.all(custInfo.cvs.map(async (document, index) => (
        ` [BEGIN_RESUME ${index}: ${(await toText(await document.arrayBuffer()))} :END_RESUME ${index}] `
    )));
    const userPrompt = buildUserPrompt(custInfo, jobInfo);
    const systemPrompt = buildSystemPrompt();
    const aiModel = AI.MODELS.GPT4o;
    
    console.log(`attaching ${cvText.length} characters from resumes to cover letter request`);
    console.log("user prompt:", userPrompt);
    console.log("system prompt:", systemPrompt);
    console.log("ai model:", aiModel);

    let coverLetter = "";
    try {
        const timer = startTimer();
          // https://sdk.vercel.ai/docs/reference/ai-sdk-core/generate-text
        const { text } = await generateText({
          model: aiModel,
          messages: [
            {
              role: "system",
              content: systemPrompt
            },
            {
              role: "user",
              content : [
                { type: "text", text: userPrompt },
                { type: "text", text: cvText.join(" || ")}
              ]
            }
          ]
        });
        timer.stop();
        console.log(`AI call took [${timer.getTotalTime()}]ms`)
        console.log("generated cover letter", text);
        coverLetter = text;
        
      } catch (e) {
        console.error("error on generate text", e);
        throw e;
      }

    return coverLetter;
}

function startTimer() {
    const stopwatch = new StopWatch();
    stopwatch.start();
    return stopwatch;
}

async function toText(buffer: ArrayBuffer): Promise<string> {
    const text = await pdfToText(Buffer.from(buffer))
    console.log("pdf text:", text);
    return text;
}

const example = "Here is an example cover letter: [ <p>Capco</p><p><br></p><p>Dear Hiring Manager,</p><p><br></p><p>What drew me to Capco is the opportunity to work at the intersection of digital transformation and analytics at a global scale. This role is a strong fit with my experience driving data-led decision-making and strategic consulting, and I am excited by the challenge of applying that to a diverse portfolio of products and markets.</p><p><br></p><p>My professional background at IBM Consulting as a Business Transformation Consultant aligns closely with Capco's requirements. I led a diverse team of 40 software testers in a large-scale system conversion project, optimizing data use and enhancing team efficiency by 30%. My experience includes designing data-driven reports, orchestrating strategies to leverage infrastructure data, and coordinating cross-functional project management, which directly relates to Capco's focus on digital transformation and data analytics.</p><p><br></p><p>I am eager to bring my experience in digital transformation and strategy to Capco. Thank you for your time and consideration. I look forward to the opportunity to contribute to your team.</p><p><br></p><p>Sincerely,</p><p>John Doe</p><p><a href='https://www.linkedin.com/in/john-doe' target='_blank'>https://www.linkedin.com/in/john-doe</a></p><p><EMAIL> / <EMAIL></p><p>******-430-4898</p> ]";

const exampleFormatting = "Use this html formatting: [ <p>{Company}</p><p><br></p><p>Dear Hiring Manager,</p><p><br></p><p>{paragraph 1}</p><p><br></p><p>{paragraph 2}</p><p><br></p><p>{paragraph 3}</p><p><br></p><p>Sincerely,</p><p>{name}</p><p><a href='{linkedin url}' target='_blank'>{linkedin url}</a></p><p>{email}</p><p>{phone}</p> ]. "

function buildUserPrompt(custInfo: CustomerInfo, jobInfo: JobInfo) {
  let prompt = "";
  if (custInfo.functionalPreference) {
    prompt += ` The job seeker has personal values: [ ${custInfo.functionalPreference} ]. `;
  }
  return prompt.concat(`Job Title: [${jobInfo.jobTitle}], Hiring Company: [${jobInfo.company}], Job Description: [${jobInfo.jobDescription}] `);
}

function buildSystemPrompt(): string {
  return PROMPTS.cover_letter.v2 + exampleFormatting + example
}