set check_function_bodies = off;

create table "public"."stats_daily_payments" (
    "id" uuid primary key default gen_random_uuid(),
    "date" date not null,
    "total_payment_amount" integer not null default 0,
    "payment_amount_today" integer not null default 0,
    "created_at" timestamp with time zone not null default now()
);


CREATE OR REPLACE FUNCTION public.populate_stats_daily_payments()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  report_date date := current_date - 1;
  total_amount int;
  amount_today int;
BEGIN
  SELECT COALESCE(SUM(amount_pennies), 0) INTO total_amount
  FROM public.payments;

  SELECT COALESCE(SUM(amount_pennies), 0) INTO amount_today
  FROM public.payments
  WHERE created_at::date = report_date;

  INSERT INTO public.stats_daily_payments (
    id,
    date,
    total_payment_amount,
    payment_amount_today,
    created_at
  ) VALUES (
    gen_random_uuid(),
    report_date,
    total_amount,
    amount_today,
    now()
  );
END;
$function$
;


CREATE EXTENSION IF NOT EXISTS pg_cron;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM cron.job WHERE jobname = 'daily_stats_payments'
  ) THEN
    PERFORM cron.schedule(
      job_name := 'daily_stats_payments',
      schedule := '25 0 * * *',
      command := 'SELECT public.populate_stats_daily_payments();'
    );
  END IF;
END
$$;