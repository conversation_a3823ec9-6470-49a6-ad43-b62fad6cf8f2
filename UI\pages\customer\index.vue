<template>
  <div v-if="isLoading">
    <div class="flex justify-center items-center pt-20">
      <BaseSpinner :show-loader="isLoading" size="lg" />
    </div>
  </div>
  <UCard v-if="!isLoading" class="mx-auto my-7 max-w-7xl bg-white shadow-lg">
    <UTabs
      v-model="selectedTab"
      :items="tabs"
      class="mx-0"
      :ui="{ list: { tab: { active: 'ring-1 ring-gray-300', size: 'text-lg' } } }"
    >
      <template #account>
        <div class="flex flex-col gap-6">
          <UCard
            class="tabContent border-1 w-full items-center overflow-scroll"
          >
            <div class="w-full">
              <div class="p-2 grid md:grid-cols-2 gap-4">
                <UCard class="border-1 rounded-md sm:w-full">
                  <h3 class="text-lg font-semibold text-orange-600">Profile</h3>
                  <table class="border-0 mx-auto w-full table-auto">
                    <tbody>
                      <tr>
                        <td class="p-2 w-28">Name:</td>
                        <td class="p-2 truncate max-w-[200px]">
                          {{ customer.name }}
                        </td>
                      </tr>
                      <tr>
                        <td class="p-2 w-28">Email:</td>
                        <td class="p-2 truncate max-w-[200px]">
                          {{ customer.email }}
                        </td>
                      </tr>
                      <tr>
                        <td class="p-2 w-28">Whatsapp #:</td>
                        <td class="p-2 truncate max-w-[200px]">
                          {{ customer.whatsappNumber }}
                        </td>
                      </tr>
                      <tr>
                        <td class="p-2 w-28">Linkedin URL:</td>
                        <td class="p-2 truncate max-w-[200px]">
                          <ULink
                            v-if="customer.linkedinUrl"
                            :to="customer.linkedinUrl"
                            target="_blank"
                            class="text-orange-600"
                          >
                            {{ customer.linkedinUrl }}
                          </ULink>
                        </td>
                      </tr>
                      <tr>
                        <td class="p-2 w-28">Your Whatsapp Group:</td>
                        <td class="p-2 truncate max-w-[200px]">
                          <UButton
                            v-if="customer.whatsappGroupUrl"
                            :to="customer.whatsappGroupUrl"
                            variant="outline"
                            color="green"
                            target="_blank"
                            class="h-10"
                            icon="la:whatsapp"
                          >
                            Whatsapp Group
                          </UButton>
                        </td>
                      </tr>
                      <tr v-if="customer.affiliateId" class="mt-4">
                        <td class="p-2 w-28">Your Affiliate Link</td>
                        <td class="flex items-center gap-2">
                          <UInput
                            :model-value="affiliateLink"
                            class="w-full"
                            readonly
                          />
                          <UButton
                            icon="i-heroicons-clipboard"
                            @click="copyToClipboard(affiliateLink)"
                          >
                            Copy
                          </UButton>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </UCard>

                <UCard
                  v-if="customer.affiliateId"
                  class="border-1 rounded-md sm:w-full"
                >
                  <div class="pt-4">
                    <CustomerReferrals :customer="customer" />
                  </div>
                </UCard>

                <!-- <UCard class="border-1 rounded-md sm:w-full md:w-1/2">
                  <h3 class="text-lg font-semibold text-orange-600">
                    Settings
                  </h3>
                  <div class="grid grid-cols-3 p-2">
                    <div class="text-right pr-4">Email Access:</div>
                    <div v-if="emailSetup">
                      <UIcon
                        name="la:check-circle-solid"
                        class="text-green-500 w-6 h-6"
                      />
                    </div>
                    <div v-else>
                      <UPopover>
                        <UButton
                          icon="la:envelope-solid"
                          class="button-bg-gradient"
                          >Setup Email</UButton
                        >

                        <template #panel="{ close }">
                          <div class="p-5 max-w-lg">
                            <div class="text-sm">
                              <p>
                                The email you provide access to, must be the
                                same as your Applysquad login. Our only
                                intention is to be able to verify job board
                                accounts and click OTP links. We do not display
                                images. We do not display your entire inbox to
                                our Agents, but they are allowed to search by a
                                keyword, in order to find the email from the job
                                board or company in order to log in and apply on
                                your behalf.
                              </p>
                              <br />
                              <p>
                                Applysquad uses a 3rd party
                                <span class="font-bold">(Nylas)</span> to
                                authenticate to your email inbox. You will be
                                taken to their site, where you may select your
                                email provider, and then login. You will be
                                directed back to Applysquad after you've
                                finished. If you encounter an issue, just start over.
                              </p>
                              <br/>
                              <p>
                                Example: <EMAIL> -> select provider Google.
                                If you don't know your provider, try using "IMAP".
                                Reach out to your agent on Whatsapp if you have
                                persistent issues.
                              </p>
                            </div>

                            <div class="flex gap-2 justify-end mt-4">
                              <div class="">
                                <UButton
                                  label="I do not agree"
                                  @click="close"
                                  class="button-bg-gradient"
                                />
                              </div>
                              <div class="">
                                <UButton
                                  class="button-bg-gradient"
                                  @click="setupEmail()"
                                  >I agree</UButton
                                >
                              </div>
                            </div>
                          </div>
                        </template>
                      </UPopover>
                    </div>
                    <div v-if="emailSetup">
                      <UButton
                        icon="la:ban"
                        @click="revokeEmail()"
                        :loading="revoking"
                        >Revoke Access</UButton
                      >
                    </div>
                  </div>
                </UCard> -->
              </div>
            </div>

            <!-- </UCard>

          <UCard
            class="tabContent border-1 w-full items-center overflow-scroll"
          > -->
            <div class="w-full">
              <div class="p-2">
                <UCard class="border-1 rounded-md w-full">
                  <CustomerOnboardingTabs
                    :customer="customer"
                    :is-customer="true"
                    v-model:has-onboarding="hasOnboarding"
                  />
                </UCard>
              </div>
            </div>
          </UCard>
        </div>
      </template>

      <template #documents>
        <UCard class="tabContent">
          <UContainer class="w-full left-0">
            <!-- can't upload job docs here -->
            <FileUploader
              :customer-id="customer.customerId"
              :document-types="docTypes"
              :document-added-callback="(doc) => successfulDocUpload(doc)"  
            />

          </UContainer>

          <UTable
            :rows="documents"
            :columns="documentColumns"
            :ui="{
              wrapper: 'relative overflow-x-auto',
              base: 'min-w-full table-fixed',
              td: {
                base: 'truncate max-w-[10px]',
                padding: 'px-1 sm:px-4 py-2',
              },
              th: {
                padding: 'px-1 sm:px-4 py-2',
                font: 'font-medium',
              },
            }"
            :empty-state="{
              icon: 'i-heroicons-circle-stack-20-solid',
              label: 'No items.',
            }"
          >
            <template #actions-data="{ row }">
              <div class="grid grid-cols-2 w-16 sm:w-20 sm:gap-2">
                <span>
                  <UIcon
                    class="text-red-500 w-7 h-7 hover:cursor-pointer"
                    name="la:trash"
                    :title="`delete ${row.name}`"
                    @click="deleteDocument(row)"
                  />
                </span>
                <span>
                  <DocumentDownload :document="row" />
                </span>
              </div>
            </template>
          </UTable>
        </UCard>
      </template>

      <template #jobs>
        <UCard class="tabContent w-max justify-end">
          <template #header>
            <div class="m-0 flex justify-between gap-4">
              <JobPlanSummaryBar
                v-model:customer-jobs="jobs"
                :customer="customer"
              />
              <div class="flex justify-end"/>
              <JobHelp />
            </div>
          </template>

          
          <JobTracker
            v-model:jobs="jobs"
            v-model:documents="documents"
            :customer="customer"
            :is-customer="true"
          />
        </UCard>
      </template>

      <template #home>
        <div class="flex flex-col">
          <div class="flex justify-end gap-4">
            <div class="flex items-center gap-6">
              <div class="flex items-center justify-center gap-4">
                <button
                  :disabled="selectedHomeTab === 0"
                  class="size-8"
                  @click="setTab(selectedHomeTab - 1)"
                >
                  <UIcon
                    color="orange"
                    variant="outline"
                    class="size-8 text-orange-600 cursor-pointer"
                    name="la:arrow-circle-left"
                  />
                </button>
              </div>
              <div class="flex items-center gap-3">
                <div
                  v-for="(step, index) in homeTabSteps"
                  :key="index"
                  class="rounded-full px-3 py-1 cursor-pointer h-fit"
                  :class="
                    selectedHomeTab === index
                      ? 'bg-orange-600 text-white'
                      : 'bg-gray-200 text-orange-600'
                  "
                  @click="setTab(index)"
                >
                  {{ step }}
                </div>
              </div>
              <div class="flex items-center">
                <button
                  :disabled="selectedHomeTab === (homeTabSteps === 3 ? 2 : 1)"
                  class="size-8"
                  @click="setTab(selectedHomeTab + 1)"
                >
                  <UIcon
                    class="size-8 text-orange-600"
                    name="la:arrow-circle-right"
                  />
                </button>
              </div>
            </div>
          </div>
          <div v-if="selectedHomeTab === 0" >
            <div class="text-3xl font-semibold text-orange-600 text-center my-4">
              Welcome to Applysquad
            </div>
            <div class="grid justify-items-center">
              <div class="w-2/3  text-xl/9">
                <div class="text-2xl text-center py-10">
                  Thanks for trusting us to help you LEVEL UP in life.
                  Our goal is to make this process as easy as possible for you.
                </div>
                <div v-if="(!hasPlan || !hasOnboarding)" class="">
                  There are a couple things you'll need to do to get started:

                  <div :class=" { homeItemBox: true } ">
                    <ULink
                      to="/purchase"
                      class="text-orange-600 hover:text-orange-700 block"
                      :class="{ 'line-through': hasPlan }"
                    >
                    1. Purchase a Plan
                    </ULink>
                  </div>

                  <div :class=" { homeItemBox: true } ">
                    <ULink
                      to="/customer/onboarding"
                      class="text-orange-600 hover:text-orange-700 block text-nowrap"
                      :class="{ 'line-through': hasOnboarding } "
                    >
                    2. Fill out the Onboarding Survey
                    </ULink>
                  </div>

                  <div :class=" { homeItemBox: true, 'line-through': hasUploadedCV } ">
                    3. Upload your CV/resume(s) to Documents 
                  </div>

                </div>
                <div v-else class="text-center" >
                  You've already purchased a Plan, uploaded your CV, and completed your onboarding survey.
                  Here is what's next:
                  <JobProcess/>
                </div>
              </div>
              <div class="text-orange-500 font-bold text-2xl text-center text-nowrap mt-8">
                LET'S GO! 
              </div>
            </div>

            <div
              class="flex flex-col justify-center items-center p-5 shadow-none"
            >
              <UCard class="block mt-10 w-[600px] p-12 md:p-0 my-auto">
                <div
                  className="relative cursor-pointer h-80 sm:h-[22rem] w-full"
                >
                  <iframe
                    className="relative z-10"
                    width="100%"
                    height="100%"
                    :src="`https://www.youtube.com/embed/${videoId}`"
                    title="YouTube video player"
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                  />
                </div>
              </UCard>
            </div>
          </div>

          <div v-if="selectedHomeTab === 1 && !hasPlan">
            <PlansCards />
          </div>

          <div
            v-if="selectedHomeTab === 2 || (selectedHomeTab === 1 && hasPlan)"
          >
            <div class="mt-10">
              <CustomerOnboardingTabs
                :customer="customer"
                :is-customer="true"
                v-model:has-onboarding="hasOnboarding"
              />
            </div>
          </div>
          <div class="flex justify-end">
            <UTooltip
              v-if="!isOrientationComplete && !hasOnboarding"
              text="Complete the onboarding survey first"
            >
              <UButton
                class="mt-4 px-4 py-2 rounded w-fit"
                :disabled="!hasOnboarding"
                @click="onFinishClick"
              >
                I Understand
              </UButton>
            </UTooltip>
            <UButton
              v-else-if="!isOrientationComplete"
              class="mt-4 px-4 py-2 rounded w-fit"
              @click="onFinishClick()"
            >
              I Understand
            </UButton>
          </div>
        </div>
      </template>

      <template #contacts>
        <UCard class="tabContent w-max">
          <ContactTracker
            :customer="customer"
            :is-customer="true"
            :customer-contacts="contacts"
            :refresh-contacts="() => reloadContacts()"
          />
        </UCard>
      </template>
    </UTabs>
  </UCard>
</template>

<script setup lang="ts">
import { ref } from "vue";
import {
  DocumentType,
  CustomerJobStatus,
  type CustomerContact,
  type Customer,
  type Document,
  type CustomerJob,
  CustomerOrientationStatus,
  type Plan,
} from "~/common/common";
import {
  fetchNetworkingPlan,
  fetchCustomer,
  fetchCustomerContacts,
  fetchCustomerJobs,
  hasCustomerSetupEmailAccess,
  fetchActivePlans,
  hasCompletedOnboarding,
  getCustomerDocuments,
} from "~/common/customer-service";
import type { Database } from "~/lib/applysquad-common/database.types";
import ContactTracker from "~/components/ContactTracker.vue";

definePageMeta({
  middleware: ["auth"],
});

// Define reactive state variables
const toast = useToast();
const toastTimeout = 4000;
const toastErrorTimeout = 10000;
const supabase = useSupabaseClient<Database>();
const user = useSupabaseUser();
const videoId = "Z9Of_QFELtA";
const homeTabSteps = ref(3);
const isLoading = ref(false);
const customer = ref<Customer>(await fetchCustomer(user.value!.id, supabase));
const customerPlan = ref<Plan[]>(
  await fetchActivePlans(user.value!.id, supabase)
);
const hasNetworking =
  (await fetchNetworkingPlan(customer.value.customerId, supabase)) != undefined;

const hasOnboarding = ref(false);

// Fetch onboarding status
const checkOnboarding = async () => {
  hasOnboarding.value = await hasCompletedOnboarding(true, user.value, supabase);
};

const hasPlan = computed(() => customerPlan.value.length > 0);
const isOrientationComplete = computed(() => customer.value.orientationStatus === "COMPLETE");
const affiliateLink = computed(
  () => `${SITE_URL}/signup?afid=${customer.value.affiliateId}`
);
const hasUploadedCV = computed<boolean>(() => 
  documents.value.find( doc => doc.type === DocumentType.CV ) != undefined);

const tabs = ref(await getTabs(hasPlan.value, isOrientationComplete.value));
const selectedTab = ref(0);
const selectedHomeTab = ref(0);

const config = useRuntimeConfig();
const SITE_URL = config.public.SITE_URL || window.location.origin;
const emailSetup = ref(
  await hasCustomerSetupEmailAccess(customer.value.customerId, supabase)
);
const error = useRequestURL().searchParams.get("error_message");
if (error) {
  errorToast(error.toString());
}

const revoking = ref(false);
const documents = ref<Document[]>([]);
const customerJobStatus = [
  CustomerJobStatus.NEW,
  CustomerJobStatus.APPROVED,
  CustomerJobStatus.DECLINED,
  CustomerJobStatus.APPLIED,
  CustomerJobStatus.SATISFIED,
  CustomerJobStatus.DISSATISFIED,
  CustomerJobStatus.CUSTOMER_APPLIED, 
];
const docTypes = [DocumentType.CV, DocumentType.COVER];
const jobs = ref<CustomerJob[]>([]);
const contacts = ref<CustomerContact[]>([]);
const documentColumns = [
  {
    label: "Name",
    key: "name",
    sortable: true,
  },
  {
    label: "Type",
    key: "type",
    sortable: true,
  },
  {
    label: "Actions",
    key: "actions",
  },
];



const setTab = (index: number) => {
  selectedHomeTab.value = index;
};

const onFinishClick = async () => {
  const customerPlan = ref<Plan[]>(
    await fetchActivePlans(user.value!.id, supabase)
  );

  if (customerPlan.value.length) {
    const { error } = await supabase
      .from("customers")
      .update({ orientation_status: CustomerOrientationStatus.COMPLETE })
      .eq("id", customer.value.customerId);

    if (error) {
      console.error("Error loading documents:", error);
    }

    customer.value.orientationStatus = CustomerOrientationStatus.COMPLETE;

    // Re-fetch tabs after updating status
    tabs.value = await getTabs(hasPlan.value, true);

    selectedTab.value = 1;

    // Fetch jobs and contacts after setting selectedTab
    window.scrollTo({ top: 0, behavior: "smooth" });
  } else {
    errorToast(`Please purchase plan first to get jobs list.`);
  }
};


function copyToClipboard(text: string) {
  navigator.clipboard.writeText(text);
  // Show success toast
  toast.add({
    description: "Affiate Link copied Successfully",
    color: "green",
    timeout: 2000,
  });
}

async function getTabs(hasPlan: boolean, isOrientationComplete: boolean) {
  const tabs = [];

  // Always show Home
  tabs.push({ slot: "home", label: "Home" });

  // Show Jobs and/or Contacts only if they have an open Plan AND orientation is complete
  if (hasPlan) {
    tabs.push({ slot: "jobs", label: "Jobs" });

    if (hasNetworking) {
      tabs.push({ slot: "contacts", label: "Contacts" });
    }
  }

  // Always show Documents and Account tabs
  tabs.push({ slot: "documents", label: "Documents" });
  tabs.push({ slot: "account", label: "Account" });
  return tabs;
}

async function updateTabs() {
  tabs.value = await getTabs(hasPlan.value, isOrientationComplete.value);
  selectedTab.value = 0;

  if (hasPlan.value && !isOrientationComplete.value) {
    homeTabSteps.value = 2;
    selectedTab.value = 0; // Set tab to "Jobs"
  } else if (hasPlan.value && isOrientationComplete.value) {
    selectedTab.value = 1; // Set tab to "Jobs"
  }
  await loadJobs();
  await reloadContacts();
}


async function loadJobs() {
  jobs.value = await fetchCustomerJobs(
    customer.value.customerId,
    customerJobStatus,
    supabase
  );
  jobs.value = [...jobs.value]; 
}

async function reloadContacts() {
  if (hasNetworking) {
    contacts.value = await fetchCustomerContacts(
      customer.value.customerId,
      supabase
    );
    console.log("fetched customer contacts");
  }
}

async function loadCustomerDocuments() {
  console.log('loacing customer docs', documents.value.length);
  documents.value = await getCustomerDocuments(customer.value.customerId, supabase);
}

async function deleteDocument(deleted: Document) {
  // delete from database
  const { error } = await supabase
    .from("documents")
    .update({ active: false })
    .eq("id", deleted.id);

  if (error) {
    throw error;
  }

  errorToast(`Document ${deleted.name} deleted`);

  documents.value = documents.value.filter((doc) => doc.id != deleted.id);
}

function successfulDocUpload(doc: Document) {
  console.log('added document');
  documents.value.push(doc);
}

async function setupEmail() {
  navigateTo(`/init-email-setup?customerId=${customer.value.customerId}`, {
    external: true,
  });
}

async function revokeEmail() {
  revoking.value = true;
  const response = await $fetch(
    `/revoke-email-access?customerId=${customer.value.customerId}`
  );
  if (response === "OK") {
    emailSetup.value = false;
    revoking.value = false;
    successToast("email access revoked");
  } else {
    errorToast("problem revoking email access, please try again");
  }
}

function successToast(message: string) {
  toast.add({
    id: message,
    description: message,
    color: "green",
    timeout: toastTimeout,
  });
}
function errorToast(message: string) {
  toast.add({
    id: message,
    description: message,
    color: "red",
    timeout: toastErrorTimeout,
  });
}
https://www.airbnb.com/hosting/reservations/details/HMFE438HWR
onMounted(async () => {
  isLoading.value = true;
  await checkOnboarding();
  await loadJobs();
  await loadCustomerDocuments();
  await updateTabs();
  isLoading.value = false;
});
</script>

<style scoped>
.tabContent {
  margin-bottom: 10px;
  margin-bottom: 0;
  width: 100%;
}
.homeItemBox {
  @apply bg-slate-100 rounded-xl p-6 my-2;
}
</style>
