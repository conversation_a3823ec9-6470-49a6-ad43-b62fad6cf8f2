{"type": "INSERT", "schema": "public", "table": "jobs", "record": {"id": "@@JOB_ID@@", "description": "About the job About Subaru LOVE. It's what makes Subaru, Subaru®. As a leading auto brand in the US, we strive to be More Than a Car Company®. Subaru believes in being a positive force in the communities in which we live and work, not just with donations but with actions that set an example for others to follow. That's what we call our Subaru Love Promise®. Subaru is a globally renowned automobile manufacturer known for its commitment to innovation, safety, and sustainability. With a rich history dating back to 1953, Subaru has consistently pushed the boundaries of automotive engineering to deliver vehicles that offer not only exceptional performance but also a unique blend of utility and adventure. Subaru's company culture is built on collaboration, diversity, and a shared passion for our product. We foster an inclusive environment that encourages employees to bring their unique perspectives and talents to the table. Our team members are driven by a common goal: to create exceptional vehicles that inspire and delight our customers. As a Senior Software Engineer (Retailer Portals) on the Subarunet Team, you will contribute to the development and evolution of Subaru's dynamic and multi-faceted retailer portal system. Subarunet serves as a critical hub of responsive web applications designed to connect Subaru with its retailer network, offering seamless performance across desktop and mobile platforms.About the Role  As a Senior Software Engineer on the Subarunet Team, you will contribute to the development and evolution of Subaru's dynamic and multi-faceted retailer portal system. Subarunet serves as a critical hub of responsive web applications designed to connect Subaru with its retailer network, offering seamless performance across desktop and mobile [platforms.In](http://platforms.in/) this role, you will leverage your expertise in Java development to build scalable, modular microservices while collaborating on front-end development to create intuitive user interfaces. Partnering with business analysts, lead software engineers, and stakeholders, you will design innovative solutions that enhance Subaru's retail operations Key Responsibilities  Translate complex business requirements into functional, scalable solutions. Design and implement robust back-end and front-end code adhering to industry best practices and coding standards. Collaborate with IT, business teams, and QA testers to ensure quality delivery.  Provide accurate high-level and detailed task estimates.  Conduct peer code reviews and mentor junior team members.  Manage the full software development lifecycle from conception to deployment.  Partner with DevOps to streamline CI/CD pipelines and SDLC processes.  Troubleshoot and debug system issues to maintain operational excellence.  Oversee testing efforts and coordinate project deployments.  Apply system knowledge to identify opportunities for improvement and enhancement.  Stay up-to-date with emerging development methodologies, architectural trends, and technical standards.  Work effectively within an Agile framework.  Provide after-hours support on a rotational or as-needed basis. Technical Skills & Qualifications Required: Extensive experience in Java development and object-oriented programming. Extensive experience in web services software architecture and design Proficiency with the Spring Framework (Spring Boot). Experience with modern front-end frameworks such as Vue.js, React, or Angular. Deep understanding of CI/CD pipelines and contemporary DevOps practices. Advanced skills in SQL, including stored procedures and query optimization. Recommended: Familiarity with the Azure Cloud Platform. Knowledge of UNIX/Linux environments and basic shell scripting.  Experience with Spring MVC and JSP. Non-Technical Skills Exceptional verbal and written communication skills, capable of engaging both technical and non-technical audiences. Self-motivated, organized, and able to balance independent work with team collaboration. Strong problem-solving skills with a focus on quality and innovation. A collaborative mindset with a willingness to learn, adapt, and explore new ideas. A proactive team player who supports colleagues and knows when to seek assistance. Work Environment Hybrid Role: Remote work available two days per week (Wednesdays & Fridays) after the first 90 days. Remote Option: Candidates living more than 100 miles from Subaru Headquarters in Camden, NJ, may be considered for a fully remote position.  Education & Experience A BA/BS degree in Computer Science or a related field. At least 6 years of professional experience in enterprise software development. COMPENSATION: The recruiting base salary range for this full-time position is $85,400.00- $120,000.00/ year. Within the range, individual pay is determined by factors, including job-related skills, experience, and relevant education or training. (Internal Job Grade: P2_T) In addition to competitive salary, Subaru offers an amazing benefits package that includes: Medical, Dental, Vision Plans Pension, Profit Sharing, and 401K Match Offerings 15 Vacation days, 9 Company Holidays, 5 Floating Holidays, and 5 Sick days. Tuition Reimbursement Program Vehicle Discount Programs See our Careers landing page for additional information about our compensation and benefit programs. Benefits found in job post 401(k)"}, "old_record": null}