<template>
  <div
    class="data-entry-container py-16 px-8 bg-gradient-to-r from-red-500 to-orange-400"
  >
    <UCard class="shadow-2xl shadow-gray-700 mx-auto max-w-xl bg-white">
      <template #header>
        <h1 class="text-4xl font-bold text-center text-gray-700 mb-2">
          Sign Up Now!
        </h1>
      </template>

      <UForm :state="state" @submit="createAgent">
        <div class="text-sm font-medium text-gray-700">
          <UFormGroup label="Name" name="name" class="my-3">
            <UInput v-model="state.name" required />
          </UFormGroup>

          <UFormGroup label="Email" name="email" class="my-3">
            <UInput
              v-model="state.email"
              placeholder="<EMAIL>"
              required
            />
          </UFormGroup>

          <UFormGroup label="WhatsApp Number" name="whatsapp" class="my-3">
            <UInput v-model="state.whatsapp" required />
          </UFormGroup>

          <UFormGroup label="Date of birth" name="dateOfBirth" class="my-3">
            <UPopover :popper="{ placement: 'bottom-start' }">
              <UButton
                icon="i-heroicons-calendar-days-20-solid"
                :label="
                  state.dateOfBirth && format(state.dateOfBirth, 'dd/MM/yyyy')
                "
                variant="outline"
                :ui="{
                  base: 'w-full',
                  truncate: 'flex justify-center w-full',
                }"
                truncate
              />
              <template #panel="{ close }">
                <DatePicker
                  v-model="state.dateOfBirth"
                  is-required
                  @close="close"
                />
              </template>
            </UPopover>
          </UFormGroup>

          <UFormGroup
            label="Linkedin Profile URL"
            name="linkedinUrl"
            class="my-3"
          >
            <UInput
              v-model="state.linkedinUrl"
              placeholder="paste your linkedin profile URL"
              required
            />
          </UFormGroup>

          <UFormGroup label="Upwork Profile URL" name="upworkUrl" class="my-3">
            <UInput
              v-model="state.upworkUrl"
              placeholder="paste your upwork profile URL"
              required
            />
          </UFormGroup>

          <UFormGroup label="Gender" name="gender" class="my-3">
            <USelectMenu
              v-model="state.gender"
              value-attribute="value"
              option-attribute="label"
              :options="genders"
              required
            />
          </UFormGroup>

          <UFormGroup label="Country" name="country" class="my-3">
            <UInput v-model="state.country" />
          </UFormGroup>

          <UFormGroup label="Password" name="password" class="my-3">
            <UInput v-model="state.password" type="password" required />
          </UFormGroup>

          <UButton
            type="submit"
            class="bg-orange-500 hover:bg-orange-600 my-4 justify-center py-3 text-lg w-full"
          >
            Signup
          </UButton>

          <div v-if="errorMessage" class="text-red-500 text-center">
            {{ errorMessage }}
          </div>
          <div v-if="successMessage" class="text-green-500 text-center">
            {{ successMessage }}
          </div>
        </div>
      </UForm>
    </UCard>
  </div>
</template>

<script setup lang="ts">
import type { Database } from "~/lib/applysquad-common/database.types";
import { format } from "date-fns";

const config = useRuntimeConfig();
const supabase = useSupabaseClient<Database>();
const genders = [
  { value: "MALE", label: "Male" },
  { value: "FEMALE", label: "Female" },
  { value: "OTHER", label: "Other" },
  { value: "PREFER_NOT", label: "Prefer not to say" },
];
const successMessage = ref("");
const errorMessage = ref("");

const state = reactive({
  email: "",
  password: undefined,
  name: undefined,
  whatsapp: undefined,
  gender: undefined,
  dateOfBirth: undefined,
  linkedinUrl: undefined,
  upworkUrl: undefined,
  country: undefined,
});

async function createAgent() {
  console.log(`creating agent ${state.email}`);
  errorMessage.value = "";

  // Check if the agent already exists
  const { data: existingAgent, error: existingAgentError } = await supabase
    .from("agents")
    .select("id")
    .eq("email", state.email?.toLowerCase().trim())
    .single();

  if (existingAgent) {
    errorMessage.value = "Account Already Exists.";
    return;
  }

  // create user
  const { data: authData, error: authError } = await supabase.auth.signUp({
    email: state.email?.toLowerCase().trim(),
    password: state.password!,
    options: {
      emailRedirectTo: `${config.public.SITE_URL}/login`,
      data: {
        role: "agent",
      },
    },
  });
  const agentId = authData.user?.id;
  console.log("created agent ", agentId);
  if (authError) {
    const authErrorMessage = "Error signing up. " + authError.message;
    errorMessage.value = authErrorMessage;
    console.log(authErrorMessage);
    return;
  }

  function formatDateToUTC(date: any): string {
    // Create a UTC date to avoid timezone shifts
    const utcDate = new Date(
      Date.UTC(
        new Date(date).getFullYear(),
        new Date(date).getMonth(),
        new Date(date).getDate()
      )
    );

    // Convert to YYYY-MM-DD format
    return utcDate.toISOString().split("T")[0];
  }

  // create agent
  const { error } = await supabase.from("agents").insert({
    id: agentId,
    name: state.name!,
    email: state.email?.toLowerCase().trim(),
    whatsapp_number: state.whatsapp!,
    gender: state.gender!,
    date_of_birth: formatDateToUTC(state.dateOfBirth!),
    linkedin_url: state.linkedinUrl!,
    upwork_url: state.upworkUrl,
    country: state.country,
  });

  if (error) {
    console.error(error);
    errorMessage.value = "Error creating agent " + error.message;
    return;
  } else {
    successMessage.value =
      "Thank you for signing up. Check your email for a confirmation link";
  }
}
</script>
