create table "public"."customer_onboarding" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "job_types" job_type[],
    "languages" text[],
    "locations" text[],
    "industries" text[],
    "salary_requirements" text,
    "customer_id" uuid not null default gen_random_uuid()
);


alter table "public"."customer_onboarding" enable row level security;

alter table "public"."customers" add column "onboarding_complete" boolean not null default false;

CREATE UNIQUE INDEX customer_onboarding_pkey ON public.customer_onboarding USING btree (id);

alter table "public"."customer_onboarding" add constraint "customer_onboarding_pkey" PRIMARY KEY using index "customer_onboarding_pkey";

alter table "public"."customer_onboarding" add constraint "customer_onboarding_customer_id_fkey" FOREIGN KEY (customer_id) REFERENCES customers(id) not valid;

alter table "public"."customer_onboarding" validate constraint "customer_onboarding_customer_id_fkey";

grant delete on table "public"."customer_onboarding" to "anon";

grant insert on table "public"."customer_onboarding" to "anon";

grant references on table "public"."customer_onboarding" to "anon";

grant select on table "public"."customer_onboarding" to "anon";

grant trigger on table "public"."customer_onboarding" to "anon";

grant truncate on table "public"."customer_onboarding" to "anon";

grant update on table "public"."customer_onboarding" to "anon";

grant delete on table "public"."customer_onboarding" to "authenticated";

grant insert on table "public"."customer_onboarding" to "authenticated";

grant references on table "public"."customer_onboarding" to "authenticated";

grant select on table "public"."customer_onboarding" to "authenticated";

grant trigger on table "public"."customer_onboarding" to "authenticated";

grant truncate on table "public"."customer_onboarding" to "authenticated";

grant update on table "public"."customer_onboarding" to "authenticated";

grant delete on table "public"."customer_onboarding" to "service_role";

grant insert on table "public"."customer_onboarding" to "service_role";

grant references on table "public"."customer_onboarding" to "service_role";

grant select on table "public"."customer_onboarding" to "service_role";

grant trigger on table "public"."customer_onboarding" to "service_role";

grant truncate on table "public"."customer_onboarding" to "service_role";

grant update on table "public"."customer_onboarding" to "service_role";


