set check_function_bodies = off;

create table "public"."stats_daily_customer_jobs" (
    "id" uuid primary key default gen_random_uuid(),
    "date" date not null,
    "total_customer_jobs" integer not null default 0,
    "total_customer_jobs_for_today" integer not null default 0,
    "created_at" timestamp with time zone not null default now(),
    "new_customer_jobs" integer not null default 0,
    "approved_customer_jobs" integer not null default 0,
    "declined_customer_jobs" integer not null default 0,
    "applied_customer_jobs" integer not null default 0,
    "satisfied_customer_jobs" integer not null default 0,
    "dissatisfied_customer_jobs" integer not null default 0,
    "deleted_customer_jobs" integer not null default 0,
    "customer_applied_customer_jobs" integer not null default 0,
    "expired_customer_jobs" integer not null default 0
);

CREATE OR REPLACE FUNCTION public.populate_stats_daily_customer_jobs()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
declare
  report_date date := current_date - 1;
  total_customer_jobs int;
  total_customer_jobs_for_today int;
  new_customer_jobs int;
  approved_customer_jobs int;
  declined_customer_jobs int;
  applied_customer_jobs int;
  satisfied_customer_jobs int;
  dissatisfied_customer_jobs int;
  deleted_customer_jobs int;
  customer_applied_customer_jobs int;
  expired_customer_jobs int;
begin
  select count(*) into total_customer_jobs
  from public.customer_jobs;

  select count(*) filter (where created_at::date = report_date) into total_customer_jobs_for_today
  from public.customer_jobs;

  select count(*) filter (where status = 'NEW') into new_customer_jobs
  from public.customer_jobs;

  select count(*) filter (where status = 'APPROVED') into approved_customer_jobs
  from public.customer_jobs;

  select count(*) filter (where status = 'DECLINED') into declined_customer_jobs
  from public.customer_jobs;

  select count(*) filter (where status = 'APPLIED') into applied_customer_jobs
  from public.customer_jobs;

  select count(*) filter (where status = 'SATISFIED') into satisfied_customer_jobs
  from public.customer_jobs;

  select count(*) filter (where status = 'DISSATISFIED') into dissatisfied_customer_jobs
  from public.customer_jobs;

  select count(*) filter (where status = 'DELETED') into deleted_customer_jobs
  from public.customer_jobs;

  select count(*) filter (where status = 'CUSTOMER_APPLIED') into customer_applied_customer_jobs
  from public.customer_jobs;

  select count(*) filter (where status = 'EXPIRED') into expired_customer_jobs
  from public.customer_jobs;

  insert into public.stats_daily_customer_jobs (
    id,
    date,
    total_customer_jobs,
    total_customer_jobs_for_today,
    created_at,
    new_customer_jobs,
    approved_customer_jobs,
    declined_customer_jobs,
    applied_customer_jobs,
    satisfied_customer_jobs,
    dissatisfied_customer_jobs,
    deleted_customer_jobs,
    customer_applied_customer_jobs,
    expired_customer_jobs
  )
  values (
    gen_random_uuid(),
    report_date,
    total_customer_jobs,
    total_customer_jobs_for_today,
    now(),
    new_customer_jobs,
    approved_customer_jobs,
    declined_customer_jobs,
    applied_customer_jobs,
    satisfied_customer_jobs,
    dissatisfied_customer_jobs,
    deleted_customer_jobs,
    customer_applied_customer_jobs,
    expired_customer_jobs
  );
end;
$function$
;

CREATE EXTENSION IF NOT EXISTS pg_cron;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM cron.job WHERE jobname = 'daily_stats_customer_job'
  ) THEN
    PERFORM cron.schedule(
      job_name => 'daily_stats_customer_job',
      schedule => '10 0 * * *',
      command => $cron$SELECT public.populate_stats_daily_customer_jobs();$cron$
    );
  END IF;
END
$$;