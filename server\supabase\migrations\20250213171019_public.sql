create table "public"."onboarding_locations" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "onboarding_id" bigint,
    "location" text,
    "work_authorized" boolean,
    "pay_requirement" text
);


alter table "public"."onboarding_locations" enable row level security;

CREATE UNIQUE INDEX onboarding_locations_pkey ON public.onboarding_locations USING btree (id);

alter table "public"."onboarding_locations" add constraint "onboarding_locations_pkey" PRIMARY KEY using index "onboarding_locations_pkey";

alter table "public"."onboarding_locations" add constraint "onboarding_locations_onboarding_id_fkey" FOREIGN KEY (onboarding_id) REFERENCES customer_onboarding(id) not valid;

alter table "public"."onboarding_locations" validate constraint "onboarding_locations_onboarding_id_fkey";

grant delete on table "public"."onboarding_locations" to "anon";

grant insert on table "public"."onboarding_locations" to "anon";

grant references on table "public"."onboarding_locations" to "anon";

grant select on table "public"."onboarding_locations" to "anon";

grant trigger on table "public"."onboarding_locations" to "anon";

grant truncate on table "public"."onboarding_locations" to "anon";

grant update on table "public"."onboarding_locations" to "anon";

grant delete on table "public"."onboarding_locations" to "authenticated";

grant insert on table "public"."onboarding_locations" to "authenticated";

grant references on table "public"."onboarding_locations" to "authenticated";

grant select on table "public"."onboarding_locations" to "authenticated";

grant trigger on table "public"."onboarding_locations" to "authenticated";

grant truncate on table "public"."onboarding_locations" to "authenticated";

grant update on table "public"."onboarding_locations" to "authenticated";

grant delete on table "public"."onboarding_locations" to "service_role";

grant insert on table "public"."onboarding_locations" to "service_role";

grant references on table "public"."onboarding_locations" to "service_role";

grant select on table "public"."onboarding_locations" to "service_role";

grant trigger on table "public"."onboarding_locations" to "service_role";

grant truncate on table "public"."onboarding_locations" to "service_role";

grant update on table "public"."onboarding_locations" to "service_role";


