-- production products
INSERT INTO "public"."products" 
("id", "created_at", "product_code", "label", "price_usd_pennies", "payment_link", "work_promised", "status") 
VALUES 
('1', '2025-01-11 08:29:09.545569+00', 'APPS_20', '20 Applications', '4000', 'https://buy.stripe.com/cN2dRg7L4c3I92w9AG', 20, 'ACTIVE'), 
('2', '2025-01-11 08:51:07.87738+00', 'APPS_50', '50 Applications', '9000', 'https://buy.stripe.com/14k4gGc1k1p45Qk9AF', 50, 'ACTIVE'), 
('3', '2025-01-11 08:52:01.293352+00', 'APPS_100', '100 Applications', '17000', 'https://buy.stripe.com/eVa4gG4ySffUceI8wz', 100, 'ACTIVE'), 
('4', '2025-01-11 08:52:42.138755+00', 'APPS_500', '500 Applications', '60000', 'https://buy.stripe.com/14keVk2qK3xc92w004', 500, 'ACTIVE'), 
('5', '2025-01-11 08:53:59.372257+00', 'NETWORK_80', 'Executive Assistant part-time 1 mo', '34900', 'https://buy.stripe.com/bIYaF41mG1p46UocMT', 80, 'ACTIVE'),
('6', '2025-05-13 08:53:59.372257+00', 'NETWORK_20', '20 Networking Contacts', '1000', 'https://buy.stripe.com/5kA4gG8P8ffUceIbIR', 20,'ACTIVE'),
('7', '2025-01-11 08:53:59.372257+00', 'APPS_TRIAL_5', 'Free Trial 5 Applications', '0000', '', 5, 'INACTIVE');

-- test products
INSERT INTO "public"."products" 
("id", "created_at", "product_code", "label", "price_usd_pennies", "payment_link", "work_promised", "status") 
VALUES 
('20', '2025-01-11 08:29:09.545569+00', 'APPS_20', 'Test Job Apps', '4000', 'https://buy.stripe.com/test_cN24gkgBu19o8xi8ww', 20, 'ACTIVE'), 
('21', '2025-01-11 08:51:07.87738+00', 'NETWORK_20', 'Test Networking', '9000', 'https://buy.stripe.com/test_00g28c4SM9FU3cYcMN', 80, 'ACTIVE'), 
('22', '2025-01-11 08:53:59.372257+00', 'APPS_TRIAL_5', 'Test Free Trial 5 Applications', '0000', '', 5, 'INACTIVE');