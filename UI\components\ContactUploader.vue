<template>
  <div>

    <UPopover>

      <UButton
        color="blue"
        size="lg"
        icon="la:plus-circle"
        trailing-icon="la:plus-circle"
        label="Bulk Contacts"
        />

        <template #panel>
          <div class="p-6 max-w-lg">
            <p>
              Bulk upload contacts from .CSV in the format:
            </p>
            <p>
              "First Name","Last Name","Title", "Company", "Email","Contact Linkedin Url","Corporate Phone"
            </p>
            <p>
              Having additional columns is ok.  .
            </p>
            
            <UInput
              type="file"
              class="py-6"
              accept=".csv"
              @change="setFile"
              />
          
            <UButton
              label="upload contacts"
              color="blue"
              :loading="isLoading"
              class=""
              required
              @click="uploadContacts"
              />

            <div v-if="errorMessage" class="text-red-500">
              {{ errorMessage }}
            </div>
            <div v-if="successMessage" class="text-green-500">
              {{ successMessage }}
            </div>
          </div>
        </template>

    </UPopover>
    
  </div>
</template>

<script lang="ts" setup>
  import Papa from 'papaparse';
  import type { Database } from '~/lib/applysquad-common/database.types';

  const isLoading = ref(false);
  const errorMessage = ref("");
  const successMessage = ref<string[]>();
  const supabase = useSupabaseClient<Database>();
  const props = defineProps<{
    customerId: string,
    agentId: string,
    refreshContacts: () => void
  }>();
  const fileContents = ref<string>();
  type Contact = {
    "First Name": string,
    "Last Name": string,
    Title: string,
    Company: string,
    Email: string,
    "Person Linkedin Url": string,
    "Corporate Phone": string,
    "Company Phone": string,
  }

  const setFile = async (fileList: FileList) => {
    fileContents.value = await fileList.item(0)!.text();
  }

  const uploadContacts = async () => {

    errorMessage.value = "";
    successMessage.value = [];
    isLoading.value = true;

    if (!fileContents.value) {
      errorMessage.value = "Please select a file";
      isLoading.value = false;
      return;
    }

    const { data: contactData } = Papa.parse<Contact>(fileContents.value!, {
      delimiter: ',',
      dynamicTyping: true,
      header: true,
      skipEmptyLines: true,
    });

    let contactCount = 0;
    const upsertPayload = contactData.map( (contact) => (
      contactCount++,
      {
        name: `${contact['First Name']} ${contact['Last Name']}`,
        company: contact.Company,
        title: contact.Title,
        phone: contact['Corporate Phone'] ?? contact['Company Phone'],
        email: contact.Email,
        linkedin_url: contact['Person Linkedin Url']
      }
    ));
    successMessage.value.push(`attempting to upload ${contactCount} Contacts`);

    const { data: upsertData, error: upsertErrors } = await supabase
      .from("contacts")
      .upsert(upsertPayload, { onConflict: "email", ignoreDuplicates: false })
      .select();
    if (upsertErrors) {
      errorMessage.value = upsertErrors.message;
      isLoading.value = false;
      return;
    }
    console.log("upsert select has ", upsertData.length);

    const customerContactPayload = upsertData?.map((data) => (
      {
        customer_id: props.customerId,
        src_id: props.agentId,
        contact_id: data.id
      }
    ));
    const { data: upsertCustomerData, error: upsertCustomerErrors } = await supabase
      .from("customer_contacts")
      .upsert(customerContactPayload, { onConflict: "contact_id, customer_id", ignoreDuplicates: true })
      .select();
    if (upsertCustomerErrors) {
      errorMessage.value = upsertCustomerErrors.message;
      isLoading.value = false;
      return;
    }
    successMessage.value.push(`${upsertCustomerData.length} Contacts added successfully`);
    isLoading.value = false;
    props.refreshContacts();
  }


</script>

<style>

</style>