
create table "public"."job_boards" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default now(),
    "key" text not null,
    "name" text not null,
    "url" text not null
);


alter table "public"."job_boards" enable row level security;

alter table "public"."customers" add column "auto_search" boolean default false;

CREATE UNIQUE INDEX job_boards_pkey ON public.job_boards USING btree (id);

alter table "public"."job_boards" add constraint "job_boards_pkey" PRIMARY KEY using index "job_boards_pkey";

grant delete on table "public"."job_boards" to "authenticated";

grant insert on table "public"."job_boards" to "authenticated";

grant references on table "public"."job_boards" to "authenticated";

grant select on table "public"."job_boards" to "authenticated";

grant trigger on table "public"."job_boards" to "authenticated";

grant truncate on table "public"."job_boards" to "authenticated";

grant update on table "public"."job_boards" to "authenticated";

grant delete on table "public"."job_boards" to "service_role";

grant insert on table "public"."job_boards" to "service_role";

grant references on table "public"."job_boards" to "service_role";

grant select on table "public"."job_boards" to "service_role";

grant trigger on table "public"."job_boards" to "service_role";

grant truncate on table "public"."job_boards" to "service_role";

grant update on table "public"."job_boards" to "service_role";


