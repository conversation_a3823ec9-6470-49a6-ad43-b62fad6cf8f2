import { createStopwatch } from "jsr:@117/stopwatch";
import { generateText } from "npm:ai";
import * as AI from "../lib/ai.ts"
import { supabaseClient } from '../shared/common.ts';
import { PROMPTS } from '../lib/system-prompts.ts';
import { SupabaseClient } from 'jsr:@supabase/supabase-js@2';
import { Database } from '../lib/database.types.ts';

const aiModel = AI.MODELS.GPT4oMINI;

console.log("Job Created Event Handler booting");

export function startDeno() {
  // just used by tests to start Deno.serve()
}

interface Job {
  id: string,
  description: string
}

type Record = {
  id: string;
  description: string;
}

type InsertPayload = {
  record: Record;
}



Deno.serve(async (req: Request) => {

  console.log("handling job created request");

  if (req.method !== "POST") {
    return new Response("Invalid method", { status: 405 });
  }
  
  const supabase = supabaseClient();
  const payload: InsertPayload = JSON.parse(decodeURI(await req.text()) );
  
  await summarizeJobDescription(payload, supabase);


  return new Response("job created event handled - added generated summary")
})

async function summarizeJobDescription(payload: InsertPayload, supabase: SupabaseClient<Database>) {
  // summarize
  const stopwatch = createStopwatch().start();
  const { text } = await generateText({
    model: aiModel,
    system: PROMPTS.job_summary.v1,
    prompt: payload.record.description,
  });
  stopwatch.stop();
  console.log(`OpenAI call took [${stopwatch.getElapsedTime()}]ms`)

  // save summary
  const { error } = await supabase
    .from('jobs')
    .update({summary: text.toString()})
    .eq('id', payload.record.id);
  if (error) {
    throw error;
  }
  console.log(`updated job ${payload.record.id} with summary ${text}`)
}