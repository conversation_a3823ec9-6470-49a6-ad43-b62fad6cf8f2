
import { startDeno } from "../customer-job-created/index.ts";
import { load } from 'jsr:@std/dotenv';
import { supabaseClient } from '../shared/common.ts';
import { type Database } from '../lib/database.types.ts';
import { SupabaseClient } from 'jsr:@supabase/supabase-js@2';
import { assertNotEquals } from 'jsr:@std/assert';
import { v4 as _uuid } from 'npm:uuid';
import { createCustomer, post, checkFileExists } from './test-common.ts';


/**
 * This test costs money to run, so it should only be run as needed.
 */
Deno.test("integration test",
  async () => {
    await load({ export:true });
    startDeno()
    const supabase = supabaseClient();

    const customerJobId = 1234567899;
    const jobId = "ddf6696b-8c9f-4390-9335-e53333bb7848";
    const customer = await createCustomer("<EMAIL>", supabase);
    const eventPayload = Deno
      .readTextFileSync("./test-customer-job-created-event.json")
      .replace("@@CUSTOMER_JOB_ID@@", `${customerJobId}`)
      .replace("@@CUSTOMER_ID@@", customer.id)
      .replace("@@JOB_ID@@", jobId);
    await saveJob(customerJobId, jobId, customer.id, supabase);
    await saveCV(customer.id, customerJobId, supabase);
    
    
    await post(eventPayload);
    
    await assertCoverLetterGenerated(customerJobId, supabase);

})


async function saveCV(
  customerId: string, 
  customerJobId: number, 
  supabase: SupabaseClient<Database>) {

  const file = Deno.readFileSync("./resume-john-doe.pdf");
  const documentType = "CV";
  const filename = "cv.pdf";
  const path = `${documentType}/${filename}`;

  // create bucket
  if (!(await checkFileExists(path, customerId, supabase))) {

    // upload file
    const { error: uploadError } = await supabase.storage
            .from(customerId)
            .upload(path, file, { upsert: true });
    if (uploadError) {
      throw uploadError;
    }
  }

  if (!(await documentExists(path, customerId, supabase))) {
    // create document
    const { data: document, error: docError } = await supabase
        .from("documents")
        .insert(
          { bucket: customerId, document_type: documentType, file_name: filename,  path: path, customer_id: customerId }
        )
        .select()
        .single();
    if (docError) {
      throw docError;
    }

    const { error: custJobError } = await supabase
      .from("job_documents")
      .insert(
        { document_type: "CV", customer_id: customerId, customer_job_id: customerJobId, document_id: document.id }
      );
    if (custJobError) {
      throw custJobError;
    }
  } else {
    console.log("document exists");
  }
}


async function saveJob(customerJobId: number,
    jobId: string,
    customerId: string,
    supabase: SupabaseClient<Database>) {

  const { error } = await supabase
    .from("jobs")
    .upsert(
      {id: jobId, description: jobDescription, url: jobUrl, location: "paris", employer: jobCompany, title: jobTitle },
      { onConflict: "id", ignoreDuplicates: true }
    );
  if (error) {
    throw error;
  }

  const { error: customerJobError } = await supabase
    .from("customer_jobs")
    .upsert(
      {id: customerJobId, job_id: jobId, customer_id: customerId, cover_letter: null},
      {onConflict: "id", ignoreDuplicates: false}
    );
  if (customerJobError) {
    throw customerJobError;
  }
}

async function assertCoverLetterGenerated(customerJobId: number, supabase: SupabaseClient<Database>) {
  const { data, error } = await supabase
    .from("cover_letters")
    .select("*")
    .eq("customer_job_id", customerJobId)
    .order("created_at");

  if (error) {
    throw error;
  }
  assertNotEquals(data.length, 0, "no cover_letter row created");
  console.log("generated cover", data[0].data);

  assertNotEquals(data[0].data, null);
}

async function documentExists(
    path: string, 
    customerId: string, 
    supabase: SupabaseClient<Database>) {

  const {data, error} = await supabase
      .from("documents")
      .select("*")
      .eq("customer_id", customerId)
      .eq("path", path);
  if (error) {
    console.error("error checking for document", error);
    throw error;
  }
  console.log("documents for customer", data);
  return data.length > 0;
}

const jobTitle = "Product Manager - Marketplace";
const jobCompany = "Curri";
const jobUrl = "https://www.linkedin.com/jobs/view/4068123693/";
const jobDescription = `About the job
What's the role?

Do you like building great products that help customers solve real-world problems? Do you enjoy collaborating with great people to turn it into a reality? Do you want responsibility and ownership beyond just shipping random features that your boss tells you need to be out by the end of the quarter?

Great! You might be a fit for our growing Product team at Curri.

We are looking for a Product Manager to help grow our marketplace here at Curri. Construction and industrial distributors use the Curri marketplace nationwide to deliver material to their customers. You will work closely with our customers to deeply understand their needs and innovate on their behalf to create amazing delivery experiences.

Join us in making "Deliver with Curri" the default option for construction and industrial supplies.

What will you do:

Understand our customers' needs better than they do and ensure that Curri's marketplace provides solutions that solve their business needs.
Collaborate with our sales, operations, product, and engineering teams to define and build cohesive, intuitive, and quality experiences for our customers.
Define and gather requirements and create shaping docs that ensure we build software that solves our customers’ problems.
Work closely with the go-to-market teams to ensure that the Curri marketplace meets our customers’ needs and is effectively presented to them.
Engage with leadership to participate in prioritizing software engineering work.
Prepare and present regular reports on the progress of initiatives to key stakeholders, including executive leadership.

What you will need to have:

A track record: 2+ years of experience doing software product management and a demonstrable ability to work with product stakeholders to bring ideas to market
A Customer First mentality: You prioritize engaging with customers to understand and meet customer needs.
Execution skills: A bias towards action and a record of working with engineering teams to ship software quickly and iteratively.
Problem-solving skills: The ability to understand complex problems and explain them clearly.
Prioritization: Working on and prioritizing multiple projects, understanding their connection, and making trade-offs when necessary.

What will make you stand out:

Product Chops: You excel at the nuts and bolts of product development, from user research to shaping, execution, and measuring success.
Clarity and Communication: Demonstrate that you can think clearly and communicate your thoughts. There are many moving parts at Curri, and your ability to understand and communicate these to others is vital to your success.
Collaboration: This role will involve working closely with several different teams, both internally and externally, and you’ll need to be able to communicate with a wide variety of people effectively.
Growth: A desire to push yourself and hone your craft. This scope of work will grow fast, and you’ll need to be in continuous learning mode.

What is in it for you?

You will have the opportunity to work for a dynamic and successful start-up on a diverse team where you can make a huge impact by doing meaningful work.
Significant and meaningful responsibilities from Day 1. The possibilities are limitless and depend on you.
Work in an environment with a flexible schedule. We don’t micromanage and want to help you do great work.
There is no work/life—there is only life, and we want your time at Curri to be life-giving and foster the best version of you. We care about family and your own personal development and don't expect you to always be engaged with work.
We offer a competitive salary, and benefits including, but not limited to, health, dental, vision, 401K, and an equity compensation grant.`;