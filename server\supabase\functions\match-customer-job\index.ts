import { isValidQstashRequest } from '../shared/common.ts';


console.log("Match Customer Job Event Handler booting");

export function startDeno() {
  // just used by tests to start Deno.serve()
}

Deno.serve(async (req: Request) => {

  const body = await req.text();

  if (await isValidQstashRequest(req.headers, body)) {

    console.log("handling match customer job request, ", body);

    return new Response("matched customer to job")

  } else {
    console.warn("not a valid requestion from Qstash, ignoring");
    return new Response(":p");
  }

})