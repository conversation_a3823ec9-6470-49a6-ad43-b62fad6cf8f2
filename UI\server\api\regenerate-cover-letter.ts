import type { CustomerInfo} from "~/lib/applysquad-common/cover-letter-service";
import { generateCoverLetter } from "~/lib/applysquad-common/cover-letter-service"
import { serverSupabaseClient } from '#supabase/server';
import type { Database } from "~/lib/applysquad-common/database.types";
import type { SupabaseClient } from "@supabase/supabase-js";
export default defineEventHandler(async (event) => {

  console.log("generating cover letter");

  const supabase = await serverSupabaseClient<Database>(event);
  const cvId = getQuery(event).cvId;
  const jobId = getQuery(event).jobId;
  const customerJobId = getQuery(event).customerJobId;

  if (!cvId || !jobId || !customerJobId) {
    setResponseStatus(event, 400);
    return "BAD_REQUEST";
  }

  const { data: jobData, error: jobError } = await supabase
  .from("customer_jobs")
  .select("*, job:jobs(*)")
  .eq("id", Number(customerJobId!.toString()))
  .eq("job.id", jobId!.toString())
  .single();
  if (jobError) {
    throw jobError;
  }

  const customerInfo = await buildCustInfo(jobData.customer_id, cvId!.toString(), supabase);
  const jobInfo = {
    jobDescription: jobData.job.description,
    jobTitle: jobData.job.title,
    company: jobData.job.employer
  }

  const coverLetter = await generateCoverLetter(customerInfo, jobInfo)

  await saveCoverLetter(coverLetter, Number(customerJobId!.valueOf()), supabase);

  setResponseStatus(event, 200);
  return "OK";
})

async function buildCustInfo(customerId:string, cvId: string, supabase: SupabaseClient<Database>): Promise<CustomerInfo> {
  const { data: customerData, error: customerError } = await supabase
    .from("customer_onboarding")
    .select("*")
    .eq("customer_id", customerId)
    .single();
  if (customerError) {
    console.log("customer has no onboarding", customerError);
  }
  const cv = await getCV(cvId, supabase);

  return {
    cvs: [cv],
    functionalPreference: customerData?.functional_preferences?.join(",")
  };
}


async function getCV(cvId: string, supabase: SupabaseClient<Database>): Promise<Blob> {

  const { data: document, error: documentError } = await supabase
    .from("documents")
    .select("*")
    .eq("id", cvId!.toString())
    .single();
  if (documentError) {
    throw documentError;
  }
  const cvs = await fetchDocument(document.bucket, document.path, supabase);
  console.log("cvs text", cvs);

  return cvs;
}

async function fetchDocument(bucket: string, path: string, supabase: SupabaseClient): Promise<Blob> {
  const { data, error } = await supabase.storage
    .from(bucket)
    .download(path);
  if (error) {
    throw error;
  }
  return data;
}

async function saveCoverLetter(coverLetter: string, customerJobId: number, supabase: SupabaseClient<Database>) {

  const { error: saveError } = await supabase
    .from("cover_letters")
    .insert({ customer_job_id: customerJobId, data: coverLetter });
  if (saveError) {
    throw saveError;
  }

}