<template>
  <JobPlanSummary :is-customer="props.isCustomer">
    <template #header="{ plan, customer }">
      <div>
        <div v-if="props.isCustomer">Your plan:</div>
        <div v-else>{{ customer?.name }}'s plan:</div>
        <div>{{ plan.product.label }}</div>
        <UDivider class="py-2" />
      </div>
    </template>
    <template #summary="{ summary, plan }">
      <table class="table-auto">
        <tbody>
          <tr>
            <td>Approved:</td>
            <td class="text-right">{{ summary.approved }}</td>
          </tr> 
          <tr>
            <td>Applied:</td>
            <td class="text-right">{{ summary.applied }}</td>
          </tr>
          <tr>
            <td class="text-nowrap">Awaiting Approval:</td>
            <td class="text-right">{{ summary.awaitingApproval }}</td>
          </tr>
          <tr>
            <td title="Linkedin Easy Apply">Linkedin EA:</td>
            <td title="Linkedin Easy Apply" class="text-right">{{ summary.customerApply }}</td>
          </tr>
          <tr>
            <td>Declined:</td>
            <td class="text-right">{{ summary.declined }}</td>
          </tr>
          <tr>
            <td class="font-bold">Outstanding:</td>
            <td class="font-bold text-right">
              {{
                plan.product.work -
                (summary.applied + summary.awaitingApproval)
              }}
            </td>
          </tr>
        </tbody>
      </table>
    </template>
    <template #no-plan="{ isCustomer }">
      <div v-if="isCustomer">
        <ULink class="text-orange-500 hover:text-orange-600" to="/purchase"
          >Upgrade Now!</ULink
        >
      </div>
    </template>
  </JobPlanSummary>
</template>

<script lang="ts" setup>
import JobPlanSummary from "./JobPlanSummary.vue";
import { defineProps } from "vue";

const props = defineProps({ isCustomer: Boolean });
</script>
