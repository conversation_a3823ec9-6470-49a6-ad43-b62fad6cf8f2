create type "public"."contact_status" as enum ('NEW', 'APPROVED', 'DECLINED', 'CONTACTED');

create table "public"."contacts" (
    "created_at" timestamp with time zone not null default now(),
    "name" text not null,
    "company" text,
    "title" text,
    "phone" text,
    "email" text,
    "linkedin_url" text,
    "description" text,
    "id" uuid not null default gen_random_uuid()
);


alter table "public"."contacts" enable row level security;

create table "public"."customer_contacts" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "customer_id" uuid not null default gen_random_uuid(),
    "message" text,
    "hours" real,
    "status" contact_status not null default 'NEW'::contact_status,
    "customer_notes" text,
    "agent_notes" text,
    "contact_id" uuid default gen_random_uuid(),
    "src_id" text not null
);


alter table "public"."customer_contacts" enable row level security;

CREATE UNIQUE INDEX contacts_pkey ON public.contacts USING btree (id);

CREATE UNIQUE INDEX customer_contacts_pkey ON public.customer_contacts USING btree (id);

alter table "public"."contacts" add constraint "contacts_pkey" PRIMARY KEY using index "contacts_pkey";

alter table "public"."customer_contacts" add constraint "customer_contacts_pkey" PRIMARY KEY using index "customer_contacts_pkey";

alter table "public"."customer_contacts" add constraint "customer_contacts_contact_id_fkey" FOREIGN KEY (contact_id) REFERENCES contacts(id) not valid;

alter table "public"."customer_contacts" validate constraint "customer_contacts_contact_id_fkey";

alter table "public"."customer_contacts" add constraint "customer_contacts_customer_id_fkey" FOREIGN KEY (customer_id) REFERENCES customers(id) not valid;

alter table "public"."customer_contacts" validate constraint "customer_contacts_customer_id_fkey";

grant delete on table "public"."contacts" to "authenticated";

grant insert on table "public"."contacts" to "authenticated";

grant references on table "public"."contacts" to "authenticated";

grant select on table "public"."contacts" to "authenticated";

grant trigger on table "public"."contacts" to "authenticated";

grant truncate on table "public"."contacts" to "authenticated";

grant update on table "public"."contacts" to "authenticated";

grant delete on table "public"."contacts" to "service_role";

grant insert on table "public"."contacts" to "service_role";

grant references on table "public"."contacts" to "service_role";

grant select on table "public"."contacts" to "service_role";

grant trigger on table "public"."contacts" to "service_role";

grant truncate on table "public"."contacts" to "service_role";

grant update on table "public"."contacts" to "service_role";

grant delete on table "public"."customer_contacts" to "authenticated";

grant insert on table "public"."customer_contacts" to "authenticated";

grant references on table "public"."customer_contacts" to "authenticated";

grant select on table "public"."customer_contacts" to "authenticated";

grant trigger on table "public"."customer_contacts" to "authenticated";

grant truncate on table "public"."customer_contacts" to "authenticated";

grant update on table "public"."customer_contacts" to "authenticated";

grant delete on table "public"."customer_contacts" to "service_role";

grant insert on table "public"."customer_contacts" to "service_role";

grant references on table "public"."customer_contacts" to "service_role";

grant select on table "public"."customer_contacts" to "service_role";

grant trigger on table "public"."customer_contacts" to "service_role";

grant truncate on table "public"."customer_contacts" to "service_role";

grant update on table "public"."customer_contacts" to "service_role";

create policy "Enable all for authenticated users only"
on "public"."contacts"
as permissive
for all
to authenticated
using (true)
with check (true);


create policy "Enable all for authenticated users only"
on "public"."customer_contacts"
as permissive
for all
to authenticated
using (true)
with check (true);



