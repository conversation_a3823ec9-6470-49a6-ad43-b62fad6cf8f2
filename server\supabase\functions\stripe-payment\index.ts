import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { supabaseClient, PRODUCT_CODE, getEnumKey, handleError, template, Postmark, LivePostmark } from '../shared/common.ts'
import { type Database } from '../lib/database.types.ts'
import { SupabaseClient } from 'jsr:@supabase/supabase-js@2';
import { TEMPLATES } from '../shared/common.ts';

console.log("Stripe-payment function booting")


export function startDeno() {
  // just used by tests to start Deno.serve()
}



class PaymentDto {
  constructor(
    public id: number,
    public name: string,
    public email: string,
    public amount_in_pennies: number,
    public event_id: string,
    public product: PRODUCT_CODE
  ) {}
  
}

/**
 * Parse a raw Stripe event payload into a Payment object.
 * 
 * Supabase typescript client does not support transactions,
 * so processing the Stripe event should be idempotent. A
 * failure of one step should allow the event to be retried,
 * without negative side effects, like duplicate data.
 * 
 */
Deno.serve(async (req) => {
  console.log("executing stripe-payment function")
  if (req.method!== "POST") {
    return new Response("Invalid method", { status: 405 })
  }

  const body = await req.text()

  const payment = parseRawEvent(body)

  if (payment) {
    console.log(`processing stripe payment event: ${payment.event_id}`);

    const supabase = supabaseClient();
    await upsertPayment(payment, supabase);
    await upsertPlan(payment, supabase)
    console.log(`Stripe-Payment processed successfully for ${payment.email}`)
    await notifyAdmin(payment, supabase);

    return new Response(
      `thank you for your payment ${payment.email}`,
      { headers: { "Content-Type": "text/plain" } },
    )
  } else {
    return new Response('cannot handle this event type')
  }
})


/**
 * Parse the raw Stripe event payload into a Payment object.
 * @param rawEvent 
 * @returns 
 */
export function parseRawEvent(rawEvent: string): PaymentDto|undefined {
  console.log("parsing raw event: ", rawEvent);
  const parsedEvent = JSON.parse(rawEvent);

  // need to check this at some point!
  if (parsedEvent.type !== 'checkout.session.completed') {
    console.error(`Invalid event type: ${parsedEvent.type}`);
    return undefined;
  }


  const event_id = parsedEvent.id;
  const name = parsedEvent.data.object.customer_details.name;
  const email = parsedEvent.data.object.customer_details.email;
  
  const product = parsedEvent.data.object.metadata.PRODUCT_CODE;

  let amount_in_pennies = 0;
  if (parsedEvent.data.object.currency == 'usd') {
    amount_in_pennies = parsedEvent.data.object.amount_total;
  } else if (parsedEvent.data.object.currency_conversion != null) {
    if (parsedEvent.data.object.currency_conversion.source_currency != "usd") {
      console.error("source currency was not USD! - misconfiuration likely");
    }
    amount_in_pennies = parsedEvent.data.object.currency_conversion.amount_total;
  } else {
    const errorMessage = "could not determine USD payment amount"
    console.error(errorMessage);
    throw Error(errorMessage);
  }

  return new PaymentDto(
    0, 
    name, 
    email, 
    amount_in_pennies, 
    event_id, 
    getEnumKey(PRODUCT_CODE, product));
}


/**
 * Upsert a Payment record.
 * 
 * @param payment 
 * @param supabase 
 */
async function upsertPayment(payment: PaymentDto, supabase: SupabaseClient<Database>) {  
   
   const {error} = await supabase
    .from("payments")
    .upsert({ 
      event_id: payment.event_id,
      name: payment.name,
      email: payment.email, 
      amount_pennies: payment.amount_in_pennies,
      product_code: payment.product
    }, { 
      onConflict: 'event_id',
      ignoreDuplicates: true })

    handleError(error)

    const {data} = await supabase
      .from("payments")
      .select("id")
      .eq("event_id", payment.event_id)

    payment.id = data![0].id

    console.log('Payment upserted successfully:', data);
}

async function upsertPlan(payment: PaymentDto, supabase: SupabaseClient<Database>) {
  
  const {data: product, error: paymentError} = await supabase
      .from("products")
      .select("*")
      .eq("product_code", payment.product)
      .eq("status", "ACTIVE")
      .single()

  if (paymentError) {
    handleError(paymentError)
    throw paymentError
  }

  const {data: customer, error: customerError} = await supabase
      .from("customers")
      .select("id")
      .eq("email", payment.email)
      .single()

  if (customerError) {
   const message = "no customer found for payment, unable to create order: " + customerError.message
   console.error(message)
   throw customerError 
  }

  const {error} = await supabase.from("plans")
    .upsert({
      "customer_id": customer.id,
      "payment_id": payment.id,
      "product_id": product.id,
      "work_promised": product.work_promised,
    }, {
      onConflict: 'payment_id',
      ignoreDuplicates: true
    })
  if (error) {
    throw error
  }

}


async function notifyAdmin(payment: PaymentDto, supabase: SupabaseClient<Database>) {

  const { data, error} = await supabase
    .from('customers')
    .select('*')
    .eq('email', payment.email)

  if (error) {
    handleError(error)
    throw error
  }

  const body = await template(
    TEMPLATES.purchaseAdminNotification, 
    {customer: data[0], payment: payment });
  console.log("body", body)

  await (getPostmark().sendEmail(
    {
      to: Deno.env.get("ADMIN_EMAIL")!,
      subject: "New payment received",
      body: body
    }
  ));

}


// this is to allow the test to set a mock
let postmarkClient: Postmark;
export function setPostmark(client: Postmark) {
  postmarkClient = client;
}
function getPostmark() {
  if (!postmarkClient) {
    postmarkClient = new LivePostmark();
  }
  return postmarkClient;
}
