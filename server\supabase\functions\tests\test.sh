#!/usr/bin/bash

deno test test-theirstack.ts --allow-read --allow-net --allow-env --trace-leaks 
#deno test test-create-search-criteria.ts --allow-read --allow-net --allow-env --trace-leaks 
 #deno test test-stripe-payment.ts test-nylas-expired.ts test-customer-created.ts --allow-read --allow-net --allow-env --trace-leaks 
#deno test test-customer-created.ts  --allow-read --allow-net --allow-env --trace-leaks 
# deno test test-stripe-payment.ts --allow-read --allow-net --allow-env --trace-leaks


# costs $$ to run, so only run as needed
# deno test test-job-created.ts  --allow-read --allow-net --allow-env --trace-leaks 
#deno test test-customer-job-created.ts  --allow-read --allow-net --allow-env --trace-leaks 

# TheirStack Integration Test 
# deno test test-search-job-board.ts --allow-read --allow-net --allow-env --trace-leaks
read -p "Press Enter to exit..."
