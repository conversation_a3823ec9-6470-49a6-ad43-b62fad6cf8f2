name: GitHub Dev Server Actions 
run-name: ${{ github.actor }} is deploying dev server  
on:
  push:
    branches:
      - dev
env:
  SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN_STAGE }}
  SUPABASE_DB_PASSWORD: ${{ secrets.SUPABASE_DB_PASSWORD_STAGE }}
  PROJECT_ID: ${{ secrets.SUPABASE_PROJECT_STAGE }} 
jobs:
  code-quality:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: denoland/setup-deno@v2
      - run: deno lint
        working-directory: ./server

  deploy-supabase-functions:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: supabase/setup-cli@v1
        with:
          version: latest
      - run: supabase init --force
        working-directory: ./server
      - run: supabase link --project-ref $PROJECT_ID
        working-directory: ./server
      - run: supabase functions deploy stripe-payment --no-verify-jwt
        working-directory: ./server
      - run: supabase functions deploy nylas-webhook --no-verify-jwt
        working-directory: ./server
      - run: supabase functions deploy job-created
        working-directory: ./server
      - run: supabase functions deploy customer-created
        working-directory: ./server
      - run: supabase functions deploy customer-job-created
        working-directory: ./server
      - run: supabase functions deploy create-search-criteria 
        working-directory: ./server
      - run: supabase functions deploy search-job-board --no-verify-jwt
        working-directory: ./server
      - run: supabase functions deploy match-customer-job --no-verify-jwt
        working-directory: ./server
  deploy-supabase-migration:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: supabase/setup-cli@v1
        with:
          version: latest
      - run: supabase init --force
        working-directory: ./server
      - run: supabase link --project-ref $PROJECT_ID
        working-directory: ./server
      - run: supabase db push
        working-directory: ./server
