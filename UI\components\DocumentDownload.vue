<template>
  <div v-if="document?.id != ''">
    <UIcon 
        name="la:arrow-circle-down" 
        class="hover:cursor-pointer text-orange-500 w-8 h-8" 
        :title="`download ${document?.name}`"
        @click="downloadDocument()" />
  </div>
</template>

<script lang="ts" setup>
import type { Document } from '~/common/common'

const toast = useToast()
const supabase = useSupabaseClient()

const document = defineModel<Document>('document')

async function downloadDocument() {
  if (document.value) {
    toast.add({ description: 'Downloading document...' + document.value.name, timeout: 3000 })
    const {data, error} = await supabase.storage
      .from(document.value.bucket)
      .createSignedUrl(document.value.path, 60, { download: true })
    if (error) { throw error }
    window.open(data.signedUrl, "_blank")
  }
}

</script>