set check_function_bodies = off;

create table "public"."stats_daily_plans" (
    "id" uuid primary key default gen_random_uuid(),
    "date" date not null,
    "total_plans" integer not null default 0,
    "open_plans" integer not null default 0,
    "complete_plans" integer not null default 0,
    "created_at" timestamp with time zone not null default now()
);


CREATE OR REPLACE FUNCTION public.populate_stats_daily_plans()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  report_date date := current_date - 1;
  total_plans int;
  open_plans int;
  complete_plans int;
BEGIN
  SELECT count(*) INTO total_plans
  FROM public.plans;

  SELECT count(*) INTO open_plans
  FROM public.plans
  WHERE status = 'OPEN';

  SELECT count(*) INTO complete_plans
  FROM public.plans
  WHERE status = 'COMPLETE';

  INSERT INTO public.stats_daily_plans (
    id,
    date,
    total_plans,
    open_plans,
    complete_plans,
    created_at
  ) VALUES (
    gen_random_uuid(),
    report_date,
    total_plans,
    open_plans,
    complete_plans,
    now()
  );
END;
$function$
;

CREATE EXTENSION IF NOT EXISTS pg_cron;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM cron.job WHERE jobname = 'daily_stats_plans'
  ) THEN
    PERFORM cron.schedule(
      job_name := 'daily_stats_plans',
      schedule := '20 0 * * *',
      command := 'SELECT public.populate_stats_daily_plans();'
    );
  END IF;
END
$$;