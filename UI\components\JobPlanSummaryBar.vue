<template>
  <JobPlanSummary v-model:customer-jobs="customerJobs" :is-customer="true">
    <template #statistics="{ statistics }">
      <!-- Optionally display statistics details -->
      <div class="flex flex-col w-full md:flex-row md:flex-wrap gap-2">
        <UButton
          v-for="(stat, index) in statistics"
          :key="index"
          variant="soft"
          color="gray"
          class="flex flex-col w-36 px-0 border-gray-300 border"
        >
          <div class="text-sm">{{ stat.label }}</div>
          <div class="text-lg text-orange-400">{{ stat.number }}</div>
        </UButton>
      </div>
    </template>
    <template #no-plan>
      <div>
        <ULink class="text-orange-500 hover:text-orange-600" to="/purchase">
          Upgrade Now!
        </ULink>
      </div>
    </template>
  </JobPlanSummary>
</template>

<script lang="ts" setup>
import JobPlanSummary from "./JobPlanSummary.vue";
import type { CustomerJob } from "~/common/common";

const customerJobs = defineModel<CustomerJob[]>("customerJobs");
</script>
