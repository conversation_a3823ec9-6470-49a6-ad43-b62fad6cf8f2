<template>
  <div>
    <!-- Hero Section -->
    <section
      class="hero bg-gray-100 text-orange-600 text-center py-10 sm:py-32 px-4"
    >
      <p class="mt-6 text-xl sm:text-3xl max-w-3xl mx-auto ">
        Outsource your job application hassle!
      </p>
      <p class="mt-3 sm:mt-6 text-2xl sm:text-4xl max-w-3xl mx-auto">
        Get high-quality, tailored applications that help you stand out!
      </p>
      <UButton
        type="button"
        to="/signup"
        class="mt-8 inline-block px-8 py-3 text-white font-semibold rounded-md shadow-lg button-bg-gradient"
      >
        Sign Up!
      </UButton>
    </section>

    <!-- Features Section -->
    <section
      class="features py-16 bg-gradient-to-r from-red-500 to-orange-400 text-center px-4"
    >
      <h2 class="text-2xl sm:text-4xl font-bold text-white">
        Why Choose Applysquad?
      </h2>
      <div
        class="mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto place-items-center"
      >
        <div
          v-for="(item, index) in cardsData"
          :key="index"
          class="feature-card w-full h-full bg-white border border-orange-300 shadow-lg p-6 rounded-md transform transition hover:scale-105 cursor-pointer"
        >
          <p class="mb-2 text-lg text-orange-600">
            {{ item }}
          </p>
        </div>
      </div>
    </section>

    <div class="flex flex-col justify-center items-center p-5 bg-gray-100 shadow-none">
      <UCard class="block mt-10 w-[600px] p-12 md:p-0 my-auto">
        <div className="relative cursor-pointer h-80 sm:h-[22rem] w-full">
          <iframe
            className="relative z-10"
            width="100%"
            height="100%"
            :src="`https://www.youtube.com/embed/${videoId}`"
            title="YouTube video player"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          />
        </div>
      </UCard>
    </div>

    <!-- Contact Section -->
    <section
      class="contact py-6 sm:py-16 bg-gradient-to-r from-red-500 to-orange-400 text-white text-center"
    >
      <h2 class="text-2xl sm:text-4xl font-bold">Get in Touch</h2>
      <p class="mt-4 sm:text-lg">
        Email us at:
        <a
          href="mailto:<EMAIL>"
          class="underline hover:text-gray-300"
          ><EMAIL></a
        >
      </p>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

const cardsData = ref([
  "We filter jobs after discussing your profile, experience and preferences",
  "We create new employer accounts on your behalf, wherever required",
  "We create personalized cover letters, and submit applications for you",
  "We give you a dashboard to track your job applications",
]);

const videoId = "Z9Of_QFELtA";
</script>

<style scoped></style>
