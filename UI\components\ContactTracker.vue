<template>
  <div>
    <UAccordion ref="accordionRef" :items="customerContactSections" multiple>

      <template #default="{ item, open }">
        <UButton
          color="gray"
          variant="soft"
          class="border border-gray-300 text-slate-700 mb-3"
          :ui="{ rounded: 'rounded-lg', padding: { sm: 'p-3' } }"
        >
          <slot name="leading"/>

          <span>
            {{ item.contact.name }} @ {{ item.contact.company }} ({{
              $t(`contact.status.${item.contact.contactStatus}`)
            }})
          </span>
          <UIcon
            v-if="item.requiresAction"
            name="la:exclamation-triangle"
            class="text-yellow-500 w-6 h-6"
          />

          <template #trailing>
            <UIcon
              name="la:chevron-down"
              class="w-5 h-5 ms-auto transform transition-transform duration-200"
              :class="[open && 'rotate-180']"
            />
          </template>
        </UButton>
      </template>

      <template #item="{ item, open }">
        <div class="p-2 text-md border rounded-lg text-slate-700">

          <UDivider label="Contact Details" :ui="{ label: 'text-base' }" />

          <UContainer class="flex flex-col md:flex-row pt-4 space-y-3">
            <!-- general job info -->
            <table class="table-auto md:w-2/5 text-sm">
              <tbody class="flex flex-col space-y-3">
                <tr>
                  <td class="w-32 pr-3">Name:</td>
                  <td>{{ item.contact.name }}</td>
                </tr>
                <tr>
                  <td class="w-32 pr-3">Company:</td>
                  <td>{{ item.contact.company }}</td>
                </tr>
                <tr>
                  <td class="w-32 pr-3">Title:</td>
                  <td class="">{{ item.contact.title }}</td>
                </tr>
                <tr>
                  <td class="w-32 pr-3">Linkedin Profile:</td>
                  <td class="">
                    <ULink 
                        :to="item.contact.linkedinUrl" 
                        class="text-orange-500 hover:text-orange-600"
                        target="_blank" >
                      View Linkedin Profile
                    </ULink>
                  </td>
                </tr>
                <tr>
                  <td class="w-32 pr-3">Phone:</td>
                  <td class="">{{ item.contact.phone }}</td>
                </tr>
                <tr>
                  <td class="w-32 pr-3">Email:</td>
                  <td class="">{{ item.contact.email }}</td>
                </tr>
                <tr>
                  <td class="w-32 pr-3">Description:</td>
                  <td class="">{{ item.contact.description }}</td>
                </tr>
                
              </tbody>
            </table>
            
            <table class="table-auto md:w-3/5 text-sm">
              <tbody class="  ">
                <tr class=" ">
                  <td class="">Message to Contact:</td>
                </tr>
                <tr class="">                    
                  <td class="">
                    <UTextarea 
                        v-model="item.contact.message" 
                        :rows="15" 
                        autoresize 
                        resize 
                        class="w-full"
                        placeholder="Enter the message you'd like to send to the contact..."
                    />
                  </td>
                </tr>
              </tbody>
            </table>

          </UContainer>

          <!-- ACTIONS -->
          <UDivider label="Actions" class="py-3" :ui="{ label: 'text-base' }" />
          <UContainer class="w-full">
            <p class="py-2 px-3">
              Next Step: {{ $t(`contact.nextStep.${item.contact.contactStatus}`) }}
            </p>

            <div v-if="props.isCustomer" class="px-3">
              <!-- customer view-->

              <div
                v-if="item.contact.contactStatus === CustomerContactStatus.NEW"
                class="flex flex-col sm:flex-row gap-3"
              >
                <UButton
                  icon="la:check-circle-solid"
                  class="bg-green-500 hover:bg-green-600 text-white"
                  @click="approveContact(item.contact)"
                >
                  Approve This Contact
                </UButton>
                <UButton
                  icon="la:pause-circle-solid"
                  class="bg-red-500 hover:bg-red-600 text-white"
                  @click="declineContact(item.contact)"
                >
                  Decline This Contact
                </UButton>
              </div>

              
            </div>

            <div v-else class="px-3">
              <!-- agent view-->
              <div
                v-if="item.contact.contactStatus === CustomerContactStatus.APPROVED"
                class="py-4 space-y-2"
              >
                <div>
                  1. Send the message to this contact
                </div>

                <div class="">
                  <div class="w-full">
                    2. Ensure the actual contact message used is updated here
                  </div>
                </div>

                <div>
                  3.
                  <UButton @click="markContacted(item.contact.id)">
                    Mark as "contacted"
                  </UButton>
                </div>
                <!-- <div>
                  4. Enter the amount of time you spent messaging this contact:
                  TODO: add hours
                </div> -->
              </div>
            </div>
          </UContainer>

          <!-- NOTES -->
          <UDivider label="Notes" class="py-4" :ui="{ label: 'text-base' }" />
          <UContainer class="flex flex-wrap w-full gap-y-3 pb-3">
            <div class="px-3 w-full sm:w-1/2 sm:pr-2">
              <UTextarea
                v-model="item.contact.customerNotes"
                autoresize
                resize
                padded
                color="gray"
                class="text-slate-700"
                :disabled="!isCustomer"
                :placeholder="item.contact.customerNotes ?? 'Customer Notes ...'"
                @blur="updateCustomerNote(item.contact)"
              />
            </div>

            <div class="px-3 w-full sm:w-1/2 sm:pl-2">
              <UTextarea
                v-model="item.contact.agentNotes"
                resize
                autoresize
                padded
                color="gray"
                class="text-slate-700"
                :disabled="isCustomer"
                :placeholder="item.contact.agentNotes ?? 'Agent Notes...'"
                @blur="updateAgentNote(item.contact)"
              />
            </div>
          </UContainer>
        </div>
      </template>
    </UAccordion>
  </div>
</template>

<script lang="ts" setup>
import type { Database } from "~/lib/applysquad-common/database.types";
import {
  CustomerContactStatus,
  type Agent,
  type Customer,
  type CustomerContact,
} from "../common/common";

interface Props {
  customer: Customer;
  isCustomer: boolean;
  agent?: Agent;
  // callback to parent to refresh the contacts, the state has changed
  refreshContacts: () => {};
}

interface CustomerContactSection {
  label: string;
  contact: CustomerContact;
  requiresAction: boolean;
}

const props: Props = defineProps<Props>();
const customerContacts = defineModel<CustomerContact[]>("customerContacts");

const i18n = useI18n().t;
const supabase = useSupabaseClient<Database>();
const customerContactSections = ref<CustomerContactSection[]>([]);
const toast = useToast();
const toastTimeout = 4000;
const accordionRef = ref<any>(null);

/**
   * Sort order depends on whether this is a customer or agent view. We want
   * actionable contacts to appear first.
   * Customer sort:
   *   - NEW
   *   - CONTACTED
   *   - APPROVED
   *   - DECLINED
   * Agent sort:
   *   - APPROVED
   *   - NEW
   *   - CONTACTED/DECLINED (by create date)
   *
   */
   const customerStatusRank = {
    NEW: 1,
    CONTACTED: 2,
    APPROVED: 3,
    DECLINED: 4,
  };
  const agentStatusRank = {
    APPROVED: 1,
    NEW: 2,
    CONTACTED: 3,
    DECLINED: 3,
  };
  const customerSorter = (
    a: CustomerContactSection,
    b: CustomerContactSection
  ): number => {
    if (customerStatusRank[a.contact.contactStatus] < customerStatusRank[b.contact.contactStatus]) {
      return -1;
    } else if (
      customerStatusRank[a.contact.contactStatus] > customerStatusRank[b.contact.contactStatus]
    ) {
      return 1;
    } else {
      /* if (customerStatusRank[a.job.status] === customerStatusRank[b.job.status]) { */
      return 0; // TODO: date comparision
    }
  };
  const agentSorter = (
    a: CustomerContactSection,
    b: CustomerContactSection
  ): number => {
    if (agentStatusRank[a.contact.contactStatus] < agentStatusRank[b.contact.contactStatus]) {
      return -1;
    } else if (agentStatusRank[a.contact.contactStatus] > agentStatusRank[b.contact.contactStatus]) {
      return 1;
    } else {
      /* if (agentStatusRank[a.job.status] === agentStatusRank[b.job.status]) { */
      return 0; // TODO: date comparision
    }
  };



async function reloadSections() {

  console.log("initialize contact sections", customerContacts.value);
  
  const sections = customerContacts.value?.map((contact) => ({
    label:
      contact.title +
      "@" +
      contact.company +
      "   (" + i18n(`contact.status.${contact.contactStatus}`) + ")",
    contact: contact,
    requiresAction: requiresAction(contact),
  })).sort(sortContactSections);

  customerContactSections.value = sections ?? [];
}

function requiresAction(contact: CustomerContact): boolean {
  if (props.isCustomer) {
    return (
      contact.contactStatus === CustomerContactStatus.NEW
    );
  } else {
    return contact.contactStatus === CustomerContactStatus.APPROVED;
  }
}

function sortContactSections(a: CustomerContactSection, b: CustomerContactSection): number {
  
  if (props.isCustomer) {
    return customerSorter(a, b);
  } else {
    return agentSorter(a, b);
  }
}

async function markContacted(customerContactId: number) {
  console.log("marking contacted for", customerContactId);
  const { error } = await supabase
    .from("customer_contacts")
    .update({ status: CustomerContactStatus.CONTACTED })
    .eq("id", customerContactId);
  if (error) {
    throw error;
  }
  alert("marked as contacted!");
  refresh();
}

async function approveContact(contact: CustomerContact) {
  console.log("marking contact approved", contact.id);
  const { error } = await supabase
    .from("customer_contacts")
    .update({ status: CustomerContactStatus.APPROVED })
    .eq("id", contact.id);
  if (error) {
    throw error;
  }
  toast.add({
    description: `Contact ${contact.name}@${contact.company} approved!`,
    color: "green",
    timeout: toastTimeout,
  });
  refresh();
}

async function declineContact(contact: CustomerContact) {
  console.log("declining contact ", contact.id);
  const { error } = await supabase
    .from("customer_contacts")
    .update({ status: CustomerContactStatus.DECLINED })
    .eq("id", contact.id);
  if (error) {
    throw error;
  }
  toast.add({
    description: `Contact ${contact.name} declined`,
    color: "red",
    timeout: toastTimeout,
  });
  refresh();
}

async function updateCustomerNote(contact: CustomerContact) {
  console.log("updating customer note ", contact.customerNotes);
  const { error } = await supabase
    .from("customer_contacts")
    .update({ customer_notes: contact.customerNotes })
    .eq("id", contact.id);
  if (error) {
    throw error;
  }
  toast.add({
    description: `customer note updated`,
    color: "green",
    timeout: toastTimeout,
  });
  // don't need to refresh
}

async function updateAgentNote(contact: CustomerContact) {
  console.log("updating agent note ", contact.agentNotes);
  const { error } = await supabase
    .from("customer_contacts")
    .update({ agent_notes: contact.agentNotes })
    .eq("id", contact.id);
  if (error) {
    throw error;
  }
  toast.add({
    description: `agent note updated`,
    color: "green",
    timeout: toastTimeout,
  });
  // don't need to refresh
}

function closeAll() {
  accordionRef.value.buttonRefs.forEach((btn: any) => btn.close());
}

function refresh() {
  props.refreshContacts();
  closeAll();
}

// this load the table with initial data
await reloadSections();


watch(customerContacts, async () => {
  console.log("customerContacts changed!");
  reloadSections();
})

</script>

