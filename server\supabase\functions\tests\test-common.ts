import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../lib/database.types.ts';
import { v4 as uid } from 'npm:uuid';
import { Customer, CustomerSearchCriteriaCreated, getOnboardingSurvey, Plan, PLAN_STATUS, Postmark, Product, PRODUCT_CODE } from '../shared/common.ts';
import { Email } from '../shared/common.ts';
import { Events } from '../shared/common.ts';
import { getEnumKey } from '../shared/common.ts';
import { randomInt } from 'node:crypto';

export const checkFileExists = async (path: string, bucket: string, supabase: SupabaseClient) => {
  const { data, error } = await supabase.storage
    .from(bucket)
    .list(path.split("/")[0], {
      search: path.split("/")[1],
    });

  if (error) throw error;
  return data && data.length > 0;
};

export async function createCustomer(
      email: string,
      supabase: SupabaseClient<Database>, 
      affiliateId?: string, 
      referredById?: string): Promise<Customer> {

    // if customer exists, use it, otherwise create it
    const {data: existingCustomer, error: fetchError} = await supabase
      .from("customers")
      .select("id, email, referred_by_id, affiliate_id")
      .eq("email", email)
  
    if (!fetchError && existingCustomer.length > 0) {
      console.log("using existing customer", existingCustomer[0].id)
      return {
        id: existingCustomer[0].id,
        email: existingCustomer[0].email,
        affiliateId: existingCustomer[0].affiliate_id,
        referredById: existingCustomer[0].referred_by_id,
        plans: []
      }
  
    } else {
      console.log("no customer found, creating...")
      const customerId = uid()
      await supabase.auth.admin.createUser({
        id: customerId,
        email: email, 
        email_confirm: false, 
        password: '' })
      const {error} = await supabase
          .from("customers")
          .insert({
            id: customerId,
            email: email,
            name: "Testy Guy",
            affiliate_id: affiliateId,
            referred_by_id: referredById
          })
      if (error) {
        throw error
      }    

      const { error: bucketError } = await supabase.storage.createBucket(customerId);
      if (bucketError) {
        throw bucketError;
      }

      console.log(`created customer ${customerId}`)
      return {
        id: customerId,
        email: email,
        affiliateId: affiliateId,
        referredById: referredById,
        plans: []
      }
    }
  }

export async function upsertOnboarding(customerId:string,
  jobTitles:string[],
  locations:string[],
  supabase:SupabaseClient<Database>
) {

  const existing = await getOnboardingSurvey(customerId, supabase);
  if (!existing) {

    const { data, error } = await supabase
      .from("customer_onboarding")
      .insert({ customer_id: customerId, job_titles: jobTitles.join(",") })
      .select("id")
      .single();

    if (error) {
      throw new Error(`could not insert onboarding: ${error.message}`);
    }

    await supabase
      .from("onboarding_locations")
      .insert(
        locations.map((m) => ({
          onboarding_id: data.id,
          location: m,
          work_authorized: true,
          pay_requirements: "100"
        }))
      );

  } else {
    await supabase
      .from("customer_onboarding")
      .update({job_titles: jobTitles.join(",") })
      .eq("id", existing.id);

    await supabase
      .from("onboarding_locations")
      .delete()
      .eq("onboarding_id", existing.id);

    const { error } = await supabase
      .from("onboarding_locations")
      .insert(
        locations.map((m) => ({
          onboarding_id: existing.id,
          location: m,
          work_authorized: true,
          pay_requirement: "100"
        }))
      );
    if (error) {
      throw new Error(`could not insert locations: ${error.message}`)
    }
  }
}
  
export async function upsertPlan(customerId:string, productCode:PRODUCT_CODE, supabase: SupabaseClient<Database>): Promise<Plan> {

  const { data, error } = await supabase
      .from("plans")
      .select("*, product:products(*)")
      .eq("customer_id", customerId)
      .eq("status", "OPEN")
      .eq("product.product_code", productCode)
      .order("created_at");
  if (error) {
    throw error;
  }
  if (data.length > 0) {
    // use existing plan
    return {
      customerId: customerId,
      productCode: productCode,
      status: PLAN_STATUS.OPEN
    }
  } else {
    // none existing, create a new one
    const product = await getProduct(productCode, supabase);
    const { error:insertError } = await supabase.from("plans")
      .insert({
        customer_id: customerId,
        product_id: product.id,
        payment_id: randomInt(100000),
        work_promised: 80,
        status: PLAN_STATUS.OPEN
      })
    if (insertError) {
      throw insertError;
    }
    return {
      customerId: customerId,
      productCode: productCode,
      status: PLAN_STATUS.OPEN
    }
  }
}

export async function getProduct(productCode: PRODUCT_CODE, supabase:SupabaseClient<Database>): Promise<Product> {
  const { data, error } = await supabase
    .from("products")
    .select("*")
    .eq("product_code", productCode)
    .single(); 

  if (error) {
    throw error; 
  }

  if (!data) {
    throw new Error(`Product with code ${productCode} not found`);
  }

  return { id: data.id, productCode: getEnumKey(PRODUCT_CODE, data.product_code) };
}



export async function post(body: string): Promise<number> {
  console.log("Posting", body);
  const request = new Request("http://0.0.0.0:8000/", {
      method: "POST",
      body: body,
      headers: {
          "content-type": "application/json",
          "x-nylas-signature": Deno.env.get("NYLAS_WEBHOOK_KEY")!,
      },
      });
  const response = await fetch(request);
  console.log("response", response.statusText);
  if (response.status !> 200 && response.status !< 300) {
    throw new Error(`bad response: ${response.status}, ${await response.text()}`)
  }
  await response.text();
  return response.status;
}

export async function get(params: string) {
  const request = new Request("http://0.0.0.0:8000/" + params, {
      method: "GET",
  });
  const response = await fetch(request);
  return response;
}

export class TestPostmark implements Postmark {
  sent: Email[] = []
  constructor() { }
  async sendEmail(email: Email): Promise<void> {
    console.log("TestPostmark received email", email);
    await this.sent.push(email);
    return;
  }
}

export class TestEvents implements Events {
  published: CustomerSearchCriteriaCreated[] = [];
  publishCustSearchCriteriaCreated(payload: CustomerSearchCriteriaCreated[], _topic: string): Promise<void> {
    payload.forEach((event) => (
      this.published.push(event )
    ))
    return Promise.resolve();
  }
}

