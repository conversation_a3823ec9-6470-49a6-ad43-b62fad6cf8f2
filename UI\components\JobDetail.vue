<template>
  <div class="max-w-7xl mx-auto">
    <div v-if="job">
      <div class="p-2 text-md border rounded-lg text-slate-700">
        <UContainer>
          <UProgress
            :value="statusProgress(job.status)"
            :color="statusColor(job.status)"
            class="pt-2 pb-6"
          >
            <template #indicator="{ percent }">
              <div class="text-left text-sm">
                ({{ $t(`job.status.${job.status}`) }})
                {{ $t(`job.nextStep.${jobNextStep(job)}`) }}
              </div>
            </template>
          </UProgress>
        </UContainer>

        <UDivider label="Job Details" :ui="{ label: 'text-base' }" />

        <UContainer class="flex flex-col md:flex-row gap-5 pt-4 space-y-3">
          <!-- general job info -->
          <div class="grid grid-cols-3 gap-2 gap-y-2 md:w-3/5 text-sm">
            <span class="text-left col-span-1">Title:</span>
            <span class="col-span-2">{{ job.title }}</span>

            <span class="text-left col-span-1">Company:</span>
            <span class="col-span-2">{{ job.employer }}</span>

            <span class="text-left col-span-1">Location:</span>
            <span class="col-span-2">{{ job.location }}</span>

            <span class="text-left col-span-1">Pay:</span>
            <span class="col-span-2"> {{ job.pay }}</span>

            <span class="text-left col-span-1">Job Type:</span>
            <span class="col-span-2">{{ $t(`job.type.${job.jobType}`) }}</span>

            <span class="text-left col-span-1">Visa Required:</span>
            <span class="col-span-2">{{ job.visaRequired }}</span>

            <span class="text-left col-span-1">Easy Apply:</span>
            <span class="col-span-2">{{ job.customerApply }}</span>

            <div class="flex flex-nowrap relative">
              <span v-if="job.accountRequired" class="">
                <UPopover mode="hover">
                  <UIcon
                    name="la:question-circle-solid"
                    size="24"
                    class="text-blue-400"
                  />
                  <template #panel>
                    <div
                      class="w-64 p-4 overflow-visible text-wrap text-center"
                    >
                      Job Applications that require an OTP/Verification can be
                      handled in 2 ways:
                      <ol class="px-6 list-decimal text-left pt-2">
                        <li>Create a new Gmail, give us access to it.</li>
                        <li>
                          Coordinate a time with your agent to pass
                          OTP/Verification links.
                        </li>
                      </ol>
                    </div>
                  </template>
                </UPopover>
              </span>
              <span class="left-0">OTP/Verify Required:</span>
            </div>
            <span v-if="props.isCustomer" class="col-span-2">{{
              job.accountRequired ? "Yes" : "No"
            }}</span>
            <span v-if="!props.isCustomer" class="col-span-2">
              <UToggle
                v-model="job.accountRequired"
                color="orange"
                @change="updateAccountRequired(job)"
              />
            </span>

            <span class="text-left col-span-1">Languages:</span>
            <span class="col-span-2">{{}}</span>

            <span class="text-left col-span-1">Description:</span>
            <span class="col-span-2">
              <div class="border p-2 rounded-lg border-gray-400">
                {{ job.summary ?? job.description }}
              </div>
              <div v-if="job.summary">
                <UPopover>
                  <span class="text-xs">Read full description...</span>

                  <template #panel>
                    <div class="p-6 max-w-2xl">
                      {{ job.description }}
                    </div>
                  </template>
                </UPopover>
              </div>
            </span>
          </div>

          <!-- LINKS & DOCS -->
          <div class="h-min w-full md:w-2/5">
            <div class="flex flex-col space-y-3">
              <div class="grid grid-cols-3">
                <div class="col-span-1 px-3">Job Link:</div>
                <div class="col-span-2">
                  <ULink
                    :to="job.url"
                    target="_blank"
                    class="text-orange-500 underline"
                  >
                    Go To Job
                  </ULink>
                </div>
              </div>

              <div class="grid grid-cols-3 items-center">
                <div class="col-span-1 px-3">Generated Cover Letter:</div>
                <div class="col-span-2">
                  <div class="flex">
                    <ULink
                      variant="outline"
                      label="View/Edit"
                      class="text-orange-500 w-[163px] text-center items-center flex"
                      to="#"
                      @click="isOpen = true"
                      >View/Edit
                    </ULink>
                    <UModal v-model="isOpen" prevent-close>
                      <UCard
                        class="text-md"
                        :ui="{
                          body: {
                            padding: 'px-4 py-5 sm:py-3 ',
                          },
                        }"
                      >
                        <div class="flex justify-end items-end">
                          <UIcon
                            name="la-times-circle"
                            class="w-8 h-8 cursor-pointer text-orange-500"
                            @click="isOpen = false"
                          />
                        </div>
                        <div class="pt-2 flex flex-col">
                          <quill-editor
                            ref="quillEditorRef"
                            v-model:content="generatedCoverLetter"
                            theme="snow"
                            class="cus-qill"
                            placeholder="Enter a description.."
                            content-type="html"
                            :toolbar="[
                              [
                                { 'size': ['small', false, 'large', 'huge'] },
                                'bold',
                                'italic',
                                'underline',
                                'link',
                                { align: [] },
                                { list: 'ordered' },
                                { list: 'bullet' },
                                { indent: '-1' },
                                { indent: '+1' },
                              ],
                            ]"
                          />
                          <div class="mt-5 flex justify-end">
                            <UButton
                              label="Save"
                              class="w-fit"
                              @click="saveCoverLetter(job)"
                            />
                          </div>
                        </div>
                      </UCard>
                    </UModal>
                    <UIcon
                      v-if="generatedCoverLetter"
                      name="la:arrow-circle-down"
                      class="hover:cursor-pointer text-orange-500 w-8 h-8"
                      @click="downloadPDF(job)"
                    />
                    <UIcon
                      v-if="allowRegenerateCover"
                      :class="
                        [isRegenerating && 'animate-spin'] +
                        ' hover:cursor-pointer text-orange-500 h-8 w-8 mx-2'
                      "
                      title="Regenerate cover letter"
                      name="la:redo-alt"
                      @click="regenerateCoverLetter(job)"
                    />
                  </div>
                </div>
              </div>

              <div class="grid grid-cols-3 items-center">
                <div class="col-span-1 px-3">Cover Letter:</div>
                <div class="col-span-2">
                  <DocumentDisplay
                    v-model:options="coverLetters"
                    :selected-doc-id="jobDocuments?.coverLetter?.document.id"
                    @change="(docId) => selectDocument(docId, DocumentType.COVER)"
                  />
                </div>
              </div>

              <div class="grid grid-cols-3">
                <div class="col-span-1 px-3">CV:</div>
                <div class="col-span-2">
                  <DocumentDisplay
                    v-model:options="CVs"
                    :selected-doc-id="jobDocuments?.cv?.document.id"
                    @change="(docId) => cvChanged(docId)"
                  />
                </div>
              </div>

              <div v-if="screenshots?.length" class="grid grid-cols-3">
                <div class="col-span-1 px-3 items-start text-start">
                  Screenshot:
                </div>
                <div class="col-span-2">
                  <div
                    v-for="(item, index) in screenshots"
                    :key="index"
                    class="flex justify-between gap-2"
                  >
                    <p>
                      {{ truncateFileName(item.document.name, 13) }}
                    </p>
                    <div class="flex items-center">
                      <DocumentDownload :document="item.document" />
                      <UIcon
                        v-if="!isCustomer"
                        class="text-red-500 w-7 h-7 mb-1.5 hover:cursor-pointer"
                        name="la:trash"
                        :title="`delete ${item.document.name}`"
                        @click="deleteScreenshot(item)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </UContainer>

        <!-- ACTIONS -->
        <UDivider label="Actions" class="py-3" :ui="{ label: 'text-base' }" />
        <UContainer class="w-full">
          <div class="flex items-center justify-between">
            <p class="py-2 px-3">
              Next Step: {{ $t(`job.nextStep.${jobNextStep(job)}`) }}
            </p>
          </div>

          <div v-if="isCustomer" class="px-3">
            <!-- customer view -->

            <!-- customer easy apply-->
            <div
              v-if="job.customerApply && job.status === CustomerJobStatus.NEW"
              class="gap-3"
            >
              <div class="pt-2">
                1.
                <ULink
                  :to="job.url"
                  target="_blank"
                  class="text-orange-600 text-xl"
                >
                  Apply
                </ULink>
                for this job!
              </div>
              <div class="pt-2">
                2.
                <UButton
                  icon="la:check-circle-solid"
                  @click="markJobAppliedFor(job)"
                >
                  I Applied
                </UButton>
              </div>
              <div class="pt-2">
                OR ...
                <UButton
                  icon="la:pause-circle-solid"
                  class="bg-red-500 hover:bg-red-600 text-white"
                  @click="declineJob(job)"
                >
                  Reject This Job
                </UButton>
              </div>
            </div>

            <!-- customer approval-->
            <div
              v-if="!job.customerApply && job.status === CustomerJobStatus.NEW"
              class="flex flex-col sm:flex-row gap-3"
            >
              <UButton
                icon="la:check-circle-solid"
                class="bg-green-500 hover:bg-green-600 text-white"
                @click="approveJob(job)"
              >
                Approve This Job
              </UButton>
              <UButton
                icon="la:pause-circle-solid"
                class="bg-red-500 hover:bg-red-600 text-white"
                @click="declineJob(job)"
              >
                Reject This Job
              </UButton>
            </div>

            <div
              v-if="job.status === CustomerJobStatus.APPLIED"
              class="flex flex-col gap-3"
            >
              Are you satisfied with this application?
              <div>
                <UButton
                  icon="la:thumbs-up-solid"
                  class="bg-green-500 hover:bg-green-600 text-white"
                  @click="satisfied(job)"
                >
                  Yes
                </UButton>
                <UButton
                  icon="la:thumbs-down-solid"
                  class="bg-red-500 hover:bg-red-600 text-white mx-4"
                  @click="dissatisfied(job)"
                >
                  No
                </UButton>
              </div>
            </div>
          </div>
          
          
          <div v-else class="px-3">
            <!-- agent view -->

            <div class="flex justify-end">
              <UButton
                v-if="!isCustomer && job.status === 'NEW'"
                color="red"
                variant="outline"
                icon="la:trash"
                class="ml-2"
                @click.stop="deleteJob(job)"
              >
                Delete
              </UButton>
            </div>

            <div class="flex justify-end">
              <UButton
                v-if="!isCustomer && jobCanBeExpired(job)"
                color="red"
                variant="outline"
                icon="la:clock"
                class="ml-2 mt-2"
                @click.stop="expireJob(job)"
              >
                Job Expired
              </UButton>
            </div>

            <p>upload a cover letter:</p>
            <FileUploader
              :customer-id="job.customerId"
              :document-types="[DocumentType.COVER]"
              :document-added-callback="
                (doc) => {
                  documentAdded(doc)
                }
              "
            />

            <div
              v-if="job.status === CustomerJobStatus.APPROVED"
              class="py-4 space-y-2"
            >
              <div>
                1.
                <ULink
                  :to="job.url"
                  target="_blank"
                  class="text-orange-600 text-xl"
                >
                  Apply</ULink
                >
                for this job!
              </div>

              <div class="flex items-center">
                <div class="w-min">2.</div>
                <div class="w-min">
                  <FileUploader
                    :customer-id="job.customerId"
                    :document-types="[DocumentType.COVER]"
                    :document-added-callback="
                      (doc) => {
                        documentAdded(doc);
                      }
                    "
                  />
                </div>
                <div class="w-full">
                  upload the cover letter used (if different than the current
                  one)
                </div>
              </div>

              <div class="flex items-center">
                <div class="w-min">3.</div>
                <div class="w-min">
                  <FileUploader
                    :customer-id="job.customerId"
                    :document-types="[DocumentType.SCREENSHOT]"
                    :document-added-callback="(doc) => screenshotAdded(doc)"
                  />
                </div>
                <div class="w-full">upload screen shot</div>
              </div>

              <div>
                4.
                <UButton @click="markJobAppliedFor(job)">
                  Mark job applied for
                </UButton>
              </div>
            </div>
          </div>
        </UContainer>

        <!-- NOTES -->
        <UDivider label="Notes" class="py-4" :ui="{ label: 'text-base' }" />
        <UContainer class="flex flex-wrap w-full gap-y-3 pb-3">
          <div class="px-3 w-full sm:w-1/2 sm:pr-2">
            <UTextarea
              v-model="customerNotes"
              autoresize
              color="gray"
              class="text-slate-700"
              :disabled="!isCustomer"
              :placeholder="job.customerNotes ?? 'Customer Notes ...'"
              @blur="updateCustomerNote(job)"
            />
          </div>

          <div class="px-3 w-full sm:w-1/2 sm:pl-2">
            <UTextarea
              v-model="agentNotes"
              autoresize
              color="gray"
              class="text-slate-700"
              :disabled="isCustomer"
              :placeholder="job.agentNotes ?? 'Agent Notes...'"
              @blur="updateAgentNote(job)"
            />
          </div>
        </UContainer>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  CustomerJobStatus,
  DocumentType,
  type JobDocument,
  jobNextStep,
  JobStatus,
  type CustomerJob,
  type Document,
  type CustomerJobDocuments,
  notNull,
  toJobDocument,
  emptyDocument,
  isNull,
} from "~/common/common";
import type { Database } from "~/lib/applysquad-common/database.types";
import { jsPDF } from "jspdf";


const props = defineProps<{ isCustomer: boolean }>();
const job = defineModel<CustomerJob>("job", {required: true});
const allJobDocuments = defineModel<JobDocument[]>("jobDocuments", { default: [] });
const allDocuments = defineModel<Document[]>("documents", { default: [] })

const CVs = ref<Document[]>([]);
const coverLetters = ref<Document[]>([]);
const screenshots = ref<JobDocument[]>([]);
const jobDocuments = ref<CustomerJobDocuments>();

const supabase = useSupabaseClient<Database>();
const toast = useToast();
const toastTimeout = 4000;
const toastErrorTimeout = 10000;
const isOpen = ref(false);
const generatedCoverLetter = ref<string>();
const quillEditorRef = ref<any>(null); // explicitly type as any
const allowRegenerateCover = ref(false);
const selectedCvId = ref();
const isRegenerating = ref(false);
const customerNotes = computed({
  get: () => job.value?.customerNotes ?? "",
  set: (value) => {
    if (job.value) {
      job.value.customerNotes = value;
    }
  },
});

const agentNotes = computed({
  get: () => job.value?.agentNotes ?? "",
  set: (value) => {
    if (job.value) {
      job.value.agentNotes = value;
    }
  },
});

/**
 * This only needs to be run once, so it's a separate method
 * and only called initially.
 */
async function loadCoverLetter() {
  const { data, error } = await supabase
    .from("cover_letters")
    .select("data")
    .eq("customer_job_id", job.value.id)
    .order("created_at", { ascending: false })
    .limit(1);

  if (error || !data) {
    console.log("error", error);
    throw error;
  }

  generatedCoverLetter.value = data[0]?.data ?? job.value.oldGeneratedCover;
  if (isNull(generatedCoverLetter.value)) {
    generatedCoverLetter.value = "";
  }
};


async function loadDocuments() {

  console.log('loading docs');

  CVs.value = allDocuments.value.filter(
    (doc) => doc.type === DocumentType.CV
  );
  CVs.value.unshift(emptyDocument(DocumentType.CV));

  coverLetters.value = allDocuments.value.filter(
    (doc) => doc.type === DocumentType.COVER
  );
  coverLetters.value.unshift(emptyDocument(DocumentType.COVER));

  screenshots.value = allJobDocuments.value.filter(
    (doc) => doc.customerJobId === job.value.id && doc.type === DocumentType.SCREENSHOT
  );

  console.log('init JobDetail, CVs', CVs.value.length);
  console.log('all job docs', allJobDocuments.value.length);

}

/**
 * @precondition: `loadDocument()` has been run
 */
async function loadJobDocuments() {

  console.log('all job docs', allJobDocuments.value);

  jobDocuments.value = {
    cv: allJobDocuments.value.find((doc) => 
      doc.customerJobId === job.value.id && doc.type === DocumentType.CV
    ),
    coverLetter: allJobDocuments.value.find((doc) => 
      doc.customerJobId === job.value.id && doc.type === DocumentType.COVER
    ),
    screenshots: screenshots.value
  }

  console.log('loaded job documents:', jobDocuments);
}


const saveCoverLetter = async (job: CustomerJob) => {

  // TODO: instead of seeing if it exist, save the id on fetch
  const { data: existingCoverLetter, error: fetchError } = await supabase
    .from("cover_letters")
    .select("id")
    .eq("customer_job_id", job.id)
    .single();

  if (fetchError && fetchError.code !== "PGRST116") {
    console.error("Error fetching existing cover letter:", fetchError);
    return;
  }
  console.log("existingCoverLetter", existingCoverLetter);

  const { error } = await supabase.from("cover_letters").upsert({
    id: existingCoverLetter?.id ?? undefined, // If exists, update; if not, insert
    data: generatedCoverLetter.value!,
    updated_at: new Date().toISOString(),
    customer_job_id: job.id,
  });

  if (error) {
    console.error("Error saving cover letter:", error);
    throw error;
  }

  toast.add({
    title: "Success",
    description: "Cover Letter Saved Successfully",
    color: "green",
  });

  isOpen.value = false;
};

const downloadPDF = async (job: CustomerJob) => {
  if (import.meta.client) {
    let htmlContent = quillEditorRef.value?.getSemanticHTML
      ? quillEditorRef.value.getSemanticHTML()
      : generatedCoverLetter.value;

    // Apply link colors
    htmlContent = htmlContent.replace(
      /<a([^>]*)>/g,
      '<a$1 style="color: blue; text-decoration: underline;">'
    );

    // Add custom list markers and indentation, paragraph spacing
    htmlContent = htmlContent
      .replace(/<p([^>]*)>/g, '<p$1 style="margin-bottom: 0px;">')
      .replace(/<ul>/g, '<ul style="padding-left: 20px;">')
      .replace(/<ol>/g, '<ol style="padding-left: 20px;">');

    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = htmlContent;

    // Add bullet points for unordered lists
    tempDiv.querySelectorAll("ul li").forEach((li: any) => {
      li.style.display = "list-item";
      li.style.listStyleType = "none";
      li.innerHTML = `&#8226; ${li.innerHTML}`;
    });

    // Add numbers for ordered lists
    tempDiv.querySelectorAll("ol li").forEach((li: any, index) => {
      li.style.display = "list-item";
      li.style.listStyleType = "none";
      li.innerHTML = `${index + 1}. ${li.innerHTML}`;
    });

    // Handle indentation
    tempDiv.querySelectorAll(".ql-indent-1").forEach((element: any) => {
      element.style.marginLeft = "30px";
    });
    tempDiv.querySelectorAll(".ql-indent-2").forEach((element: any) => {
      element.style.marginLeft = "60px";
    });

    htmlContent = tempDiv.innerHTML;

    // Wrap the HTML content in a proper structure with styles
    const fullHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <style>
            body {
                font-family: Arial, sans-serif;
                font-size: 12px;
                line-height: 1.6;
                color: #333;
                padding: 0;
                margin: 0;
              }
            .ql-editor {
              font-family: Arial, sans-serif;
              font-size: 12px;
              line-height: 1.4; /* Slightly tighter line height */
              padding: 12px;
            }
            .ql-align-center {
              text-align: center;
            }
            .ql-align-right {
              text-align: right;
            }
            .ql-align-justify {
              text-align: justify;
            }
            .ql-list[data-list="ordered"] {
              list-style-type: decimal;
            }
            .ql-list[data-list="bullet"] {
              list-style-type: disc;
            }
            p {
              padding: 0 !important; /* Reduced bottom margin for paragraphs */
              margin: 0 !important; /* Reduced bottom margin for paragraphs */
              line-height: 1.4; /* Consistent line height */
            }
            /* Handle empty paragraphs and line breaks */
            p:empty {
              height: 0;
              margin: 0 !important;
              padding: 0 !important;
            }
            br {
              content: "";
              display: block;
              margin: 0; /* Consistent spacing for line breaks */
              line-height: 0.5em; /* Half the normal line height */
            }
            a {
              color: #1a0dab;
              text-decoration: underline;
            }
            .ql-editor .ql-editor {
              font-size: 14px; /* default (false) */

              /* Optional: limit max width or padding if needed */
              padding: 10px;
            }

            /* Quill's size formats */
            .ql-editor .ql-size-small {
              font-size: 12px;
            }
            .ql-editor .ql-size-large {
              font-size: 18px;
            }
            .ql-editor .ql-size-huge {
              font-size: 24px;
            }
            .ql-indent-1 { margin-left: 3em; }
            .ql-indent-2 { margin-left: 6em; }
            .ql-indent-3 { margin-left: 9em; }
            .ql-indent-4 { margin-left: 12em; }
            .ql-indent-5 { margin-left: 15em; }
            .ql-indent-6 { margin-left: 18em; }
            .ql-indent-7 { margin-left: 21em; }
            .ql-indent-8 { margin-left: 24em; }
          </style>
        </head>
        <body>
          <div class="ql-editor">${htmlContent}</div>
        </body>
      </html>
    `;

    const pdf = new jsPDF({
      orientation: "portrait",
      unit: "pt",
      format: "a4",
      compress: true,
    });

    pdf.html(fullHtml, {
      callback: (doc) => {
        doc.save(`cover_letter_${job.employer}.pdf`);
      },
      x: 60,
      y: 60,
      margin: 30,
      width: 420,
      windowWidth: 700,
    });
  }
};

async function deleteJob(job: CustomerJob) {
  job.status = CustomerJobStatus.DELETED;

  const { error } = await supabase
    .from("customer_jobs")
    .update({ status: CustomerJobStatus.DELETED })
    .eq("id", job.id);

  if (error) {
    console.error("Failed to delete job:", error);
    return;
  }

  toast.add({
    description: `Job ${job.title} Deleted Successfully`,
    color: "red",
    timeout: toastTimeout,
  });

}

function jobCanBeExpired(job: CustomerJob): boolean {
  return [ 
    CustomerJobStatus.NEW, 
    CustomerJobStatus.APPROVED ].includes(job.status);
}

async function expireJob(job: CustomerJob) {
  job.status = CustomerJobStatus.EXPIRED;

  const { error } = await supabase
    .from("customer_jobs")
    .update({ status: CustomerJobStatus.EXPIRED })
    .eq("id", job.id);

  if (error) {
    console.error(`Failed to expire job: ${job.id}`, error);
    return;
  }

  const { error:jobError } = await supabase
    .from("jobs")
    .update({ job_status: JobStatus.CLOSED })
    .eq("id", job.jobId);

  if (jobError) {
    console.log(`failed to close job ${job.jobId}`, jobError);
  }

  toast.add({
    description: `Job ${job.title} Expired Successfully`,
    color: "red",
    timeout: toastTimeout,
  });

}


async function approveJob(job: CustomerJob) {
  job.status = CustomerJobStatus.APPROVED;

  const { error } = await supabase
    .from("customer_jobs")
    .update({ status: CustomerJobStatus.APPROVED })
    .eq("id", job.id);

  if (error) {
    throw error;
  }

  toast.add({
    description: `Job ${job.title} Approved Successfully`,
    color: "green",
    timeout: toastTimeout,
  });
}

async function declineJob(job: CustomerJob) {
  job.status = CustomerJobStatus.DECLINED;

  const { error } = await supabase
    .from("customer_jobs")
    .update({ status: CustomerJobStatus.DECLINED })
    .eq("id", job.id);

  if (error) {
    throw error;
  }

  toast.add({
    description: `Job ${job.title} Declined Successfully`,
    color: "red",
    timeout: toastTimeout,
  });

}

async function satisfied(job: CustomerJob) {
  job.status = CustomerJobStatus.SATISFIED;

  const { error } = await supabase
    .from("customer_jobs")
    .update({ status: CustomerJobStatus.SATISFIED })
    .eq("id", job.id);

  if (error) {
    throw error;
  }

  toast.add({
    description: `Marked as satisfied`,
    color: "green",
    timeout: toastTimeout,
  });
}

async function dissatisfied(job: CustomerJob) {
  job.status = CustomerJobStatus.DISSATISFIED;

  const { error } = await supabase
    .from("customer_jobs")
    .update({ status: CustomerJobStatus.DISSATISFIED })
    .eq("id", job.id);

  if (error) {
    throw error;
  }

  toast.add({
    description: `Marked as dissatisfied`,
    color: "red",
    timeout: toastTimeout,
  });
}

async function updateCustomerNote(job: CustomerJob) {
  const { error } = await supabase
    .from("customer_jobs")
    .update({ customer_notes: job.customerNotes })
    .eq("id", job.id);

  if (error) {
    throw error;
  }

  toast.add({
    description: `Customer note updated`,
    color: "green",
    timeout: toastTimeout,
  });
}

async function updateAccountRequired(job: CustomerJob) {
  const { error } = await supabase
    .from("jobs")
    .update({ account_required: job.accountRequired })
    .eq("id", job.jobId);
  if (error) {
    throw error;
  }

  toast.add({
    description: `Account required updated`,
    color: "green",
    timeout: toastTimeout,
  });
  return job.accountRequired;
}

async function updateAgentNote(job: CustomerJob) {
  const { error } = await supabase
    .from("customer_jobs")
    .update({ agent_notes: job.agentNotes })
    .eq("id", job.id);

  if (error) {
    throw error;
  }

  toast.add({
    description: `Agent note updated`,
    color: "green",
    timeout: toastTimeout,
  });
}

async function markJobAppliedFor(job: CustomerJob) {
  let payload = {};
  if (job.customerApply) {
    job.status = CustomerJobStatus.CUSTOMER_APPLIED;
    payload = {
      status: CustomerJobStatus.CUSTOMER_APPLIED,
    };
  } else {
    job.status = CustomerJobStatus.APPLIED;
    payload = {
      status: CustomerJobStatus.APPLIED,
    };
  }

  const { error } = await supabase
    .from("customer_jobs")
    .update(payload)
    .eq("id", job.id);

  if (error) {
    throw error;
  }

  toast.add({
    description: `Job marked as applied`,
    color: "green",
    timeout: toastTimeout,
  });
}

async function deleteScreenshot(toDelete: JobDocument) {
  
  if (toDelete.type !== DocumentType.SCREENSHOT) {
    throw new Error("Cannot delete document type " + toDelete.type);
  }

  // "delete" from documents
  const { error } = await supabase
    .from("documents")
    .update({ active: false })
    .eq("id", toDelete.document.id);

  if (error) {
    throw error;
  }

  // delete from job_documents
  const { error: jobDocError } = await supabase
    .from("job_documents")
    .delete()
    .eq("id", toDelete.id);

  if (jobDocError) {
    throw jobDocError;
  }

  screenshots.value = screenshots.value.filter((doc) => doc.id !== toDelete.id)

  errorToast(`Document ${toDelete.document.name} deleted`);
}

async function cvChanged(cvId: string) {
  selectDocument(cvId, DocumentType.CV);
  selectedCvId.value = cvId;
  allowRegenerateCover.value = cvId != "" && cvId.length > 0;
}

async function regenerateCoverLetter(customerJob: CustomerJob) {
  console.log("regenerating cover letter");
  isRegenerating.value = true;
  const response = await $fetch("/api/regenerate-cover-letter", {
    query: {
      cvId: selectedCvId.value,
      jobId: customerJob.jobId,
      customerJobId: customerJob.id,
    },
  });
  console.log("got response from server", response);
  isRegenerating.value = false;
  if (response == "BAD_REQUEST") {
    console.error("could not regenerate cover letter", response);
  } else {
    const { data, error } = await supabase
      .from("cover_letters")
      .select("*")
      .eq("customer_job_id", customerJob.id)
      .order("created_at", { ascending: false })
      .limit(1);
    if (error) {
      throw error;
    }
    generatedCoverLetter.value = data[0].data!;
    allowRegenerateCover.value = false;
    successToast("cover letter regenerated");
  }
}

async function screenshotAdded(document: Document) {
  const { data: newJobDoc, error: insertError } = await supabase
    .from("job_documents")
    .insert({
      customer_job_id: job.value.id,
      customer_id: job.value.customerId,
      document_id: document.id,
      document_type: DocumentType.SCREENSHOT,
    })
    .select("*, document:documents(*)")
    .single();
  if (insertError) {
    console.error("cannot set document", insertError);
    throw insertError;
  }
  documentAdded(document);
  const jobDoc = toJobDocument(newJobDoc);
  allJobDocuments.value.push(jobDoc);
}

function documentAdded(document: Document) {
  allDocuments.value.push(document);
}

async function selectDocument(
  docId: string,
  docType: DocumentType
) {
  try {
    if (docType !== DocumentType.SCREENSHOT) {
      // multiple screenshots per job permitted, so don't delete
      const { error: deleteError } = await supabase
        .from("job_documents")
        .delete()
        .eq("customer_job_id", job.value.id)
        .eq("document_type", docType);
      if (deleteError) {
        throw deleteError;
      }
    }

    // docId can be null when we are removing the doc from the job
    if (notNull(docId)) {
      const { error: insertError } = await supabase
        .from("job_documents")
        .insert({
          customer_job_id: job.value.id,
          customer_id: job.value.customerId,
          document_id: docId,
          document_type: docType,
        });
      if (insertError) {
        console.error("cannot set document", insertError);
        throw insertError;
      }
    }

    // Show success message
    successToast(`${docType} updated successfully`);

    // Refresh job data
    // props.refreshData();
  } catch (error) {
    console.error("Error updating document:", error);
  }
}

function statusProgress(status: CustomerJobStatus) {
  if (
    status === CustomerJobStatus.DELETED ||
    status === CustomerJobStatus.EXPIRED
  ) {
    return 0;
  } else if (status === CustomerJobStatus.NEW) {
    return 25;
  } else if (status === CustomerJobStatus.APPROVED) {
    return 50;
  } else if (status === CustomerJobStatus.APPLIED) {
    return 75;
  } else if (
    status === CustomerJobStatus.SATISFIED ||
    status === CustomerJobStatus.DISSATISFIED ||
    status == CustomerJobStatus.DECLINED ||
    status === CustomerJobStatus.CUSTOMER_APPLIED
  ) {
    return 100;
  }
}

function statusColor(status: CustomerJobStatus) {
  if (
    status === CustomerJobStatus.DECLINED ||
    status === CustomerJobStatus.DISSATISFIED
  ) {
    return "red";
  } else if (
    status === CustomerJobStatus.DELETED ||
    status === CustomerJobStatus.EXPIRED
  ) {
    return "sky";
  } else {
    return "green";
  }
}

function truncateFileName(fileName: any, maxBaseLength: number) {
  const dotIndex = fileName.lastIndexOf(".");
  if (dotIndex === -1) return fileName; // No extension found
  const base = fileName.slice(0, dotIndex);
  const extension = fileName.slice(dotIndex);

  if (base.length > maxBaseLength) {
    return base.slice(0, maxBaseLength) + "..." + extension;
  }

  return fileName;
}

function successToast(message: string) {
  toast.add({
    id: message,
    description: message,
    color: "green",
    icon: "i-heroicons-check-circle",
    timeout: toastTimeout,
  });
}

function errorToast(message: string) {
  toast.add({
    id: message,
    description: message,
    color: "red",
    timeout: toastErrorTimeout,
  });
}

watch(allDocuments.value,
  async () => {
    console.log('job detail updating docs');
    await loadDocuments();  
  }
)

watch(allJobDocuments.value,
  async () => {
    await loadJobDocuments();
  }
)

// initialization
await loadCoverLetter();
await loadDocuments();
await loadJobDocuments();


</script>
