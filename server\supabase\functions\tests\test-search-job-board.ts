import { assertEquals, assertExists, assertRejects } from 'jsr:@std/assert';
import { TheirStackJobBoard } from '../search-job-board/job-search-theirstack.ts';
import { CustomerSearchCriteria, Job, SearchJobBoardsFound, Events } from '../shared/common.ts';
import { JOB_BOARD } from '../shared/common.ts';
import { setEvents, handleSearchJobBoardRequest, saveJobIfNotExists, checkCustomerJobExists, JobBoardSearch } from '../search-job-board/index.ts';

// Mock Events implementation for testing
class MockEvents implements Events {
  public publishedSearchJobBoardsFound: SearchJobBoardsFound[] = [];
  public publishedCustSearchCriteriaCreated: any[] = [];

  async publishCustSearchCriteriaCreated(payload: any[], topic: string): Promise<void> {
    console.log(`Mock: Publishing ${payload.length} customer search criteria to topic ${topic}`);
    this.publishedCustSearchCriteriaCreated.push(...payload);
  }

  async publishSearchJobBoardsFound(payload: SearchJobBoardsFound[], topic: string): Promise<void> {
    console.log(`Mock: Publishing ${payload.length} job board search results to topic ${topic}`);
    this.publishedSearchJobBoardsFound.push(...payload);
  }

  reset() {
    this.publishedSearchJobBoardsFound = [];
    this.publishedCustSearchCriteriaCreated = [];
  }
}

// Mock Supabase client for testing
class MockSupabaseClient {
  private jobs: Map<string, any> = new Map();
  private customerJobs: Map<string, any> = new Map();
  private jobsByUrl: Map<string, any> = new Map();

  from(table: string) {
    if (table === 'jobs') {
      return {
        select: (_columns: string) => ({
          eq: (column: string, value: string) => ({
            single: () => {
              if (column === 'url') {
                const job = this.jobsByUrl.get(value);
                return job
                  ? { data: job, error: null }
                  : { data: null, error: { code: 'PGRST116' } };
              }
              return { data: null, error: { code: 'PGRST116' } };
            }
          })
        }),
        insert: (data: any) => {
          // Save the job to our mock storage
          this.jobs.set(data.id, data);
          this.jobsByUrl.set(data.url, data);
          return { data: data, error: null };
        }
      };
    }

    if (table === 'customer_jobs') {
      return {
        select: (_columns: string) => ({
          eq: (column: string, value: string) => {
            const chainable = {
              eq: (column2: string, value2: string) => ({
                single: () => {
                  const key = `${value}-${value2}`;
                  const customerJob = this.customerJobs.get(key);
                  return customerJob
                    ? { data: customerJob, error: null }
                    : { data: null, error: { code: 'PGRST116' } };
                }
              })
            };
            return chainable;
          }
        })
      };
    }

    // Return a default object with empty methods to prevent undefined errors
    return {
      select: () => ({
        eq: () => ({
          single: () => ({ data: null, error: { code: 'PGRST116' } })
        })
      }),
      insert: () => ({
        select: () => ({
          single: () => ({ data: null, error: { code: 'PGRST116' } })
        })
      })
    };
  }

  // Helper methods for testing
  addJob(job: any) {
    this.jobs.set(job.id, job);
    this.jobsByUrl.set(job.url, job);
  }

  addCustomerJob(customerId: string, jobId: string) {
    const key = `${customerId}-${jobId}`;
    this.customerJobs.set(key, { id: 1, customer_id: customerId, job_id: jobId });
  }

  reset() {
    this.jobs.clear();
    this.customerJobs.clear();
    this.jobsByUrl.clear();
  }

  getJobs() {
    return Array.from(this.jobs.values());
  }

  getCustomerJobs() {
    return Array.from(this.customerJobs.values());
  }
}

// Test data
const mockCustomerSearchCriteria: CustomerSearchCriteria = {
  id: "test-criteria-123",
  search_date: "2025-01-15",
  job_board_key: JOB_BOARD.THEIR_STACK,
  customer_id: "test-customer-456",
  job_titles: ["Software Engineer", "Frontend Developer"],
  locations: ["Remote", "New York"],
  search_status: "NEW"
};

const mockJob: Job = {
  id: "theirstack-1234",
  url: "https://example.com/job/1234",
  employer: "MockCompany",
  title: "Software Engineer",
  location: "Remote",
  pay_amount: 120000,
  pay_frequency: "Monthly" as any,
  pay_currency: "USD",
  languages: "JavaScript, TypeScript",
  visa_required: false,
  description: "This is a mock job for testing.",
  job_type: "FULLTIME" as any,
  job_status: "OPEN" as any,
  account_required: false,
  customer_apply: false
};

// Global test setup
let mockEvents: MockEvents;
let mockSupabase: MockSupabaseClient;

function setupMocks() {
  mockEvents = new MockEvents();
  mockSupabase = new MockSupabaseClient();
  setEvents(mockEvents);
}

function resetMocks() {
  mockEvents?.reset();
  mockSupabase?.reset();
}

// Note: We now use the real exported functions (saveJobIfNotExists, checkCustomerJobExists)
// instead of mock implementations to ensure we're testing the actual logic

// Unit Tests
Deno.test("MockTheirStack returns expected job data", async () => {
  const jobBoard = new TheirStackJobBoard(undefined);

  const jobs = await jobBoard.search(mockCustomerSearchCriteria);

  assertEquals(jobs.length, 1);
  assertEquals(jobs[0].title, "Software Engineer");
  assertEquals(jobs[0].url, "https://example.com/job/1234");
  assertEquals(jobs[0].employer, "MockCompany");
  assertEquals(jobs[0].id, "theirstack-1234");
});

Deno.test("saveJobIfNotExists - saves new job successfully", async () => {
  setupMocks();

  const result = await saveJobIfNotExists(mockJob, mockSupabase as any);

  assertExists(result);
  assertEquals(result!.id, mockJob.id);
  assertEquals(result!.url, mockJob.url);

  const savedJobs = mockSupabase.getJobs();
  assertEquals(savedJobs.length, 1);
  assertEquals(savedJobs[0].id, mockJob.id);
});

Deno.test("saveJobIfNotExists - returns null for duplicate URL", async () => {
  setupMocks();

  // Add job first
  mockSupabase.addJob(mockJob);

  const result = await saveJobIfNotExists(mockJob, mockSupabase as any);

  assertEquals(result, null);

  // Should still only have one job
  const savedJobs = mockSupabase.getJobs();
  assertEquals(savedJobs.length, 1);
});

Deno.test("checkCustomerJobExists - returns false when no relationship exists", async () => {
  setupMocks();

  const exists = await checkCustomerJobExists("customer-123", "job-456", mockSupabase as any);

  assertEquals(exists, false);
});

Deno.test("checkCustomerJobExists - returns true when relationship exists", async () => {
  setupMocks();

  // Add customer-job relationship
  mockSupabase.addCustomerJob("customer-123", "job-456");

  const exists = await checkCustomerJobExists("customer-123", "job-456", mockSupabase as any);

  assertEquals(exists, true);
});

Deno.test("MockEvents - captures published events correctly", async () => {
  setupMocks();

  const events: SearchJobBoardsFound[] = [
    { customerId: "customer-1", jobId: "job-1" },
    { customerId: "customer-2", jobId: "job-2" }
  ];

  await mockEvents.publishSearchJobBoardsFound(events, "test-topic");

  assertEquals(mockEvents.publishedSearchJobBoardsFound.length, 2);
  assertEquals(mockEvents.publishedSearchJobBoardsFound[0].customerId, "customer-1");
  assertEquals(mockEvents.publishedSearchJobBoardsFound[1].jobId, "job-2");
});

// Integration Tests
Deno.test("Integration: Full job search and processing flow - new jobs", async () => {
  setupMocks();

  const jobBoard = new TheirStackJobBoard(undefined);

  // Step 1: Search for jobs
  const foundJobs = await jobBoard.search(mockCustomerSearchCriteria);
  assertEquals(foundJobs.length, 1);

  // Step 2: Simulate saving jobs (no existing jobs)
  const savedJobs: Job[] = [];

  for (const job of foundJobs) {
    const savedJob = await saveJobIfNotExists(job, mockSupabase as any);
    if (savedJob) {
      savedJobs.push(savedJob);
    }
  }

  assertEquals(savedJobs.length, 1);
  assertEquals(mockSupabase.getJobs().length, 1);

  // Step 3: Check customer jobs (no existing relationships)
  const newJobEvents: SearchJobBoardsFound[] = [];

  for (const job of savedJobs) {
    const exists = await checkCustomerJobExists(mockCustomerSearchCriteria.customer_id, job.id, mockSupabase as any);
    if (!exists) {
      newJobEvents.push({ customerId: mockCustomerSearchCriteria.customer_id, jobId: job.id });
    }
  }

  assertEquals(newJobEvents.length, 1);
  assertEquals(newJobEvents[0].customerId, mockCustomerSearchCriteria.customer_id);
  assertEquals(newJobEvents[0].jobId, "theirstack-1234");

  // Step 4: Publish events
  await mockEvents.publishSearchJobBoardsFound(newJobEvents, "test-topic");
  assertEquals(mockEvents.publishedSearchJobBoardsFound.length, 1);
});

Deno.test("Integration: Full job search and processing flow - duplicate jobs", async () => {
  setupMocks();

  // Setup: Pre-populate with existing job
  const existingJob = { ...mockJob };
  mockSupabase.addJob(existingJob);

  const jobBoard = new TheirStackJobBoard(undefined);

  // Step 1: Search for jobs (will return same job)
  const foundJobs = await jobBoard.search(mockCustomerSearchCriteria);
  assertEquals(foundJobs.length, 1);

  // Step 2: Try to save jobs (should skip duplicates)
  const savedJobs: Job[] = [];

  for (const job of foundJobs) {
    const savedJob = await saveJobIfNotExists(job, mockSupabase as any);
    if (savedJob) {
      savedJobs.push(savedJob);
    }
  }

  // Should have no new saved jobs due to duplicate URL
  assertEquals(savedJobs.length, 0);
  assertEquals(mockSupabase.getJobs().length, 1); // Still only the original job

  // Step 3: No new events should be created
  const newJobEvents: SearchJobBoardsFound[] = [];
  // Since no jobs were saved, no events to create

  assertEquals(newJobEvents.length, 0);

  // Step 4: No events to publish
  await mockEvents.publishSearchJobBoardsFound(newJobEvents, "test-topic");
  assertEquals(mockEvents.publishedSearchJobBoardsFound.length, 0);
});

Deno.test("Integration: Full job search and processing flow - existing customer job relationship", async () => {
  setupMocks();

  const jobBoard = new TheirStackJobBoard(undefined);

  // Step 1: Search and save jobs
  const foundJobs = await jobBoard.search(mockCustomerSearchCriteria);

  const savedJobs: Job[] = [];
  for (const job of foundJobs) {
    const savedJob = await saveJobIfNotExists(job, mockSupabase as any);
    if (savedJob) {
      savedJobs.push(savedJob);
    }
  }

  assertEquals(savedJobs.length, 1);

  // Step 2: Pre-populate customer job relationship
  mockSupabase.addCustomerJob(mockCustomerSearchCriteria.customer_id, savedJobs[0].id);

  // Step 3: Check customer jobs (should find existing relationship)
  const newJobEvents: SearchJobBoardsFound[] = [];

  for (const job of savedJobs) {
    const exists = await checkCustomerJobExists(mockCustomerSearchCriteria.customer_id, job.id, mockSupabase as any);
    if (!exists) {
      newJobEvents.push({ customerId: mockCustomerSearchCriteria.customer_id, jobId: job.id });
    }
  }

  // Should have no new events due to existing relationship
  assertEquals(newJobEvents.length, 0);

  // Step 4: No events to publish
  await mockEvents.publishSearchJobBoardsFound(newJobEvents, "test-topic");
  assertEquals(mockEvents.publishedSearchJobBoardsFound.length, 0);
});

// Edge case tests
Deno.test("Edge case: Multiple jobs with same URL", async () => {
  setupMocks();

  const job1 = { ...mockJob, id: "job-1" };
  const job2 = { ...mockJob, id: "job-2" }; // Same URL as job1

  // First job should save successfully
  const result1 = await saveJobIfNotExists(job1, mockSupabase as any);
  assertExists(result1);
  assertEquals(result1!.id, "job-1");

  // Second job with same URL should be rejected
  const result2 = await saveJobIfNotExists(job2, mockSupabase as any);
  assertEquals(result2, null);

  // Should only have one job saved
  assertEquals(mockSupabase.getJobs().length, 1);
  assertEquals(mockSupabase.getJobs()[0].id, "job-1");
});

Deno.test("Edge case: Empty job search results", async () => {
  setupMocks();

  // No jobs to save or process
  const savedJobs: Job[] = [];
  assertEquals(savedJobs.length, 0);

  // No events should be published
  await mockEvents.publishSearchJobBoardsFound([], "test-topic");
  assertEquals(mockEvents.publishedSearchJobBoardsFound.length, 0);
});

Deno.test("Edge case: Multiple customers for same job", async () => {
  setupMocks();

  const customerId1 = "customer-1";
  const customerId2 = "customer-2";
  const jobId = "job-123";

  // Add relationship for customer 1
  mockSupabase.addCustomerJob(customerId1, jobId);

  // Check customer 1 (should exist)
  const exists1 = await checkCustomerJobExists(customerId1, jobId, mockSupabase as any);
  assertEquals(exists1, true);

  // Check customer 2 (should not exist)
  const exists2 = await checkCustomerJobExists(customerId2, jobId, mockSupabase as any);
  assertEquals(exists2, false);
});

// Performance test with multiple jobs
Deno.test("Performance: Processing multiple jobs", async () => {
  setupMocks();

  // Create multiple mock jobs
  const jobs: Job[] = [];
  for (let i = 1; i <= 5; i++) {
    jobs.push({
      ...mockJob,
      id: `job-${i}`,
      url: `https://example.com/job/${i}`,
      title: `Software Engineer ${i}`
    });
  }

  // Save all jobs
  const savedJobs: Job[] = [];
  for (const job of jobs) {
    const savedJob = await saveJobIfNotExists(job, mockSupabase as any);
    if (savedJob) {
      savedJobs.push(savedJob);
    }
  }

  assertEquals(savedJobs.length, 5);
  assertEquals(mockSupabase.getJobs().length, 5);

  // Check customer jobs for all (none should exist)
  const newJobEvents: SearchJobBoardsFound[] = [];
  for (const job of savedJobs) {
    const exists = await checkCustomerJobExists(mockCustomerSearchCriteria.customer_id, job.id, mockSupabase as any);
    if (!exists) {
      newJobEvents.push({ customerId: mockCustomerSearchCriteria.customer_id, jobId: job.id });
    }
  }

  assertEquals(newJobEvents.length, 5);

  // Publish all events
  await mockEvents.publishSearchJobBoardsFound(newJobEvents, "test-topic");
  assertEquals(mockEvents.publishedSearchJobBoardsFound.length, 5);
});

// Main Handler Tests - Testing exported functions directly
Deno.test("saveJobIfNotExists - handles edge cases gracefully", async () => {
  setupMocks();

  // Create a job with edge case data
  const edgeCaseJob = { ...mockJob, id: "", url: "https://example.com/edge-case" }; // Empty ID

  const result = await saveJobIfNotExists(edgeCaseJob, mockSupabase as any);

  // The function should handle this gracefully - either save it or return null
  // The important thing is that it doesn't crash
  // In this case, our mock allows empty IDs, so it should save successfully
  assertExists(result);
  assertEquals(result!.url, "https://example.com/edge-case");
});

Deno.test("checkCustomerJobExists - handles database errors gracefully", async () => {
  setupMocks();

  // Test with empty parameters
  const exists = await checkCustomerJobExists("", "", mockSupabase as any);

  // Should handle gracefully
  assertEquals(exists, false);
});

Deno.test("saveJobIfNotExists - correctly transforms Job to database format", async () => {
  setupMocks();

  const testJob: Job = {
    id: "test-job-123",
    url: "https://test.com/job/123",
    employer: "Test Company",
    title: "Test Engineer",
    location: "Test City",
    pay_amount: 100000,
    pay_frequency: "Yearly" as any,
    pay_currency: "USD",
    languages: "JavaScript, Python",
    visa_required: true,
    description: "Test job description",
    job_type: "FULLTIME" as any,
    job_status: "OPEN" as any,
    account_required: true,
    customer_apply: true
  };

  const result = await saveJobIfNotExists(testJob, mockSupabase as any);

  assertExists(result);
  assertEquals(result!.id, testJob.id);
  assertEquals(result!.url, testJob.url);
  assertEquals(result!.visa_required, true);
  assertEquals(result!.account_required, true);
  assertEquals(result!.customer_apply, true);

  // Verify it was saved to mock database
  const savedJobs = mockSupabase.getJobs();
  assertEquals(savedJobs.length, 1);
  assertEquals(savedJobs[0].id, testJob.id);
});

// Additional comprehensive tests for search-job-board logic
Deno.test("saveJobIfNotExists - handles special characters in job data", async () => {
  setupMocks();

  const specialJob: Job = {
    ...mockJob,
    id: "job-with-special-chars",
    url: "https://example.com/job/special?param=value&other=test",
    title: "Software Engineer (Remote) - Full Stack",
    description: "Job with special chars: <script>alert('test')</script> & more",
    employer: "Company & Co.",
    languages: "JavaScript, TypeScript, C++, C#"
  };

  const result = await saveJobIfNotExists(specialJob, mockSupabase as any);

  assertExists(result);
  assertEquals(result!.title, "Software Engineer (Remote) - Full Stack");
  assertEquals(result!.employer, "Company & Co.");
});

Deno.test("checkCustomerJobExists - handles multiple customer-job relationships", async () => {
  setupMocks();

  const customerId = "customer-123";
  const jobId1 = "job-456";
  const jobId2 = "job-789";

  // Add multiple relationships for same customer
  mockSupabase.addCustomerJob(customerId, jobId1);
  mockSupabase.addCustomerJob(customerId, jobId2);

  const exists1 = await checkCustomerJobExists(customerId, jobId1, mockSupabase as any);
  const exists2 = await checkCustomerJobExists(customerId, jobId2, mockSupabase as any);
  const exists3 = await checkCustomerJobExists(customerId, "non-existent", mockSupabase as any);

  assertEquals(exists1, true);
  assertEquals(exists2, true);
  assertEquals(exists3, false);
});

Deno.test("Integration: Job processing with error recovery", async () => {
  setupMocks();

  const jobBoard = new TheirStackJobBoard(undefined);
  const foundJobs = await jobBoard.search(mockCustomerSearchCriteria);

  // Simulate processing where some jobs might fail
  const savedJobs: Job[] = [];
  const errors: string[] = [];

  for (const job of foundJobs) {
    try {
      const savedJob = await saveJobIfNotExists(job, mockSupabase as any);
      if (savedJob) {
        savedJobs.push(savedJob);
      }
    } catch (error) {
      errors.push(`Failed to save job ${job.id}: ${error}`);
    }
  }

  // Should continue processing even if some jobs fail
  assertEquals(savedJobs.length, 1);
  assertEquals(errors.length, 0); // No errors expected in this test
});

Deno.test("Integration: Event publishing with batch processing", async () => {
  setupMocks();

  // Create multiple new job events
  const newJobEvents: SearchJobBoardsFound[] = [
    { customerId: "customer-1", jobId: "job-1" },
    { customerId: "customer-1", jobId: "job-2" },
    { customerId: "customer-2", jobId: "job-3" }
  ];

  await mockEvents.publishSearchJobBoardsFound(newJobEvents, "test-topic");

  assertEquals(mockEvents.publishedSearchJobBoardsFound.length, 3);
  assertEquals(mockEvents.publishedSearchJobBoardsFound[0].customerId, "customer-1");
  assertEquals(mockEvents.publishedSearchJobBoardsFound[1].jobId, "job-2");
  assertEquals(mockEvents.publishedSearchJobBoardsFound[2].customerId, "customer-2");
});