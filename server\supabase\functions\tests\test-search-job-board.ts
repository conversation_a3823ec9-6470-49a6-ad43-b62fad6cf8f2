import { assertEquals, assertExists, assertNotEquals } from 'jsr:@std/assert';
import { load } from 'jsr:@std/dotenv';
import { TheirStackJobBoard } from '../search-job-board/job-search-theirstack.ts';
import { CustomerSearchCriteria, Job, SearchJobBoardsFound, supabaseClient, getCustomerSearchCriteria, Customer } from '../shared/common.ts';
import { setEvents, startDeno, saveJobIfNotExists, checkCustomerJobExists, handleSearchJobBoardRequest } from '../search-job-board/index.ts';
import { createCustomer, upsertOnboarding, upsertPlan, post, TestEvents } from './test-common.ts';
import { PRODUCT_CODE } from '../shared/common.ts';
import { SupabaseClient } from 'jsr:@supabase/supabase-js@2';
import { Database } from '../lib/database.types.ts';
import { v4 as uuidv4 } from 'npm:uuid';

// Test data defined inline (no external JSON files)
const testCustomerEmail = "<EMAIL>";
const testJobTitles = ["Software Engineer", "Frontend Developer", "Full Stack Developer"];
const testLocations = ["Remote", "New York", "San Francisco"];
const testSearchDate = "2025-01-15";

// Test job data for various scenarios
const createTestJob = (id: string, url: string, title: string = "Software Engineer", employer: string = "Test Company"): Job => ({
  id,
  url,
  title,
  employer,
  location: "Remote",
  pay_amount: 120000,
  pay_frequency: "Yearly" as any,
  pay_currency: "USD",
  languages: "JavaScript, TypeScript",
  visa_required: false,
  description: "Test job description for automated testing",
  job_type: "FULLTIME" as any,
  job_status: "OPEN" as any,
  account_required: false,
  customer_apply: false
});

// Helper to create realistic job data with UUID
const createRealisticJob = (baseUrl: string, title: string, employer: string): Job => ({
  id: uuidv4(),
  url: baseUrl,
  title,
  employer,
  location: "Remote",
  pay_amount: Math.floor(Math.random() * 100000) + 80000, // Random salary 80k-180k
  pay_frequency: "Yearly" as any,
  pay_currency: "USD",
  languages: "JavaScript, TypeScript, React, Node.js",
  visa_required: Math.random() > 0.7, // 30% chance of visa required
  description: `${title} position at ${employer}. We are looking for a talented developer to join our team.`,
  job_type: "FULLTIME" as any,
  job_status: "OPEN" as any,
  account_required: Math.random() > 0.5, // 50% chance
  customer_apply: Math.random() > 0.5 // 50% chance
});

// Helper functions for test setup and cleanup
async function setupTestCustomer(supabase: SupabaseClient<Database>) {
  // Create minimal customer for testing job search functionality
  // Use a unique email for each test run to avoid conflicts
  const uniqueEmail = `test-search-${Date.now()}@applysquad.com`;
  const customer = await createCustomer(uniqueEmail, supabase);

  // Skip plan and onboarding setup for now - our tests focus on core job search functionality
  // These can be added later when we need to test the full workflow
  // await upsertPlan(customer.id, PRODUCT_CODE.APPS_20, supabase);
  // await upsertOnboarding(customer.id, testJobTitles, testLocations, supabase);

  return customer;
}

async function createTestSearchCriteria(customerId: string, supabase: SupabaseClient<Database>) {
  // First, get or create a job board entry for THEIR_STACK
  let jobBoardId;
  const { data: existingBoard } = await supabase
    .from("job_boards")
    .select("id")
    .eq("key", "THEIR_STACK")
    .single();

  if (existingBoard) {
    jobBoardId = existingBoard.id;
  } else {
    // Create job board if it doesn't exist
    const { data: newBoard, error: boardError } = await supabase
      .from("job_boards")
      .insert({
        key: "THEIR_STACK",
        name: "TheirStack Job Board",
        url: "https://theirstack.com"
      })
      .select("id")
      .single();

    if (boardError) throw boardError;
    jobBoardId = newBoard.id;
  }

  const { data, error } = await supabase
    .from("customer_search_criteria")
    .insert({
      customer_id: customerId,
      job_board_id: jobBoardId,
      job_titles: testJobTitles,
      locations: testLocations,
      search_date: testSearchDate,
      search_status: "NEW"
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}

async function cleanupTestData(supabase: SupabaseClient<Database>, customerId?: string) {
  if (customerId) {
    // Clean up in reverse order due to foreign key constraints
    await supabase.from("customer_jobs").delete().eq("customer_id", customerId);
    await supabase.from("customer_search_criteria").delete().eq("customer_id", customerId);
    await supabase.from("jobs").delete().like("id", "test-%");
    await supabase.from("customers").delete().eq("id", customerId);

    // Clean up auth user
    try {
      await supabase.auth.admin.deleteUser(customerId);
    } catch (error) {
      console.log("Error deleting auth user:", error);
    }
  }
}

// Test 1: TheirStack API Integration with Mock
Deno.test("Unit: TheirStack API mock returns expected job data", async () => {
  await load({ export: true });

  // Create mock search criteria
  const searchCriteria: CustomerSearchCriteria = {
    id: uuidv4(),
    customer_id: uuidv4(),
    job_board_key: "THEIR_STACK",
    job_titles: testJobTitles,
    locations: testLocations,
    search_date: testSearchDate,
    search_status: "NEW"
  };

  // Pass undefined to trigger mock mode in TheirStackJobBoard constructor
  const jobBoard = new TheirStackJobBoard(undefined);
  const jobs = await jobBoard.search(searchCriteria);

  // Verify mock data structure
  assertEquals(jobs.length, 1);
  assertEquals(jobs[0].title, "Software Engineer");
  assertEquals(jobs[0].url, "https://example.com/job/1234");
  assertEquals(jobs[0].employer, "MockCompany");
  assertEquals(jobs[0].id, "theirstack-1234");
  assertEquals(jobs[0].job_type, "FULLTIME");
  assertEquals(jobs[0].job_status, "OPEN");
});

// Test 2: Job Creation and Validation
Deno.test("Unit: Job data structure validation", async () => {
  await load({ export: true });

  const testJob = createTestJob("test-job-123", "https://example.com/test-job", "Senior Developer", "TechCorp");

  // Verify job structure
  assertExists(testJob);
  assertEquals(testJob.id, "test-job-123");
  assertEquals(testJob.url, "https://example.com/test-job");
  assertEquals(testJob.title, "Senior Developer");
  assertEquals(testJob.employer, "TechCorp");
  assertEquals(testJob.job_type, "FULLTIME");
  assertEquals(testJob.job_status, "OPEN");
  assertEquals(testJob.pay_currency, "USD");
  assertEquals(testJob.location, "Remote");
});

// Test 3: Database Job Saving with Real Database
Deno.test("Database: saveJobIfNotExists functionality", async () => {
  await load({ export: true });
  startDeno();
  const supabase = supabaseClient();
  let customer;

  try {
    customer = await setupTestCustomer(supabase);

    // Create a realistic test job with unique URL
    const testJob = createRealisticJob(
      `https://example.com/jobs/senior-developer-${Date.now()}`,
      "Senior Software Developer",
      "TechCorp Inc"
    );

    // Test saving a new job
    const savedJob = await saveJobIfNotExists(testJob, supabase);
    assertExists(savedJob);
    assertEquals(savedJob!.id, testJob.id);
    assertEquals(savedJob!.url, testJob.url);
    assertEquals(savedJob!.title, testJob.title);

    // Verify job was saved to database
    const { data: dbJob, error } = await supabase
      .from("jobs")
      .select("*")
      .eq("id", testJob.id)
      .single();

    assertEquals(error, null);
    assertExists(dbJob);
    assertEquals(dbJob.id, testJob.id);
    assertEquals(dbJob.url, testJob.url);
    assertEquals(dbJob.title, testJob.title);
    assertEquals(dbJob.employer, testJob.employer);

    // Test duplicate URL handling
    const duplicateJob = createRealisticJob(
      testJob.url, // Same URL as the first job
      "Different Title",
      "Different Company"
    );
    const duplicateResult = await saveJobIfNotExists(duplicateJob, supabase);
    assertEquals(duplicateResult, null); // Should return null for duplicate URL

  } finally {
    if (customer) {
      await cleanupTestData(supabase, customer.id);
    }
  }
});

// Test 4: Customer-Job Relationship Checking
Deno.test("Database: checkCustomerJobExists functionality", async () => {
  await load({ export: true });
  startDeno();
  const supabase = supabaseClient();
  let customer;

  try {
    customer = await setupTestCustomer(supabase);

    // Test with non-existent job
    const exists1 = await checkCustomerJobExists(customer.id, uuidv4(), supabase);
    assertEquals(exists1, false);

    // Create a test job and save it
    const testJob = createRealisticJob(
      `https://example.com/jobs/relationship-test-${Date.now()}`,
      "Backend Developer",
      "StartupCorp"
    );
    await saveJobIfNotExists(testJob, supabase);

    // Create customer-job relationship
    const { error } = await supabase
      .from("customer_jobs")
      .insert({
        customer_id: customer.id,
        job_id: testJob.id,
        status: "NEW"
      });

    assertEquals(error, null);

    // Test that relationship exists
    const exists2 = await checkCustomerJobExists(customer.id, testJob.id, supabase);
    assertEquals(exists2, true);

    // Test with different customer (should not exist)
    const otherCustomer = await createCustomer("<EMAIL>", supabase);
    const exists3 = await checkCustomerJobExists(otherCustomer.id, testJob.id, supabase);
    assertEquals(exists3, false);

  } finally {
    if (customer) {
      await cleanupTestData(supabase, customer.id);
    }
  }
});

// Test 5: Integration Test - Full Search Job Board Flow
Deno.test("Integration: Complete search job board workflow", async () => {
  await load({ export: true });
  startDeno();
  const supabase = supabaseClient();
  const testEvents = new TestEvents();
  setEvents(testEvents);
  let customer;

  try {
    // Setup customer with onboarding and plan
    customer = await setupTestCustomer(supabase);

    // Create search criteria
    const searchCriteria = await createTestSearchCriteria(customer.id, supabase);

    // Use TheirStack mock to get jobs
    const jobBoard = new TheirStackJobBoard(undefined); // undefined triggers mock mode
    const foundJobs = await jobBoard.search(searchCriteria);

    assertEquals(foundJobs.length, 1);
    assertEquals(foundJobs[0].id, "theirstack-1234");
    assertEquals(foundJobs[0].title, "Software Engineer");

    // Save jobs to database
    const savedJobs: Job[] = [];
    for (const job of foundJobs) {
      const savedJob = await saveJobIfNotExists(job, supabase);
      if (savedJob) {
        savedJobs.push(savedJob);
      }
    }

    assertEquals(savedJobs.length, 1);

    // Verify job was saved to database
    const { data: dbJobs } = await supabase
      .from("jobs")
      .select("*")
      .eq("id", "theirstack-1234");

    assertExists(dbJobs);
    assertEquals(dbJobs.length, 1);
    assertEquals(dbJobs[0].id, "theirstack-1234");

    // Check customer job relationships (should not exist initially)
    const newJobEvents: SearchJobBoardsFound[] = [];
    for (const job of savedJobs) {
      const exists = await checkCustomerJobExists(customer.id, job.id, supabase);
      if (!exists) {
        newJobEvents.push({ customerId: customer.id, jobId: job.id });
      }
    }

    assertEquals(newJobEvents.length, 1);
    assertEquals(newJobEvents[0].customerId, customer.id);
    assertEquals(newJobEvents[0].jobId, "theirstack-1234");

    // Test event publishing
    await testEvents.publishSearchJobBoardsFound(newJobEvents, "search-job-boards-found");
    assertEquals(testEvents.publishedSearchJobBoardsFound.length, 1);

  } finally {
    if (customer) {
      await cleanupTestData(supabase, customer.id);
    }
  }
});

// Test 6: Multiple Jobs Processing
Deno.test("Integration: Multiple jobs batch processing", async () => {
  await load({ export: true });
  startDeno();
  const supabase = supabaseClient();
  const testEvents = new TestEvents();
  setEvents(testEvents);
  let customer: any;

  try {
    customer = await setupTestCustomer(supabase);

    // Create multiple realistic test jobs
    const testJobs = [
      createRealisticJob("https://example.com/jobs/backend-001", "Backend Engineer", "TechCorp"),
      createRealisticJob("https://example.com/jobs/frontend-002", "Frontend Engineer", "StartupInc"),
      createRealisticJob("https://example.com/jobs/devops-003", "DevOps Engineer", "CloudCorp")
    ];

    // Save all jobs
    const savedJobs: Job[] = [];
    for (const job of testJobs) {
      const savedJob = await saveJobIfNotExists(job, supabase);
      if (savedJob) {
        savedJobs.push(savedJob);
      }
    }

    assertEquals(savedJobs.length, 3);

    // Verify all jobs were saved to database
    const { data: dbJobs } = await supabase
      .from("jobs")
      .select("*")
      .in("id", testJobs.map(j => j.id));

    assertEquals(dbJobs?.length, 3);

    // Check for new job events (none should exist initially)
    const newJobEvents: SearchJobBoardsFound[] = [];
    for (const job of savedJobs) {
      const exists = await checkCustomerJobExists(customer.id, job.id, supabase);
      if (!exists) {
        newJobEvents.push({ customerId: customer.id, jobId: job.id });
      }
    }

    assertEquals(newJobEvents.length, 3); // All should be new

    // Test event publishing
    await testEvents.publishSearchJobBoardsFound(newJobEvents, "search-job-boards-found");
    assertEquals(testEvents.publishedSearchJobBoardsFound.length, 3);

    // Verify event data
    newJobEvents.forEach((event, index) => {
      assertEquals(event.customerId, customer.id);
      assertEquals(event.jobId, testJobs[index].id);
    });

  } finally {
    if (customer) {
      await cleanupTestData(supabase, customer.id);
    }
  }
});

// Test 7: Edge Cases and Special Characters
Deno.test("Edge case: Jobs with special characters and complex data", async () => {
  await load({ export: true });
  startDeno();
  const supabase = supabaseClient();
  let customer: any;

  try {
    customer = await setupTestCustomer(supabase);

    const specialJob: Job = {
      id: uuidv4(),
      url: "https://example.com/job/special?param=value&other=test&unicode=测试",
      title: "Software Engineer (Remote) - Full Stack 🚀",
      description: "Job with special chars: <script>alert('test')</script> & more unicode: 测试 français español",
      employer: "Company & Co. Ltd. 公司",
      location: "New York, NY / San Francisco, CA",
      pay_amount: 150000,
      pay_frequency: "Yearly" as any,
      pay_currency: "USD",
      languages: "JavaScript, TypeScript, C++, C#, Python, Go",
      visa_required: true,
      job_type: "FULLTIME" as any,
      job_status: "OPEN" as any,
      account_required: true,
      customer_apply: false
    };

    const result = await saveJobIfNotExists(specialJob, supabase);

    assertExists(result);
    assertEquals(result!.title, "Software Engineer (Remote) - Full Stack 🚀");
    assertEquals(result!.employer, "Company & Co. Ltd. 公司");
    assertEquals(result!.visa_required, true);
    assertEquals(result!.pay_amount, 150000);

    // Verify in database
    const { data: dbJob } = await supabase
      .from("jobs")
      .select("*")
      .eq("id", specialJob.id)
      .single();

    assertExists(dbJob);
    assertEquals(dbJob.title, specialJob.title);
    assertEquals(dbJob.employer, specialJob.employer);
    assertEquals(dbJob.pay_amount, specialJob.pay_amount);
    assertEquals(dbJob.visa_required, "YES"); // Database stores as string

  } finally {
    if (customer) {
      await cleanupTestData(supabase, customer.id);
    }
  }
});

// Test 8: Error Handling and Edge Cases
Deno.test("Error handling: Invalid job data and database constraints", async () => {
  await load({ export: true });
  startDeno();
  const supabase = supabaseClient();
  let customer: any;

  try {
    customer = await setupTestCustomer(supabase);

    // Test with invalid job data (missing required fields)
    const invalidJob: Partial<Job> = {
      id: uuidv4(),
      url: "https://example.com/invalid-job",
      // Missing required fields like title, employer, etc.
    };

    try {
      await saveJobIfNotExists(invalidJob as Job, supabase);
      // Should not reach here
      assertEquals(true, false, "Should have thrown an error for invalid job data");
    } catch (error) {
      // Expected to fail due to missing required fields
      assertExists(error);
    }

    // Test duplicate job handling
    const job1 = createRealisticJob("https://example.com/duplicate-test", "Test Job", "TestCorp");
    const job2 = createRealisticJob("https://example.com/duplicate-test", "Different Title", "DifferentCorp");

    const result1 = await saveJobIfNotExists(job1, supabase);
    assertExists(result1);

    const result2 = await saveJobIfNotExists(job2, supabase);
    assertEquals(result2, null); // Should be null due to duplicate URL

  } finally {
    if (customer) {
      await cleanupTestData(supabase, customer.id);
    }
  }
});

// Test 9: Search Criteria Integration
Deno.test("Integration: Search criteria creation and job matching", async () => {
  await load({ export: true });
  startDeno();
  const supabase = supabaseClient();
  let customer: any;

  try {
    customer = await setupTestCustomer(supabase);

    // Create search criteria
    const searchCriteria = await createTestSearchCriteria(customer.id, supabase);

    // Verify search criteria was created correctly
    assertExists(searchCriteria);
    assertEquals(searchCriteria.customer_id, customer.id);
    assertEquals(searchCriteria.job_board_key, "THEIR_STACK");
    assertEquals(searchCriteria.job_titles, testJobTitles);
    assertEquals(searchCriteria.locations, testLocations);
    assertEquals(searchCriteria.search_date, testSearchDate);
    assertEquals(searchCriteria.search_status, "NEW");

    // Test retrieving search criteria
    const retrievedCriteria = await getCustomerSearchCriteria(searchCriteria.id, supabase);
    assertEquals(retrievedCriteria.id, searchCriteria.id);
    assertEquals(retrievedCriteria.customer_id, customer.id);

  } finally {
    if (customer) {
      await cleanupTestData(supabase, customer.id);
    }
  }
});

// Test 10: End-to-End Webhook Simulation
Deno.test("Integration: End-to-end webhook event processing", async () => {
  await load({ export: true });
  startDeno();
  const supabase = supabaseClient();
  const testEvents = new TestEvents();
  setEvents(testEvents);
  let customer: any;

  try {
    customer = await setupTestCustomer(supabase);

    // Create search criteria
    const searchCriteria = await createTestSearchCriteria(customer.id, supabase);

    // Simulate webhook payload
    const webhookPayload = JSON.stringify({
      customerSearchCriteriaId: searchCriteria.id
    });

    // Process the webhook (this would normally be called by the webhook handler)
    const response = await post(webhookPayload);
    assertEquals(response, 200);

    // Verify that events were published
    assertNotEquals(testEvents.publishedSearchJobBoardsFound.length, 0);

    // Verify that jobs were found and saved
    const { data: jobs } = await supabase
      .from("jobs")
      .select("*")
      .eq("id", "theirstack-1234"); // Mock job ID

    assertExists(jobs);
    assertEquals(jobs.length, 1);

  } finally {
    if (customer) {
      await cleanupTestData(supabase, customer.id);
    }
  }
});