import { assertEquals, assertExists } from 'jsr:@std/assert';
import { load } from 'jsr:@std/dotenv';
import { TheirStackJobBoard, MockTheirStack } from '../search-job-board/job-search-theirstack.ts';
import { CustomerSearchCriteria, Job, SearchJobBoardsFound, Events, supabaseClient } from '../shared/common.ts';
import { setEvents, startDeno, saveJobIfNotExists, checkCustomerJobExists, JobBoardSearch } from '../search-job-board/index.ts';
import { createCustomer, upsertOnboarding, upsertPlan, post, TestEvents } from './test-common.ts';
import { PRODUCT_CODE } from '../shared/common.ts';
import { SupabaseClient } from 'jsr:@supabase/supabase-js@2';
import { Database } from '../lib/database.types.ts';
import { v4 as uuidv4 } from 'npm:uuid';

// Test data loaded from JSON files
const testSearchCriteriaData = JSON.parse(Deno.readTextFileSync("./supabase/functions/tests/test-search-criteria-data.json"));
const testCustomerData = JSON.parse(Deno.readTextFileSync("./supabase/functions/tests/test-customer-data.json"));
const testJobBoardData = JSON.parse(Deno.readTextFileSync("./supabase/functions/tests/test-job-board-data.json"));
const testEventPayload = Deno.readTextFileSync("./supabase/functions/tests/test-search-job-board-event.json");

// Mock job board that returns predictable test data
class TestJobBoard implements JobBoardSearch {
  private jobs: Job[];

  constructor(jobs: Job[] = []) {
    this.jobs = jobs;
  }

  async search(_criteria: CustomerSearchCriteria): Promise<Job[]> {
    console.log("TestJobBoard: Returning mock jobs for testing");
    return this.jobs;
  }

  setJobs(jobs: Job[]) {
    this.jobs = jobs;
  }
}

// Sample job data for testing - let database auto-generate ID
const createTestJob = (baseId: string, url: string, title: string = "Software Engineer"): Job => {
  // Use a UUID for the job ID to match database expectations
  const jobId = uuidv4();

  // Track this job ID for cleanup
  testJobIds.push(jobId);

  return {
    id: jobId,
    url,
    title,
    employer: "Test Company",
    location: "Remote",
    pay_amount: 120000,
    pay_frequency: "Yearly" as any,
    pay_currency: "USD",
    languages: "JavaScript, TypeScript",
    visa_required: false,
    description: "Test job description for automated testing",
    job_type: "FULLTIME" as any,
    job_status: "OPEN" as any,
    account_required: false,
    customer_apply: false
  };
};

// Keep track of test job IDs for cleanup
const testJobIds: string[] = [];

// Simplified test helpers that work with existing data
async function cleanupTestJobs(supabase: SupabaseClient<Database>) {
  // Only clean up test jobs that we created
  if (testJobIds.length > 0) {
    await supabase.from("customer_jobs").delete().in("job_id", testJobIds);
    await supabase.from("jobs").delete().in("id", testJobIds);
    testJobIds.length = 0; // Clear the array
  }
}

// Get any existing customer for testing (avoid creating new ones)
async function getTestCustomer(supabase: SupabaseClient<Database>): Promise<string> {
  const { data: customers } = await supabase
    .from("customers")
    .select("id")
    .limit(1);

  if (customers && customers.length > 0) {
    return customers[0].id;
  }

  // If no customers exist, create a minimal one for testing using proper UUID
  const testCustomerId = uuidv4();

  // Create auth user first
  await supabase.auth.admin.createUser({
    id: testCustomerId,
    email: `test-${Date.now()}@example.com`,
    email_confirm: true,
    password: 'test-password'
  });

  // Create customer
  const { error } = await supabase
    .from("customers")
    .insert({
      id: testCustomerId,
      email: `test-${Date.now()}@example.com`,
      name: "Test Customer"
    });

  if (error) {
    throw error;
  }

  return testCustomerId;
}

// Database Tests using real Supabase database
Deno.test("Database: saveJobIfNotExists with real database", async () => {
  await load({ export: true });
  const supabase = supabaseClient();

  try {
    const testJob = createTestJob("test-db-001", "https://example.com/test-job-db-001");

    // Test saving a new job
    const savedJob = await saveJobIfNotExists(testJob, supabase);
    assertExists(savedJob);
    assertEquals(savedJob!.id, testJob.id);
    assertEquals(savedJob!.url, testJob.url);

    // Verify job was saved to database
    const { data: dbJob, error } = await supabase
      .from("jobs")
      .select("*")
      .eq("id", testJob.id)
      .single();

    assertEquals(error, null);
    assertExists(dbJob);
    assertEquals(dbJob.id, testJob.id);
    assertEquals(dbJob.url, testJob.url);
    assertEquals(dbJob.title, testJob.title);

    // Test duplicate URL handling
    const duplicateJob = createTestJob("test-db-001-dup", "https://example.com/test-job-db-001");
    const duplicateResult = await saveJobIfNotExists(duplicateJob, supabase);
    assertEquals(duplicateResult, null); // Should return null for duplicate URL

  } finally {
    await cleanupTestJobs(supabase);
  }
});

Deno.test("Unit: MockTheirStack returns expected job data", async () => {
  await load({ export: true });

  // Pass undefined to trigger mock mode in TheirStackJobBoard constructor
  const jobBoard = new TheirStackJobBoard(undefined);
  const searchCriteria: CustomerSearchCriteria = testSearchCriteriaData;

  const jobs = await jobBoard.search(searchCriteria);

  assertEquals(jobs.length, 1);
  assertEquals(jobs[0].title, "Software Engineer");
  assertEquals(jobs[0].url, "https://example.com/job/1234");
  assertEquals(jobs[0].employer, "MockCompany");
  assertEquals(jobs[0].id, "theirstack-1234");
});

Deno.test("Unit: Job creation and validation", async () => {
  await load({ export: true });

  const testJob = createTestJob("test-job-simple", "https://example.com/simple-test");

  // Verify job structure
  assertExists(testJob);
  assertExists(testJob.id); // Should be a UUID
  assertEquals(testJob.url, "https://example.com/simple-test");
  assertEquals(testJob.title, "Software Engineer");
  assertEquals(testJob.employer, "Test Company");
  assertEquals(testJob.job_type, "FULLTIME");
  assertEquals(testJob.job_status, "OPEN");
});

Deno.test("Database: checkCustomerJobExists with real database", async () => {
  await load({ export: true });
  const supabase = supabaseClient();

  try {
    const customerId = await getTestCustomer(supabase);

    // Test with non-existent job
    const exists1 = await checkCustomerJobExists(customerId, "non-existent-job", supabase);
    assertEquals(exists1, false);

    // Create a test job and customer-job relationship
    const testJob = createTestJob("test-rel-001", "https://example.com/test-job-rel-001");
    await saveJobIfNotExists(testJob, supabase);

    // Create customer-job relationship
    const { error } = await supabase
      .from("customer_jobs")
      .insert({
        customer_id: customerId,
        job_id: testJob.id,
        status: "NEW"
      });

    assertEquals(error, null);

    // Test that relationship exists
    const exists2 = await checkCustomerJobExists(customerId, testJob.id, supabase);
    assertEquals(exists2, true);

  } finally {
    await cleanupTestJobs(supabase);
  }
});

Deno.test("Integration: Full job search and processing flow with database", async () => {
  await load({ export: true });
  const supabase = supabaseClient();
  const testEvents = new TestEvents();
  setEvents(testEvents);

  try {
    const customerId = await getTestCustomer(supabase);

    // Step 1: Create test job board with mock data
    const testJob = createTestJob("test-flow-001", "https://example.com/test-job-flow-001", "Full Stack Developer");
    const jobBoard = new TestJobBoard([testJob]);

    // Step 2: Search for jobs
    const foundJobs = await jobBoard.search(testSearchCriteriaData);
    assertEquals(foundJobs.length, 1);
    assertEquals(foundJobs[0].id, testJob.id);

    // Step 3: Save jobs to database
    const savedJobs: Job[] = [];
    for (const job of foundJobs) {
      const savedJob = await saveJobIfNotExists(job, supabase);
      if (savedJob) {
        savedJobs.push(savedJob);
      }
    }

    assertEquals(savedJobs.length, 1);

    // Verify job was saved to database
    const { data: dbJobs } = await supabase
      .from("jobs")
      .select("*")
      .eq("id", testJob.id);

    assertExists(dbJobs);
    assertEquals(dbJobs.length, 1);
    assertEquals(dbJobs[0].id, testJob.id);

    // Step 4: Check customer job relationships (should not exist initially)
    const newJobEvents: SearchJobBoardsFound[] = [];
    for (const job of savedJobs) {
      const exists = await checkCustomerJobExists(customerId, job.id, supabase);
      if (!exists) {
        newJobEvents.push({ customerId: customerId, jobId: job.id });
      }
    }

    assertEquals(newJobEvents.length, 1);
    assertEquals(newJobEvents[0].customerId, customerId);
    assertEquals(newJobEvents[0].jobId, testJob.id);

    // Step 5: Publish events
    await testEvents.publishSearchJobBoardsFound(newJobEvents, "test-topic");
    assertEquals(testEvents.publishedSearchJobBoardsFound.length, 1);

  } finally {
    await cleanupTestJobs(supabase);
  }
});

Deno.test("Integration: Multiple jobs processing with database", async () => {
  await load({ export: true });
  const supabase = supabaseClient();
  const testEvents = new TestEvents();
  setEvents(testEvents);

  try {
    const customerId = await getTestCustomer(supabase);

    // Create multiple test jobs
    const testJobs = [
      createTestJob("test-multi-001", "https://example.com/test-job-multi-001", "Backend Engineer"),
      createTestJob("test-multi-002", "https://example.com/test-job-multi-002", "Frontend Engineer"),
      createTestJob("test-multi-003", "https://example.com/test-job-multi-003", "DevOps Engineer")
    ];

    // Save all jobs
    const savedJobs: Job[] = [];
    for (const job of testJobs) {
      const savedJob = await saveJobIfNotExists(job, supabase);
      if (savedJob) {
        savedJobs.push(savedJob);
      }
    }

    assertEquals(savedJobs.length, 3);

    // Verify all jobs were saved to database
    const { data: dbJobs } = await supabase
      .from("jobs")
      .select("*")
      .in("id", testJobs.map(j => j.id));

    assertEquals(dbJobs?.length, 3);

    // Check for new job events (none should exist initially)
    const newJobEvents: SearchJobBoardsFound[] = [];
    for (const job of savedJobs) {
      const exists = await checkCustomerJobExists(customerId, job.id, supabase);
      if (!exists) {
        newJobEvents.push({ customerId: customerId, jobId: job.id });
      }
    }

    assertEquals(newJobEvents.length, 3); // All should be new

    // Test event publishing
    await testEvents.publishSearchJobBoardsFound(newJobEvents, "test-topic");
    assertEquals(testEvents.publishedSearchJobBoardsFound.length, 3);

  } finally {
    await cleanupTestJobs(supabase);
  }
});

Deno.test("Edge case: Job with special characters and edge data", async () => {
  await load({ export: true });
  const supabase = supabaseClient();

  try {
    const specialJob: Job = {
      id: uuidv4(),
      url: "https://example.com/job/special?param=value&other=test",
      title: "Software Engineer (Remote) - Full Stack",
      description: "Job with special chars: <script>alert('test')</script> & more",
      employer: "Company & Co.",
      location: "New York, NY",
      pay_amount: 150000,
      pay_frequency: "Yearly" as any,
      pay_currency: "USD",
      languages: "JavaScript, TypeScript, C++, C#",
      visa_required: true,
      job_type: "FULLTIME" as any,
      job_status: "OPEN" as any,
      account_required: true,
      customer_apply: false
    };

    const result = await saveJobIfNotExists(specialJob, supabase);

    assertExists(result);
    assertEquals(result!.title, "Software Engineer (Remote) - Full Stack");
    assertEquals(result!.employer, "Company & Co.");
    assertEquals(result!.visa_required, true);

    // Verify in database
    const { data: dbJob } = await supabase
      .from("jobs")
      .select("*")
      .eq("id", specialJob.id)
      .single();

    assertExists(dbJob);
    assertEquals(dbJob.title, specialJob.title);
    assertEquals(dbJob.employer, specialJob.employer);

  } finally {
    await cleanupTestJobs(supabase);
  }
});