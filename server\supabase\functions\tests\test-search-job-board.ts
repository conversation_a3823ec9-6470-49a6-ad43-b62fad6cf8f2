import { assertEquals, assertExists } from 'jsr:@std/assert';
import { load } from 'jsr:@std/dotenv';
import { TheirStackJobBoard, MockTheirStack } from '../search-job-board/job-search-theirstack.ts';
import { CustomerSearchCriteria, Job, SearchJobBoardsFound, Events, supabaseClient } from '../shared/common.ts';
import { setEvents, startDeno, saveJobIfNotExists, checkCustomerJobExists, JobBoardSearch } from '../search-job-board/index.ts';
import { createCustomer, upsertOnboarding, upsertPlan, post, TestEvents } from './test-common.ts';
import { PRODUCT_CODE } from '../shared/common.ts';
import { SupabaseClient } from 'jsr:@supabase/supabase-js@2';
import { Database } from '../lib/database.types.ts';

// Test data loaded from JSON files
const testSearchCriteriaData = JSON.parse(Deno.readTextFileSync("./supabase/functions/tests/test-search-criteria-data.json"));
const testCustomerData = JSON.parse(Deno.readTextFileSync("./supabase/functions/tests/test-customer-data.json"));
const testJobBoardData = JSON.parse(Deno.readTextFileSync("./supabase/functions/tests/test-job-board-data.json"));
const testEventPayload = Deno.readTextFileSync("./supabase/functions/tests/test-search-job-board-event.json");

// Mock job board that returns predictable test data
class TestJobBoard implements JobBoardSearch {
  private jobs: Job[];

  constructor(jobs: Job[] = []) {
    this.jobs = jobs;
  }

  async search(_criteria: CustomerSearchCriteria): Promise<Job[]> {
    console.log("TestJobBoard: Returning mock jobs for testing");
    return this.jobs;
  }

  setJobs(jobs: Job[]) {
    this.jobs = jobs;
  }
}

// Sample job data for testing
const createTestJob = (id: string, url: string, title: string = "Software Engineer"): Job => ({
  id,
  url,
  title,
  employer: "Test Company",
  location: "Remote",
  pay_amount: 120000,
  pay_frequency: "Yearly" as any,
  pay_currency: "USD",
  languages: "JavaScript, TypeScript",
  visa_required: false,
  description: "Test job description for automated testing",
  job_type: "FULLTIME" as any,
  job_status: "OPEN" as any,
  account_required: false,
  customer_apply: false
});

// Helper functions for database operations
async function setupTestData(supabase: SupabaseClient<Database>) {
  // Create test customer and get the actual customer object
  const customer = await createCustomer(testCustomerData.email, supabase, testCustomerData.affiliate_id);

  // Create test job board
  const { error: jobBoardError } = await supabase
    .from("job_boards")
    .upsert(testJobBoardData);

  if (jobBoardError) {
    console.log("Job board already exists or error:", jobBoardError.message);
  }

  // Create test search criteria with the actual customer ID
  const searchCriteriaWithCustomerId = {
    ...testSearchCriteriaData,
    customer_id: customer.id
  };

  const { error: criteriaError } = await supabase
    .from("customer_search_criteria")
    .upsert(searchCriteriaWithCustomerId);

  if (criteriaError) {
    console.log("Search criteria already exists or error:", criteriaError.message);
  }

  return customer;
}

async function cleanupTestData(supabase: SupabaseClient<Database>, customerId: string) {
  // Clean up in reverse order due to foreign key constraints
  await supabase.from("customer_jobs").delete().eq("customer_id", customerId);
  await supabase.from("jobs").delete().like("id", "test-%");
  await supabase.from("customer_search_criteria").delete().eq("customer_id", customerId);
  await supabase.from("customers").delete().eq("id", customerId);

  // Clean up auth user
  try {
    await supabase.auth.admin.deleteUser(customerId);
  } catch (error) {
    console.log("Error deleting auth user:", error);
  }
}

// Integration Tests using real Supabase database
Deno.test("Integration: Search job board with new jobs", async () => {
  await load({ export: true });
  startDeno();

  const supabase = supabaseClient();
  const testEvents = new TestEvents();
  setEvents(testEvents);

  let customer: any;
  try {
    // Setup test data
    customer = await setupTestData(supabase);

    // Create test job board with mock TheirStack
    const testJob = createTestJob("test-job-001", "https://example.com/test-job-001");
    const testJobBoard = new TestJobBoard([testJob]);

    // Simulate the search job board request
    const eventPayload = testEventPayload;

    // Mock Qstash validation to return true for testing
    const originalEnv = Deno.env.get("MATCH_CUSTOMER_JOB");
    Deno.env.set("MATCH_CUSTOMER_JOB", "test-topic");

    // Test job saving
    const savedJob = await saveJobIfNotExists(testJob, supabase);
    assertExists(savedJob);
    assertEquals(savedJob!.id, testJob.id);
    assertEquals(savedJob!.url, testJob.url);

    // Verify job was saved to database
    const { data: dbJob, error } = await supabase
      .from("jobs")
      .select("*")
      .eq("id", testJob.id)
      .single();

    assertEquals(error, null);
    assertExists(dbJob);
    assertEquals(dbJob.id, testJob.id);
    assertEquals(dbJob.url, testJob.url);

    // Test customer job relationship check
    const customerJobExists = await checkCustomerJobExists(customer.id, testJob.id, supabase);
    assertEquals(customerJobExists, false); // Should not exist initially

    // Restore environment
    if (originalEnv) {
      Deno.env.set("MATCH_CUSTOMER_JOB", originalEnv);
    } else {
      Deno.env.delete("MATCH_CUSTOMER_JOB");
    }

  } finally {
    // Cleanup
    if (customer) {
      await cleanupTestData(supabase, customer.id);
    }
  }
});

Deno.test("Unit: MockTheirStack returns expected job data", async () => {
  await load({ export: true });

  // Pass undefined to trigger mock mode in TheirStackJobBoard constructor
  const jobBoard = new TheirStackJobBoard(undefined);
  const searchCriteria: CustomerSearchCriteria = testSearchCriteriaData;

  const jobs = await jobBoard.search(searchCriteria);

  assertEquals(jobs.length, 1);
  assertEquals(jobs[0].title, "Software Engineer");
  assertEquals(jobs[0].url, "https://example.com/job/1234");
  assertEquals(jobs[0].employer, "MockCompany");
  assertEquals(jobs[0].id, "theirstack-1234");
});

Deno.test("Unit: Job creation and validation", async () => {
  await load({ export: true });

  const testJob = createTestJob("test-job-simple", "https://example.com/simple-test");

  // Verify job structure
  assertExists(testJob);
  assertEquals(testJob.id, "test-job-simple");
  assertEquals(testJob.url, "https://example.com/simple-test");
  assertEquals(testJob.title, "Software Engineer");
  assertEquals(testJob.employer, "Test Company");
  assertEquals(testJob.job_type, "FULLTIME");
  assertEquals(testJob.job_status, "OPEN");
});

Deno.test("Unit: saveJobIfNotExists - saves new job successfully", async () => {
  await load({ export: true });
  const supabase = supabaseClient();

  let customer: any;
  try {
    customer = await setupTestData(supabase);

    const testJob = createTestJob("test-job-002", "https://example.com/test-job-002");
    const result = await saveJobIfNotExists(testJob, supabase);

    assertExists(result);
    assertEquals(result!.id, testJob.id);
    assertEquals(result!.url, testJob.url);

    // Verify in database
    const { data: dbJob } = await supabase
      .from("jobs")
      .select("*")
      .eq("id", testJob.id)
      .single();

    assertExists(dbJob);
    assertEquals(dbJob.id, testJob.id);
    assertEquals(dbJob.url, testJob.url);

  } finally {
    if (customer) {
      await cleanupTestData(supabase, customer.id);
    }
  }
});

Deno.test("Unit: saveJobIfNotExists - returns null for duplicate URL", async () => {
  await load({ export: true });
  const supabase = supabaseClient();

  let customer: any;
  try {
    customer = await setupTestData(supabase);

    const testJob = createTestJob("test-job-003", "https://example.com/test-job-003");

    // Save job first time
    const result1 = await saveJobIfNotExists(testJob, supabase);
    assertExists(result1);

    // Try to save same job again (same URL)
    const duplicateJob = createTestJob("test-job-003-duplicate", "https://example.com/test-job-003");
    const result2 = await saveJobIfNotExists(duplicateJob, supabase);

    assertEquals(result2, null); // Should return null for duplicate URL

  } finally {
    if (customer) {
      await cleanupTestData(supabase, customer.id);
    }
  }
});

Deno.test("Unit: checkCustomerJobExists - returns false when no relationship exists", async () => {
  await load({ export: true });
  const supabase = supabaseClient();

  let customer: any;
  try {
    customer = await setupTestData(supabase);

    const exists = await checkCustomerJobExists(customer.id, "non-existent-job", supabase);
    assertEquals(exists, false);

  } finally {
    if (customer) {
      await cleanupTestData(supabase, customer.id);
    }
  }
});

Deno.test("Unit: checkCustomerJobExists - returns true when relationship exists", async () => {
  await load({ export: true });
  const supabase = supabaseClient();

  let customer: any;
  try {
    customer = await setupTestData(supabase);

    const testJob = createTestJob("test-job-004", "https://example.com/test-job-004");
    await saveJobIfNotExists(testJob, supabase);

    // Create customer-job relationship
    const { error } = await supabase
      .from("customer_jobs")
      .insert({
        customer_id: customer.id,
        job_id: testJob.id,
        status: "NEW"
      });

    assertEquals(error, null);

    const exists = await checkCustomerJobExists(customer.id, testJob.id, supabase);
    assertEquals(exists, true);

  } finally {
    if (customer) {
      await cleanupTestData(supabase, customer.id);
    }
  }
});

Deno.test("Integration: Full job search and processing flow with database", async () => {
  await load({ export: true });
  const supabase = supabaseClient();
  const testEvents = new TestEvents();
  setEvents(testEvents);

  let customer: any;
  try {
    customer = await setupTestData(supabase);

    // Step 1: Create test job board with mock data
    const testJob = createTestJob("test-job-005", "https://example.com/test-job-005", "Full Stack Developer");
    const jobBoard = new TestJobBoard([testJob]);

    // Step 2: Search for jobs
    const foundJobs = await jobBoard.search(testSearchCriteriaData);
    assertEquals(foundJobs.length, 1);
    assertEquals(foundJobs[0].id, testJob.id);

    // Step 3: Save jobs to database
    const savedJobs: Job[] = [];
    for (const job of foundJobs) {
      const savedJob = await saveJobIfNotExists(job, supabase);
      if (savedJob) {
        savedJobs.push(savedJob);
      }
    }

    assertEquals(savedJobs.length, 1);

    // Verify job was saved to database
    const { data: dbJobs } = await supabase
      .from("jobs")
      .select("*")
      .eq("id", testJob.id);

    assertExists(dbJobs);
    assertEquals(dbJobs.length, 1);
    assertEquals(dbJobs[0].id, testJob.id);

    // Step 4: Check customer job relationships (should not exist initially)
    const newJobEvents: SearchJobBoardsFound[] = [];
    for (const job of savedJobs) {
      const exists = await checkCustomerJobExists(customer.id, job.id, supabase);
      if (!exists) {
        newJobEvents.push({ customerId: customer.id, jobId: job.id });
      }
    }

    assertEquals(newJobEvents.length, 1);
    assertEquals(newJobEvents[0].customerId, customer.id);
    assertEquals(newJobEvents[0].jobId, testJob.id);

    // Step 5: Publish events
    await testEvents.publishSearchJobBoardsFound(newJobEvents, "test-topic");
    assertEquals(testEvents.publishedSearchJobBoardsFound.length, 1);

  } finally {
    if (customer) {
      await cleanupTestData(supabase, customer.id);
    }
  }
});

Deno.test("Integration: Duplicate job handling with database", async () => {
  await load({ export: true });
  const supabase = supabaseClient();

  let customer: any;
  try {
    customer = await setupTestData(supabase);

    const testJob = createTestJob("test-job-006", "https://example.com/test-job-006");

    // Save job first time
    const result1 = await saveJobIfNotExists(testJob, supabase);
    assertExists(result1);

    // Try to save job with same URL again
    const duplicateJob = createTestJob("test-job-006-dup", "https://example.com/test-job-006");
    const result2 = await saveJobIfNotExists(duplicateJob, supabase);

    assertEquals(result2, null); // Should return null for duplicate URL

    // Verify only one job exists in database
    const { data: dbJobs } = await supabase
      .from("jobs")
      .select("*")
      .eq("url", testJob.url);

    assertExists(dbJobs);
    assertEquals(dbJobs.length, 1);
    assertEquals(dbJobs[0].id, testJob.id); // Should be the original job ID

  } finally {
    if (customer) {
      await cleanupTestData(supabase, customer.id);
    }
  }
});

Deno.test("Integration: Customer job relationship handling", async () => {
  await load({ export: true });
  const supabase = supabaseClient();
  const testEvents = new TestEvents();
  setEvents(testEvents);

  let customer: any;
  try {
    customer = await setupTestData(supabase);

    const testJob = createTestJob("test-job-007", "https://example.com/test-job-007");

    // Save job to database
    const savedJob = await saveJobIfNotExists(testJob, supabase);
    assertExists(savedJob);

    // Create customer-job relationship
    const { error } = await supabase
      .from("customer_jobs")
      .insert({
        customer_id: customer.id,
        job_id: testJob.id,
        status: "NEW"
      });

    assertEquals(error, null);

    // Check that relationship exists
    const exists = await checkCustomerJobExists(customer.id, testJob.id, supabase);
    assertEquals(exists, true);

    // When checking for new job events, should find no new events due to existing relationship
    const newJobEvents: SearchJobBoardsFound[] = [];
    if (!exists) {
      newJobEvents.push({ customerId: customer.id, jobId: testJob.id });
    }

    assertEquals(newJobEvents.length, 0); // No new events due to existing relationship

  } finally {
    if (customer) {
      await cleanupTestData(supabase, customer.id);
    }
  }
});

Deno.test("Edge case: Multiple jobs processing with database", async () => {
  await load({ export: true });
  const supabase = supabaseClient();
  const testEvents = new TestEvents();
  setEvents(testEvents);

  let customer: any;
  try {
    customer = await setupTestData(supabase);

    // Create multiple test jobs
    const testJobs = [
      createTestJob("test-job-008", "https://example.com/test-job-008", "Backend Engineer"),
      createTestJob("test-job-009", "https://example.com/test-job-009", "Frontend Engineer"),
      createTestJob("test-job-010", "https://example.com/test-job-010", "DevOps Engineer")
    ];

    // Save all jobs
    const savedJobs: Job[] = [];
    for (const job of testJobs) {
      const savedJob = await saveJobIfNotExists(job, supabase);
      if (savedJob) {
        savedJobs.push(savedJob);
      }
    }

    assertEquals(savedJobs.length, 3);

    // Verify all jobs were saved to database
    const { data: dbJobs } = await supabase
      .from("jobs")
      .select("*")
      .in("id", testJobs.map(j => j.id));

    assertEquals(dbJobs?.length, 3);

    // Check for new job events (none should exist initially)
    const newJobEvents: SearchJobBoardsFound[] = [];
    for (const job of savedJobs) {
      const exists = await checkCustomerJobExists(customer.id, job.id, supabase);
      if (!exists) {
        newJobEvents.push({ customerId: customer.id, jobId: job.id });
      }
    }

    assertEquals(newJobEvents.length, 3); // All should be new

    // Test event publishing
    await testEvents.publishSearchJobBoardsFound(newJobEvents, "test-topic");
    assertEquals(testEvents.publishedSearchJobBoardsFound.length, 3);

  } finally {
    if (customer) {
      await cleanupTestData(supabase, customer.id);
    }
  }
});

Deno.test("Edge case: Job with special characters and edge data", async () => {
  await load({ export: true });
  const supabase = supabaseClient();

  let customer: any;
  try {
    customer = await setupTestData(supabase);

    const specialJob: Job = {
      id: "test-job-special-011",
      url: "https://example.com/job/special?param=value&other=test",
      title: "Software Engineer (Remote) - Full Stack",
      description: "Job with special chars: <script>alert('test')</script> & more",
      employer: "Company & Co.",
      location: "New York, NY",
      pay_amount: 150000,
      pay_frequency: "Yearly" as any,
      pay_currency: "USD",
      languages: "JavaScript, TypeScript, C++, C#",
      visa_required: true,
      job_type: "FULLTIME" as any,
      job_status: "OPEN" as any,
      account_required: true,
      customer_apply: false
    };

    const result = await saveJobIfNotExists(specialJob, supabase);

    assertExists(result);
    assertEquals(result!.title, "Software Engineer (Remote) - Full Stack");
    assertEquals(result!.employer, "Company & Co.");
    assertEquals(result!.visa_required, true);

    // Verify in database
    const { data: dbJob } = await supabase
      .from("jobs")
      .select("*")
      .eq("id", specialJob.id)
      .single();

    assertExists(dbJob);
    assertEquals(dbJob.title, specialJob.title);
    assertEquals(dbJob.employer, specialJob.employer);

  } finally {
    if (customer) {
      await cleanupTestData(supabase, customer.id);
    }
  }
});