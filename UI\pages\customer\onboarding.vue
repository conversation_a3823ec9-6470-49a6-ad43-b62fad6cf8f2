<template>
  <div class="max-w-7xl mx-auto p-4">
    <div
      v-if="isLoading"
      class="flex flex-col items-center justify-center h-80"
    >
      <UIcon
        name="i-heroicons-arrow-path-20-solid"
        class="w-8 h-8 animate-spin"
      />
      <p class="mt-2 text-sm">Fetching onboarding survey...</p>
    </div>

    <div v-else-if="error" class="text-center py-8">
      <p class="text-red-500">{{ error }}</p>
    </div>
    <div v-else >
      <ULink to="/customer">
        <UButton variant="outline" class="my-3" icon="la:arrow-left">
          Back to dashboard
        </UButton>
      </ULink>
      <UCard class="mx-auto my-7 max-w-7xl bg-white shadow-lg">
        
        <CustomerOnboardingTabs
                    :customer="customer"
                    :is-customer="true"
                  />
        <!-- <CustomerOnboarding
          :customer="customer"
          :is-customer="true"
          :document-deleted="false"
          @document-deleted="() => {}"
          @get-customer-documents="() => {}"
        /> -->
      </UCard>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { Database } from "~/lib/applysquad-common/database.types";
import type { Customer } from "~/common/common";
import { fetchCustomer } from "~/common/customer-service";

definePageMeta({
  middleware: ["auth"],
});

const route = useRoute();
const supabase = useSupabaseClient<Database>();
const user = useSupabaseUser();
const isLoading = ref<boolean>(true);
const error = ref<string | null>(null);
const customer = ref<Customer>(await fetchCustomer(user.value!.id, supabase));

isLoading.value = false;
</script>
