<template>
  <div class="max-w-7xl mx-auto p-4">
    <div
      v-if="isLoading"
      class="flex flex-col items-center justify-center h-80"
    >
      <UIcon
        name="i-heroicons-arrow-path-20-solid"
        class="w-8 h-8 animate-spin"
      />
      <p class="mt-2 text-sm">Fetching job details...</p>
    </div>

    <div v-else-if="error" class="text-center py-8">
      <p class="text-red-500">{{ error }}</p>
    </div>
    <div v-else-if="job">
      <ULink :to="isUserCustomer ? '/customer' : '/agent'">
        <UButton variant="outline" class="my-3" icon="la:arrow-left">
          Back to dashboard
        </UButton>
      </ULink>

      <JobDetail
          v-model:job="job"
          v-model:job-documents="jobDocuments"
          v-model:documents="documents"
          :is-customer="isUserCustomer"
        />

      </div>
  </div>
</template>

<script lang="ts" setup>
import type { Database } from "~/lib/applysquad-common/database.types";
import type {
  JobDocument,
  Document,
  CustomerJob,
} from "~/common/common";
import { ref } from "vue";
import { isCustomer, isAgent } from '../../common/common';
import { fetchCustomerDocuments, fetchJobDocumentsForCustomer, toCustomerJob } from "~/common/customer-service";

definePageMeta({
  middleware: ["auth"],
});

const route = useRoute();
const supabase = useSupabaseClient<Database>();
const user = useSupabaseUser();
const isLoading = ref<boolean>(false);
const error = ref<string | null>(null);
// required interface
const jobId = Number(route.params.id);

const isUserCustomer = ref(false);

const job = ref<CustomerJob>();
const jobDocuments = ref<JobDocument[]>();
const documents = ref<Document[]>();


// Function to check if user has access to this job
async function checkAccess(jobData: CustomerJob): Promise<boolean> {
  if (!user.value) return false;

  if (isCustomer(user.value)) {
    
    isUserCustomer.value = true;
    return jobData.customerId === user.value.id;

  } else if (isAgent(user.value)) {
    
    const { data: agentCustomer } = await supabase
      .from("agent_customers")
      .select("*")
      .eq("customer_id", jobData.customerId)
      .eq("agent_id", user.value.id)
      .single();

    return !!agentCustomer;
  }

  return false;
}

// Load job data
async function loadJob() {
  try {
    isLoading.value = true;
    error.value = null;

    const { data: jobData, error: jobError } = await supabase
      .from("customer_jobs")
      .select("*, job:jobs(*)")
      .eq("id", jobId)
      .single();

    if (jobError) throw jobError;
    if (!jobData) throw new Error("Job not found");

    job.value = toCustomerJob(jobData, jobData.job);

    // Check if user has access to this job
    const hasAccess = await checkAccess(job.value);
    if (!hasAccess) {
      throw new Error("You do not have permission to view this job");
    }

    jobDocuments.value = await fetchJobDocumentsForCustomer(jobData.customer_id, supabase);

    documents.value = await fetchCustomerDocuments(jobData.customer_id, supabase);

  } catch (e: any) {
    error.value = e.message;
  } finally {
    isLoading.value = false;
  }
}




const handleRefreshJob = async () => {
  await loadJob();
};

// Load job data when component mounts
onMounted(() => {
  loadJob();
});
</script>
